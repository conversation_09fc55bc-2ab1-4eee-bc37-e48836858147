package cn.taken.ad.core.pojo.base;

import javax.persistence.*;
import java.util.Date;

/**
 * 错误码管理表
 */
@Entity
@Table(name = "base_error_code")
public class BaseErrorCode implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code")
    private String code;

    @Column(name = "media_adv_id")
    private Long mediaAdvId;

    @Column(name = "type")
    private Integer type;

    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    @Column(name = "is_delete")
    private Integer isDelete;

    @Column(name = "oper_user_id")
    private Long operUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getMediaAdvId() {
        return mediaAdvId;
    }

    public void setMediaAdvId(Long mediaAdvId) {
        this.mediaAdvId = mediaAdvId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Long getOperUserId() {
        return operUserId;
    }

    public void setOperUserId(Long operUserId) {
        this.operUserId = operUserId;
    }
}
