package cn.taken.ad.core.pojo.system;

import javax.persistence.*;
import java.util.Date;

/**
 * 操作日志
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "oper_log")
public class OperLog implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "oper_user_id")
    private Long operUserId;
    @Column(name = "media_user_id")
    private Long mediaUserId;
    @Column(name = "oper_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date operTime;
    @Column(name = "oper_module")
    private String operModule;
    @Column(name = "oper_content")
    private String operContent;
    @Column(name = "oper_ip")
    private String operIp;
    @Column(name = "oper_address")
    private String operAddress;
    @Column(name = "oper_request")
    private String request;
    @Column(name = "oper_response")
    private String response;

    /**
     * 所属系统
     */
    @Column(name = "system_type")
    private String systemType;

    public String getOperAddress() {
        return operAddress;
    }

    public void setOperAddress(String operAddress) {
        this.operAddress = operAddress;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOperUserId() {
        return operUserId;
    }

    public void setOperUserId(Long userId) {
        this.operUserId = userId;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }

    public String getOperModule() {
        return operModule;
    }

    public void setOperModule(String operModule) {
        this.operModule = operModule;
    }

    public String getOperContent() {
        return operContent;
    }

    public void setOperContent(String operContent) {
        this.operContent = operContent;
    }

    public String getOperIp() {
        return operIp;
    }

    public void setOperIp(String operIp) {
        this.operIp = operIp;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    public Long getMediaUserId() {
        return mediaUserId;
    }

    public void setMediaUserId(Long clientUserId) {
        this.mediaUserId = clientUserId;
    }
}
