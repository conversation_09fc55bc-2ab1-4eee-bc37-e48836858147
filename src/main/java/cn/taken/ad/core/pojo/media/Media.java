package cn.taken.ad.core.pojo.media;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "media")
public class Media implements java.io.Serializable {

    private static final long serialVersionUID = -7050540660617524285L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /***
     * 公司名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 媒体CODE
     */
    @Column(name = "code")
    private String code;
    /**
     * 联系人姓名
     */
    @Column(name = "linkman")
    private String linkman;
    /**
     * 公司地址
     */
    @Column(name = "address")
    private String address;
    /**
     * 联系人手机号码
     */
    @Column(name = "linkman_mobile")
    private String linkmanMobile;
    /***
     * AES价格密钥
     */
    @Column(name = "price_key")
    private String priceKey;
    /**
     * 媒体协议ID
     */
    @Column(name = "protocol_id")
    private Long protocolId;
    /**
     * 扩展参数-JSON
     */
    @Column(name = "pny_param")
    private String pnyParam;
    /**
     * 创建时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 创建人
     */
    @Column(name = "operator_id")
    private Long operatorId;

    @Column(name = "settlement_ratio")
    private Double settlementRatio;

    @Column(name = "filter_on_off")
    private Boolean filterOnOff;
    /**
     * 过滤域名
     */
    @Column(name = "filter_url_domain")
    private String filterUrlDomain;

    public Media() {

    }

    public Media(String name, String code, String linkman, String address, String linkmanMobile, String priceKey, long protocolId, String pnyParam, Date createTime, long operatorId, Double settlementRatio) {
        this.name = name;
        this.code = code;
        this.linkman = linkman;
        this.address = address;
        this.linkmanMobile = linkmanMobile;
        this.priceKey = priceKey;
        this.protocolId = protocolId;
        this.pnyParam = pnyParam;
        this.createTime = createTime;
        this.operatorId = operatorId;
        this.settlementRatio = settlementRatio;
    }



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getPnyParam() {
        return pnyParam;
    }

    public void setPnyParam(String pnyParam) {
        this.pnyParam = pnyParam;
    }

    public Long getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(Long protocolId) {
        this.protocolId = protocolId;
    }

    public String getPriceKey() {
        return priceKey;
    }

    public void setPriceKey(String priceKey) {
        this.priceKey = priceKey;
    }

    public String getLinkmanMobile() {
        return linkmanMobile;
    }

    public void setLinkmanMobile(String linkmanMobile) {
        this.linkmanMobile = linkmanMobile;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Double getSettlementRatio() {
        return settlementRatio;
    }

    public void setSettlementRatio(Double settlementRatio) {
        this.settlementRatio = settlementRatio;
    }

    public Boolean getFilterOnOff() {
        return filterOnOff;
    }

    public void setFilterOnOff(Boolean filterOnOff) {
        this.filterOnOff = filterOnOff;
    }

    public String getFilterUrlDomain() {
        return filterUrlDomain;
    }

    public void setFilterUrlDomain(String filterUrlDomain) {
        this.filterUrlDomain = filterUrlDomain;
    }
}
