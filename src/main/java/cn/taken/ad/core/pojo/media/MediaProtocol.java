package cn.taken.ad.core.pojo.media;

import javax.persistence.*;

@Entity
@Table(name = "media_protocol")
public class MediaProtocol implements java.io.Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /***
     * 协议名称
     */
    @Column(name = "name")
    private String name;
    /***
     * 协议代码
     */
    @Column(name = "code")
    private String code;
    /**
     * 媒体级参数-JSON
     */
    @Column(name = "param")
    private String param;
    /***
     * 媒体APP级参数-JSON
     */
    @Column(name = "app_param")
    private String appParam;
    /***
     * 广告位级参数-JSON
     */
    @Column(name = "tag_param")
    private String tagParam;
    /***
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "type")
    private Integer type;

    @Column(name = "is_need_app_code")
    private Boolean isNeedAppCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getAppParam() {
        return appParam;
    }

    public void setAppParam(String appParam) {
        this.appParam = appParam;
    }

    public String getTagParam() {
        return tagParam;
    }

    public void setTagParam(String tagParam) {
        this.tagParam = tagParam;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getIsNeedAppCode() {
        return isNeedAppCode;
    }

    public void setIsNeedAppCode(Boolean needAppCode) {
        this.isNeedAppCode = needAppCode;
    }
}
