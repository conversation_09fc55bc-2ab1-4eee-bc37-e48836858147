package cn.taken.ad.core.pojo.statistics;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "statistics_dsp_ad_event")
public class StatisticsDspAdEvent implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "statistics_type")
    private String statisticsType;

    @Column(name = "statistics_time")
    private String statisticsTime;

    @Column(name = "ad_id")
    private String adId;

    @Column(name = "event_type")
    private Integer eventType;

    @Column(name = "total")
    private Long total;

    @Column(name = "repeat_total")
    private Long repeatTotal;


    public StatisticsDspAdEvent() {

    }

    public StatisticsDspAdEvent(String statisticsType, String statisticsTime, String adId, Integer eventType) {
        this.statisticsType = statisticsType;
        this.statisticsTime = statisticsTime;
        this.adId = adId;
        this.eventType = eventType;
        this.total = 0L;
        this.repeatTotal = 0L;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatisticsType() {
        return statisticsType;
    }

    public void setStatisticsType(String statisticsType) {
        this.statisticsType = statisticsType;
    }

    public String getStatisticsTime() {
        return statisticsTime;
    }

    public void setStatisticsTime(String statisticsTime) {
        this.statisticsTime = statisticsTime;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public String getAdId() {
        return adId;
    }

    public void setAdId(String adId) {
        this.adId = adId;
    }

    public Long getRepeatTotal() {
        return repeatTotal;
    }

    public void setRepeatTotal(Long repeatTotal) {
        this.repeatTotal = repeatTotal;
    }
}
