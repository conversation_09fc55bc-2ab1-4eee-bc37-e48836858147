package cn.taken.ad.core.service.advertiser;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.core.dto.web.collab.CollabUserDataAuthDto;
import cn.taken.ad.core.dto.web.oper.advertiser.main.*;
import cn.taken.ad.core.pojo.advertiser.Advertiser;

import java.util.Date;
import java.util.List;

public interface AdvertiserService {

    Page<AdvertiserInfo> findPage(AdvertiserPageReq req);

    List<Advertiser> findList(AdvertiserListReq req);

    SuperResult<String> add(AdvertiserAddReq req, Long operatorId);

    SuperResult<String> modify(AdvertiserModifyReq req, long operatorId);

    List<Advertiser> findByLastUpdateTime(Date lastUpdateTime, int start, int limit);

    AdvertiserInfo findInfoById(Long id);

    List<Advertiser> findInfoByIds(Long[] advertiserIds);

    List<Advertiser> findCollabList(AdvertiserListReq req, CollabUserDataAuthDto collabUserDataAuthDto);
}
