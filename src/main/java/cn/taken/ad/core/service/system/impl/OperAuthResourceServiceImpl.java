package cn.taken.ad.core.service.system.impl;

import cn.taken.ad.core.dao.system.OperAuthResourceDao;
import cn.taken.ad.core.pojo.system.OperAuthResource;
import cn.taken.ad.core.service.system.OperAuthResourceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class OperAuthResourceServiceImpl implements OperAuthResourceService {

    @Resource
    private OperAuthResourceDao operAuthResourceDao;

    @Override
    public List<OperAuthResource> getUserResources(Long userId) {
        return operAuthResourceDao.getUserResources(userId);
    }

}
