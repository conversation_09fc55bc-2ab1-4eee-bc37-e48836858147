package cn.taken.ad.core.service.advertiser.impl;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.core.dao.advertiser.AdvertiserDao;
import cn.taken.ad.core.dao.advertiser.AdvertiserProtocolDao;
import cn.taken.ad.core.dto.web.collab.CollabUserDataAuthDto;
import cn.taken.ad.core.dto.web.oper.advertiser.main.*;
import cn.taken.ad.core.pojo.advertiser.Advertiser;
import cn.taken.ad.core.pojo.advertiser.AdvertiserProtocol;
import cn.taken.ad.core.service.advertiser.AdvertiserService;
import cn.taken.ad.utils.check.ParamChecker;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class AdvertiserServiceImpl implements AdvertiserService {

    @Resource
    private AdvertiserDao advertiserDao;
    @Resource
    private AdvertiserProtocolDao advertiserProtocolDao;

    @Override
    public Page<AdvertiserInfo> findPage(AdvertiserPageReq req) {
        return advertiserDao.findPage(req);
    }

    @Override
    public List<Advertiser> findList(AdvertiserListReq req) {
        return advertiserDao.findList(req);
    }

    @Override
    public SuperResult<String> add(AdvertiserAddReq req, Long operatorId) {
        AdvertiserProtocol protocol = advertiserProtocolDao.findById(req.getProtocolId());
        SuperResult<String> checkResult = ParamChecker.checkParams(protocol.getParam(), req.getPnyParam());
        if (!checkResult.getSuccess()) {
            return checkResult;
        }
        Advertiser advertiser = advertiserDao.findByName(req.getName());
        if (advertiser != null) {
            return SuperResult.badResult("名称重复");
        }
        Advertiser newAdvertiser = new Advertiser();
        newAdvertiser.setName(req.getName());
        newAdvertiser.setRtbUrl(req.getRtbUrl());
        newAdvertiser.setAddress(req.getAddress());
        newAdvertiser.setLinkman(req.getLinkman());
        newAdvertiser.setLinkmanMobile(req.getLinkmanMobile());
        newAdvertiser.setPnyParam(req.getPnyParam());
        newAdvertiser.setProtocolId(req.getProtocolId());
        newAdvertiser.setOperatorId(operatorId);
        newAdvertiser.setCreateTime(new Date());
        newAdvertiser.setPlatformUrl(req.getPlatformUrl());
        newAdvertiser.setAccount(req.getAccount());
        newAdvertiser.setPassword(req.getPassword());
        advertiserDao.save(newAdvertiser);
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<String> modify(AdvertiserModifyReq req, long operatorId) {
        Advertiser advertiser = advertiserDao.findById(req.getId());
        if (advertiser == null) {
            return SuperResult.badResult("未找到预算");
        }
        AdvertiserProtocol protocol = advertiserProtocolDao.findById(advertiser.getProtocolId());
        SuperResult<String> checkResult = ParamChecker.checkParams(protocol.getParam(), req.getPnyParam());
        if (!checkResult.getSuccess()) {
            return checkResult;
        }
        Advertiser advertiserByName = advertiserDao.findByName(req.getName());
        if (advertiserByName != null && !advertiserByName.getId().equals(advertiser.getId())) {
            return SuperResult.badResult("名称重复");
        }
        advertiser.setName(req.getName());
        advertiser.setRtbUrl(req.getRtbUrl());
        advertiser.setAddress(req.getAddress());
        advertiser.setLinkman(req.getLinkman());
        advertiser.setLinkmanMobile(req.getLinkmanMobile());
        advertiser.setPnyParam(req.getPnyParam());
        advertiser.setPlatformUrl(req.getPlatformUrl());
        advertiser.setAccount(req.getAccount());
        advertiser.setPassword(req.getPassword());
        advertiserDao.update(advertiser);
        return SuperResult.rightResult();
    }

    @Override
    public List<Advertiser> findByLastUpdateTime(Date lastUpdateTime, int start, int limit) {
        return advertiserDao.findByLastUpdateTime(lastUpdateTime, start, limit);
    }

    @Override
    public AdvertiserInfo findInfoById(Long id) {
        return advertiserDao.findInfoById(id);
    }

    @Override
    public List<Advertiser> findInfoByIds(Long[] advertiserIds) {
        return advertiserDao.findInfoByIds(advertiserIds);
    }

    @Override
    public List<Advertiser> findCollabList(AdvertiserListReq req, CollabUserDataAuthDto collabUserDataAuthDto) {
        return advertiserDao.findCollabList(req, collabUserDataAuthDto);
    }

}
