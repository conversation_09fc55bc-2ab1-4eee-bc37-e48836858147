package cn.taken.ad.core.dao.system;

import cn.taken.ad.component.orm.BaseSuperDao;
import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.core.dto.web.oper.tool.rtb.log.RtbLogInfo;
import cn.taken.ad.core.dto.web.oper.tool.rtb.log.RtbLogPageReq;
import cn.taken.ad.core.pojo.system.RtbLog;

import java.util.Date;

public interface RtbLogDao extends BaseSuperDao<RtbLog> {

    Page<RtbLogInfo> findPage(RtbLogPageReq req);

    public void deleteByTime(Date date);

}
