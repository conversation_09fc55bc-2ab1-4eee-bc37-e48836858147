package cn.taken.ad.core.dto.web.oper.statistics.media;

import cn.taken.ad.core.dto.global.PageReq;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class StatisticsMediaRequestPageReq extends PageReq {

    @NotNull(message = "维度未选择")
    private String statisticsType;
    private String beginTime;
    private String endTime;
    private Long mediaTagId;
    private Long mediaId;
    private Long mediaAppId;
    private Integer bidType;

    public String getStatisticsType() {
        return statisticsType;
    }

    public void setStatisticsType(String statisticsType) {
        this.statisticsType = statisticsType;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Integer getBidType() {
        return bidType;
    }

    public void setBidType(Integer bidType) {
        this.bidType = bidType;
    }
}
