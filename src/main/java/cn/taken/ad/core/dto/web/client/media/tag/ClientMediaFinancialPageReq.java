package cn.taken.ad.core.dto.web.client.media.tag;

import cn.taken.ad.core.dto.global.PageReq;

import javax.validation.Valid;

@Valid
public class ClientMediaFinancialPageReq extends PageReq {

    private String beginTime;

    private String endTime;

    private Long mediaAppId;

    private Long mediaTagId;

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }
}
