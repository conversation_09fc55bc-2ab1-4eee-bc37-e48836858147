package cn.taken.ad.core.dto.web.global;

import java.math.BigDecimal;

public class TimeStatic {

    private String statisticsTime;

    private Long mediaReqTotal = 0L;
    private Long mediaParticipatingTotal = 0L;
    private Long mediaWinTotal = 0L;
    private BigDecimal mediaAmount = BigDecimal.ZERO;

    private Long advertiserReqTotal = 0L;
    private Long advertiserParticipatingTotal = 0L;
    private Long advertiserWinTotal = 0L;
    private BigDecimal advertiserAmount = BigDecimal.ZERO;

    private Long eventExposureTotal = 0L;
    private Long eventClickTotal = 0L;

    public TimeStatic() {
    }

    public TimeStatic(String statisticsTime) {
        this.statisticsTime = statisticsTime;
    }

    public String getStatisticsTime() {
        return statisticsTime;
    }

    public void setStatisticsTime(String statisticsTime) {
        this.statisticsTime = statisticsTime;
    }

    public Long getMediaReqTotal() {
        return mediaReqTotal;
    }

    public void setMediaReqTotal(Long mediaReqTotal) {
        this.mediaReqTotal = mediaReqTotal;
    }

    public Long getMediaParticipatingTotal() {
        return mediaParticipatingTotal;
    }

    public void setMediaParticipatingTotal(Long mediaParticipatingTotal) {
        this.mediaParticipatingTotal = mediaParticipatingTotal;
    }

    public Long getMediaWinTotal() {
        return mediaWinTotal;
    }

    public void setMediaWinTotal(Long mediaWinTotal) {
        this.mediaWinTotal = mediaWinTotal;
    }

    public BigDecimal getMediaAmount() {
        return mediaAmount;
    }

    public void setMediaAmount(BigDecimal mediaAmount) {
        this.mediaAmount = mediaAmount;
    }

    public Long getAdvertiserReqTotal() {
        return advertiserReqTotal;
    }

    public void setAdvertiserReqTotal(Long advertiserReqTotal) {
        this.advertiserReqTotal = advertiserReqTotal;
    }

    public Long getAdvertiserParticipatingTotal() {
        return advertiserParticipatingTotal;
    }

    public void setAdvertiserParticipatingTotal(Long advertiserParticipatingTotal) {
        this.advertiserParticipatingTotal = advertiserParticipatingTotal;
    }

    public Long getAdvertiserWinTotal() {
        return advertiserWinTotal;
    }

    public void setAdvertiserWinTotal(Long advertiserWinTotal) {
        this.advertiserWinTotal = advertiserWinTotal;
    }

    public BigDecimal getAdvertiserAmount() {
        return advertiserAmount;
    }

    public void setAdvertiserAmount(BigDecimal advertiserAmount) {
        this.advertiserAmount = advertiserAmount;
    }

    public Long getEventExposureTotal() {
        return eventExposureTotal;
    }

    public void setEventExposureTotal(Long eventExposureTotal) {
        this.eventExposureTotal = eventExposureTotal;
    }

    public Long getEventClickTotal() {
        return eventClickTotal;
    }

    public void setEventClickTotal(Long eventClickTotal) {
        this.eventClickTotal = eventClickTotal;
    }
}
