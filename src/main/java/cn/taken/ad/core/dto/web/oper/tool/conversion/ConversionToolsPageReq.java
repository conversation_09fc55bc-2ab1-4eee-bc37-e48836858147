package cn.taken.ad.core.dto.web.oper.tool.conversion;

import cn.taken.ad.core.dto.global.PageReq;

public class ConversionToolsPageReq extends PageReq {

    private String name;
    private String url;
    private String convertUrl;
    private Integer type;
    private Integer status;
    private String desc;

    private String beginTime;
    private String endTime;

    private String iosConvertUrl;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getConvertUrl() {
        return convertUrl;
    }

    public void setConvertUrl(String convertUrl) {
        this.convertUrl = convertUrl;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getIosConvertUrl() {
        return iosConvertUrl;
    }

    public void setIosConvertUrl(String iosConvertUrl) {
        this.iosConvertUrl = iosConvertUrl;
    }
}
