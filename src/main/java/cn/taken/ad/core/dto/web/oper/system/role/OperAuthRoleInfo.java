package cn.taken.ad.core.dto.web.oper.system.role;

import cn.taken.ad.core.pojo.system.OperAuthRole;

import java.util.Date;

public class OperAuthRoleInfo {

    private Long id;
    private String[] resourceCodes;
    private String roleName;
    private Date createTime;
    private Long operatorId;
    private String remark;

    public OperAuthRoleInfo() {

    }

    public OperAuthRoleInfo(OperAuthRole role, String[] resourceCodes) {
        this.id = role.getId();
        this.roleName = role.getRoleName();
        this.createTime = role.getCreateTime();
        this.operatorId = role.getOperatorId();
        this.remark = role.getRemark();
        this.resourceCodes = resourceCodes;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String[] getResourceCodes() {
        return resourceCodes;
    }

    public void setResourceCodes(String[] resourceCodes) {
        this.resourceCodes = resourceCodes;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
