package cn.taken.ad.core.dto.web.oper.financial.merge;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Valid
public class FinancialMediaAdvModifyReq {

    @NotNull(message = "记录未选择")
    private Long id;

    @NotNull(message = "媒体填充量未填写")
    private Long mediaRealParticipatingTotal;

    @NotNull(message = "媒体曝光量未填写")
    private Long mediaRealExposureTotal;

    @NotNull(message = "媒体点击量未填写")
    private Long mediaRealClickTotal;

    @NotNull(message = "媒体消耗金额未填写")
    private BigDecimal mediaRealAmount;

    @NotNull(message = "预算填充量未填写")
    private Long advertiserRealParticipatingTotal;

    @NotNull(message = "预算曝光量未填写")
    private Long advertiserRealExposureTotal;

    @NotNull(message = "预算点击量未填写")
    private Long advertiserRealClickTotal;

    @NotNull(message = "预算金额未填写")
    private BigDecimal advertiserRealAmount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMediaRealParticipatingTotal() {
        return mediaRealParticipatingTotal;
    }

    public void setMediaRealParticipatingTotal(Long mediaRealParticipatingTotal) {
        this.mediaRealParticipatingTotal = mediaRealParticipatingTotal;
    }

    public Long getMediaRealExposureTotal() {
        return mediaRealExposureTotal;
    }

    public void setMediaRealExposureTotal(Long mediaRealExposureTotal) {
        this.mediaRealExposureTotal = mediaRealExposureTotal;
    }

    public Long getMediaRealClickTotal() {
        return mediaRealClickTotal;
    }

    public void setMediaRealClickTotal(Long mediaRealClickTotal) {
        this.mediaRealClickTotal = mediaRealClickTotal;
    }

    public BigDecimal getMediaRealAmount() {
        return mediaRealAmount;
    }

    public void setMediaRealAmount(BigDecimal mediaRealAmount) {
        this.mediaRealAmount = mediaRealAmount;
    }

    public Long getAdvertiserRealExposureTotal() {
        return advertiserRealExposureTotal;
    }

    public void setAdvertiserRealExposureTotal(Long advertiserRealExposureTotal) {
        this.advertiserRealExposureTotal = advertiserRealExposureTotal;
    }

    public Long getAdvertiserRealClickTotal() {
        return advertiserRealClickTotal;
    }

    public void setAdvertiserRealClickTotal(Long advertiserRealClickTotal) {
        this.advertiserRealClickTotal = advertiserRealClickTotal;
    }

    public BigDecimal getAdvertiserRealAmount() {
        return advertiserRealAmount;
    }

    public void setAdvertiserRealAmount(BigDecimal advertiserRealAmount) {
        this.advertiserRealAmount = advertiserRealAmount;
    }

    public Long getAdvertiserRealParticipatingTotal() {
        return advertiserRealParticipatingTotal;
    }

    public void setAdvertiserRealParticipatingTotal(Long advertiserRealParticipatingTotal) {
        this.advertiserRealParticipatingTotal = advertiserRealParticipatingTotal;
    }
}
