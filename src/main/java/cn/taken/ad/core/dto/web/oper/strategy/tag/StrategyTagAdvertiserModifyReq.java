package cn.taken.ad.core.dto.web.oper.strategy.tag;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class StrategyTagAdvertiserModifyReq {

    @NotNull(message = "未找到数据")
    private Long id;
    @NotNull(message = "未选择包名处理方式")
    private Integer handleType;
    private Integer randomQps;
    private Integer parallelPriority;
    private Integer randomFlowRatio;
    private Double advTagFixedPrice;

    /**
     * 竞价跑分成 底价处理
     * 1: 不传底价
     * 2: 透传底价
     */
    private Integer rtbToSharingBasePriceType;
    /**
     * 竞价跑分成 出价方式
     * @see cn.taken.ad.constant.business.BidPriceType
     */
    private Integer rtbToSharingBidPriceType;
    /**
     * 涨幅比例
     */
    private Integer rtbToSharingBidRisesRatio;
    /**
     * 最高曝光出价
     */
    private Double rtbToSharingMaxPrice;
    /**
     * 固价
     */
    private Double rtbToSharingFixedPrice;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getHandleType() {
        return handleType;
    }

    public void setHandleType(Integer handleType) {
        this.handleType = handleType;
    }

    public Integer getRandomQps() {
        return randomQps;
    }

    public void setRandomQps(Integer randomQps) {
        this.randomQps = randomQps;
    }

    public Integer getParallelPriority() {
        return parallelPriority;
    }

    public void setParallelPriority(Integer parallelPriority) {
        this.parallelPriority = parallelPriority;
    }

    public Integer getRandomFlowRatio() {
        return randomFlowRatio;
    }

    public void setRandomFlowRatio(Integer randomFlowRatio) {
        this.randomFlowRatio = randomFlowRatio;
    }

    public Double getAdvTagFixedPrice() {
        return advTagFixedPrice;
    }

    public void setAdvTagFixedPrice(Double advTagFixedPrice) {
        this.advTagFixedPrice = advTagFixedPrice;
    }

    public Integer getRtbToSharingBasePriceType() {
        return rtbToSharingBasePriceType;
    }

    public void setRtbToSharingBasePriceType(Integer rtbToSharingBasePriceType) {
        this.rtbToSharingBasePriceType = rtbToSharingBasePriceType;
    }

    public Integer getRtbToSharingBidPriceType() {
        return rtbToSharingBidPriceType;
    }

    public void setRtbToSharingBidPriceType(Integer rtbToSharingBidPriceType) {
        this.rtbToSharingBidPriceType = rtbToSharingBidPriceType;
    }

    public Integer getRtbToSharingBidRisesRatio() {
        return rtbToSharingBidRisesRatio;
    }

    public void setRtbToSharingBidRisesRatio(Integer rtbToSharingBidRisesRatio) {
        this.rtbToSharingBidRisesRatio = rtbToSharingBidRisesRatio;
    }

    public Double getRtbToSharingFixedPrice() {
        return rtbToSharingFixedPrice;
    }

    public void setRtbToSharingFixedPrice(Double rtbToSharingFixedPrice) {
        this.rtbToSharingFixedPrice = rtbToSharingFixedPrice;
    }

    public Double getRtbToSharingMaxPrice() {
        return rtbToSharingMaxPrice;
    }

    public void setRtbToSharingMaxPrice(Double rtbToSharingMaxPrice) {
        this.rtbToSharingMaxPrice = rtbToSharingMaxPrice;
    }
}
