package cn.taken.ad.core.dto.web.oper.advertiser.app;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class AdvertiserAppAddReq {

    @NotNull(message="预算不能为空")
    private Long advertiserId;
    @NotNull(message="APP 名称不能为空")
    private String name;
    @NotNull(message="APP CODE不能为空")
    private String code;
    @NotNull(message="类型不能为空")
    private Integer type;
    @NotNull(message="包名不能为空")
    private String packageName;
    @NotNull(message="所属行业不能为空")
    private Long firstIndustryId;
    private String pnyParam;
    @NotNull(message="二级行业不能为空")
    private Long secondIndustryId;

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getPnyParam() {
        return pnyParam;
    }

    public void setPnyParam(String pnyParam) {
        this.pnyParam = pnyParam;
    }

    public Long getFirstIndustryId() {
        return firstIndustryId;
    }

    public void setFirstIndustryId(Long firstIndustryId) {
        this.firstIndustryId = firstIndustryId;
    }

    public Long getSecondIndustryId() {
        return secondIndustryId;
    }

    public void setSecondIndustryId(Long secondIndustryId) {
        this.secondIndustryId = secondIndustryId;
    }
}
