package cn.taken.ad.core.dto.web.oper.statistics.merge;

import cn.taken.ad.component.excel.common.schema.annotation.ExcelColumn;
import cn.taken.ad.component.excel.common.schema.annotation.ExcelSheet;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.component.utils.number.BigDecimalUtils;
import cn.taken.ad.constant.business.OsType;
import cn.taken.ad.constant.business.TagType;
import cn.taken.ad.constant.state.StatisticsType;

import java.math.BigDecimal;
import java.util.Date;

@ExcelSheet(isAutoWidth = true)
public class StatisticsMediaAdvertiserRequestInfo {

    private Long id;
    private Long strategyId;
    private Long strategyTagAdvId;
    private Long mediaId;
    private Long mediaAppId;
    private Long mediaTagId;
    private Long mediaUseTimeTotal;
    private Long advertiserId;
    private Long advertiserAppId;
    private Long advertiserTagId;
    private Long advertiserUseTimeTotal;
    private Long mediaReqInvalidTotal;
    private Long mediaRespFailTotal;
    private Long mediaMaxTime;
    private Long mediaAvgTime;
    private Long mediaMinTime;
    private Long advertiserReqSuccessTotal;
    private Long advertiserReqFailTotal;
    private Long advertiserRespFailTotal;
    private Long advertiserReqTimeoutTotal;
    private Long advertiserMaxTime;
    private Long advertiserAvgTime;
    private Long advertiserMinTime;
    private Integer bidType;
    private Integer settlementType;

    @ExcelColumn(index = 0, title = "维度")
    private String statisticsType;
    @ExcelColumn(index = 1, title = "时间")
    private String statisticsTime;

    @ExcelColumn(index = 2, title = "媒体名称")
    private String mediaName;
    @ExcelColumn(index = 3, title = "媒体CODE")
    private String mediaCode;
    @ExcelColumn(index = 4, title = "媒体APP名称")
    private String mediaAppName;
    @ExcelColumn(index = 5, title = "媒体APP CODE")
    private String mediaAppCode;
    private Integer mediaAppType;
    @ExcelColumn(index = 6, title = "媒体APP系统")
    private String mediaAppTypeName;
    @ExcelColumn(index = 7, title = "媒体广告位名称")
    private String mediaTagName;
    @ExcelColumn(index = 8, title = "媒体广告位CODE")
    private String mediaTagCode;
    private Integer mediaTagType;
    @ExcelColumn(index = 9, title = "媒体广告位类型")
    private String mediaTagTypeName;
    @ExcelColumn(index = 10, title = "媒体结算类型")
    private String bidTypeName;

    @ExcelColumn(index = 11, title = "预算名称")
    private String advertiserName;
    @ExcelColumn(index = 12, title = "预算APP名称")
    private String advertiserAppName;
    @ExcelColumn(index = 13, title = "预算APP CODE")
    private String advertiserAppCode;
    private Integer advertiserAppType;
    @ExcelColumn(index = 14, title = "预算APP系统")
    private String advertiserAppTypeName;
    @ExcelColumn(index = 15, title = "预算广告位名称")
    private String advertiserTagName;
    @ExcelColumn(index = 16, title = "预算广告位CODE")
    private String advertiserTagCode;
    private Integer advertiserTagType;
    @ExcelColumn(index = 17, title = "预算广告位类型")
    private String advertiserTagTypeName;
    @ExcelColumn(index = 18, title = "预算结算类型")
    private String settlementTypeName;

    @ExcelColumn(index = 19, title = "媒体请求")
    private Long mediaReqTotal;
    @ExcelColumn(index = 20, title = "媒体填充")
    private Long mediaParticipatingTotal;
    @ExcelColumn(index = 21, title = "媒体填充率(%)")
    private BigDecimal mediaParticipatingRate;
    @ExcelColumn(index = 22, title = "媒体竟胜")
    private Long mediaWinTotal;
    @ExcelColumn(index = 23, title = "媒体竟胜率(%)")
    private BigDecimal mediaWinRate;
    @ExcelColumn(index = 24, title = "媒体消费(元)")
    private BigDecimal mediaAmount;

    @ExcelColumn(index = 25, title = "预算请求")
    private Long advertiserReqTotal;
    @ExcelColumn(index = 26, title = "预算填充")
    private Long advertiserParticipatingTotal;
    @ExcelColumn(index = 27, title = "预算填充率(%)")
    private BigDecimal advertiserParticipatingRate;
    @ExcelColumn(index = 28, title = "预算竟胜")
    private Long advertiserWinTotal;
    @ExcelColumn(index = 29, title = "预算竟胜率(%)")
    private BigDecimal advertiserWinRate;
    @ExcelColumn(index = 30, title = "预算收入(元)")
    private BigDecimal advertiserAmount;

    @ExcelColumn(index = 31, title = "曝光")
    private Long event_1;
    @ExcelColumn(index = 32, title = "曝光率(%)")
    private BigDecimal event_1Rate;
    @ExcelColumn(index = 33, title = "点击")
    private Long event_2;
    @ExcelColumn(index = 34, title = "点击率(%)")
    private BigDecimal event_2Rate;
    @ExcelColumn(index = 35, title = "下载开始")
    private Long event_3;
    @ExcelColumn(index = 36, title = "下载完成")
    private Long event_4;
    @ExcelColumn(index = 37, title = "安装开始")
    private Long event_5;
    @ExcelColumn(index = 38, title = "安装完成")
    private Long event_6;
    @ExcelColumn(index = 39, title = "安装完成后打开")
    private Long event_7;
    @ExcelColumn(index = 40, title = "激活")
    private Long event_8;
    @ExcelColumn(index = 41, title = "广告被关闭")
    private Long event_9;
    @ExcelColumn(index = 42, title = "deeplink打开失败")
    private Long event_10;
    @ExcelColumn(index = 43, title = "deeplink成功打开")
    private Long event_11;
    @ExcelColumn(index = 44, title = "尝试调起deeplink")
    private Long event_12;
    @ExcelColumn(index = 45, title = "APP未安装")
    private Long event_13;
    @ExcelColumn(index = 46, title = "APP已安装")
    private Long event_14;
    @ExcelColumn(index = 47, title = "视频开始播放")
    private Long event_15;
    @ExcelColumn(index = 48, title = "视频开始播放至25%")
    private Long event_16;
    @ExcelColumn(index = 49, title = "视频开始播放至50%")
    private Long event_17;
    @ExcelColumn(index = 50, title = "视频开始播放至75%")
    private Long event_18;
    @ExcelColumn(index = 51, title = "视频播放结束")
    private Long event_19;
    @ExcelColumn(index = 52, title = "视频跳过")
    private Long event_20;
    @ExcelColumn(index = 53, title = "视频关闭")
    private Long event_21;
    @ExcelColumn(index = 54, title = "视频全屏播放")
    private Long event_22;
    @ExcelColumn(index = 55, title = "视频退出全屏播放")
    private Long event_23;
    @ExcelColumn(index = 56, title = "视频加载成功")
    private Long event_24;
    @ExcelColumn(index = 57, title = "视频加载失败")
    private Long event_25;
    @ExcelColumn(index = 58, title = "视频静音")
    private Long event_26;
    @ExcelColumn(index = 59, title = "视频取消静音")
    private Long event_27;
    @ExcelColumn(index = 60, title = "暂停播放")
    private Long event_28;
    @ExcelColumn(index = 61, title = "继续播放")
    private Long event_29;
    @ExcelColumn(index = 62, title = "播放错误")
    private Long event_30;
    @ExcelColumn(index = 63, title = "视频重播")
    private Long event_31;
    @ExcelColumn(index = 64, title = "视频上滑事件")
    private Long event_32;
    @ExcelColumn(index = 65, title = "视频下滑事件")
    private Long event_33;
    @ExcelColumn(index = 66, title = "视频播放完成展示出后贴片内容")
    private Long event_34;
    @ExcelColumn(index = 67, title = "视频播放中点击")
    private Long event_35;
    @ExcelColumn(index = 68, title = "小程序调起成功")
    private Long event_36;
    @ExcelColumn(index = 69, title = "小程序调起失败")
    private Long event_37;
    @ExcelColumn(index = 70, title = "媒体ECPM")
    private BigDecimal mediaEcPm;
    @ExcelColumn(index = 71, title = "预算ECPM")
    private BigDecimal advEcPm;
    @ExcelColumn(index = 72, title = "媒体CPC")
    private BigDecimal mediaCpc;
    @ExcelColumn(index = 73, title = "预算ECPC")
    private BigDecimal advCpc;

    public void fillValues() {
        StatisticsType type = StatisticsType.getByCode(statisticsType);
        if (type != null) {
            this.setStatisticsType(type.getName());
            Date date = DateUtils.parseDate(this.getStatisticsTime(), type.getFormat());
            String dateStr = DateUtils.toString(date, type.getShowFormat());
            this.setStatisticsTime(dateStr);
        }
        OsType mediaOsType = OsType.findByType(this.getMediaAppType());
        if (mediaOsType != null) {
            this.setMediaAppTypeName(mediaOsType.getName());
        }
        TagType mediaTagType = TagType.findByType(this.getMediaTagType());
        if (mediaTagType != null) {
            this.setMediaTagTypeName(mediaTagType.getDesc());
        }

        this.setMediaAmount(null == this.getMediaAmount() ? BigDecimal.ZERO : BigDecimalUtils.div(this.getMediaAmount(), BigDecimal.valueOf(100), 2));
        if (this.getMediaParticipatingTotal() != null && this.getMediaReqTotal() != null && this.getMediaReqTotal() > 0) {
            this.setMediaParticipatingRate(BigDecimalUtils.div(BigDecimal.valueOf(100 * this.getMediaParticipatingTotal()), BigDecimal.valueOf(this.getMediaReqTotal()), 2));
        } else {
            this.setMediaParticipatingRate(BigDecimal.ZERO);
        }

        if (this.getMediaWinTotal() != null && this.getMediaParticipatingTotal() != null && this.getMediaParticipatingTotal() > 0) {
            this.setMediaWinRate(BigDecimalUtils.div(BigDecimal.valueOf(100 * this.getMediaWinTotal()), BigDecimal.valueOf(this.getMediaParticipatingTotal()), 2));
        } else {
            this.setMediaWinRate(BigDecimal.ZERO);
        }


        OsType adOsType = OsType.findByType(this.getAdvertiserAppType());
        if (adOsType != null) {
            this.setAdvertiserAppTypeName(adOsType.getName());
        }
        TagType adTagType = TagType.findByType(this.getAdvertiserTagType());
        if (adTagType != null) {
            this.setAdvertiserTagTypeName(adTagType.getDesc());
        }
        this.setAdvertiserAmount(null == this.getAdvertiserAmount() ? BigDecimal.ZERO : BigDecimalUtils.div(this.getAdvertiserAmount(), BigDecimal.valueOf(100), 2));
        if (this.getAdvertiserParticipatingTotal() != null && this.getAdvertiserReqTotal() != null && this.getAdvertiserReqTotal() > 0) {
            this.setAdvertiserParticipatingRate(BigDecimalUtils.div(BigDecimal.valueOf(100 * this.getAdvertiserParticipatingTotal()), BigDecimal.valueOf(this.getAdvertiserReqTotal()), 2));
        } else {
            this.setAdvertiserParticipatingRate(BigDecimal.ZERO);
        }

        if (this.getAdvertiserWinTotal() != null && this.getAdvertiserParticipatingTotal() != null && this.getAdvertiserParticipatingTotal() > 0) {
            this.setAdvertiserWinRate(BigDecimalUtils.div(BigDecimal.valueOf(100 * this.getAdvertiserWinTotal()), BigDecimal.valueOf(this.getAdvertiserParticipatingTotal()), 2));
        } else {
            this.setAdvertiserWinRate(BigDecimal.ZERO);
        }

        if (this.getEvent_1() != null && this.getMediaWinTotal() != null && this.getMediaWinTotal() > 0) {
            this.setEvent_1Rate(BigDecimalUtils.div(BigDecimal.valueOf(100L * this.getEvent_1()), BigDecimal.valueOf(this.getMediaWinTotal()), 2));
        } else if (this.getEvent_1() != null && this.getMediaParticipatingTotal() != null && this.getMediaParticipatingTotal() > 0) {
            this.setEvent_1Rate(BigDecimalUtils.div(BigDecimal.valueOf(100L * this.getEvent_1()), BigDecimal.valueOf(this.getMediaParticipatingTotal()), 2));
        } else {
            this.setEvent_1Rate(BigDecimal.ZERO);
        }

        if (this.getEvent_2() != null && this.getEvent_1() != null && this.getEvent_1() > 0) {
            this.setEvent_2Rate(BigDecimalUtils.div(BigDecimal.valueOf(100L * this.getEvent_2()), BigDecimal.valueOf(this.getEvent_1()), 2));
        } else {
            this.setEvent_2Rate(BigDecimal.ZERO);
        }

        if(this.getAdvertiserAmount()!=null&&this.getEvent_1()!=null&&this.getEvent_1() > 0){
            this.setAdvEcPm(BigDecimalUtils.div(this.getAdvertiserAmount().multiply(new BigDecimal(1000L)), BigDecimal.valueOf(this.getEvent_1()), 4));
        }else{
            this.setAdvEcPm(BigDecimal.ZERO);
        }

        if(this.getMediaAmount()!=null&&this.getEvent_1()!=null&&this.getEvent_1() > 0){
            this.setMediaEcPm(BigDecimalUtils.div(this.getMediaAmount().multiply(new BigDecimal(1000L)), BigDecimal.valueOf(this.getEvent_1()), 2));
        }else{
            this.setMediaEcPm(BigDecimal.ZERO);
        }

        if (this.bidType != null) {
            this.bidTypeName = this.bidType == 1 ? "RTB" : "分成";
        } else {
            this.bidTypeName = "未知";
        }

        if (this.settlementType != null) {
            this.settlementTypeName = this.settlementType == 1 ? "RTB" : "分成";
        } else {
            this.settlementTypeName = "未知";
        }
        //cpc
        if (this.getAdvertiserAmount() != null && this.getEvent_2() != null && this.getEvent_2() > 0) {
            this.advCpc = BigDecimalUtils.div(this.getAdvertiserAmount(), BigDecimal.valueOf(this.getEvent_2()), 2);
        } else {
            this.advCpc = BigDecimal.ZERO;
        }
        if (this.getMediaAmount() != null && this.getEvent_2() != null && this.getEvent_2() > 0) {
            this.mediaCpc = BigDecimalUtils.div(this.getMediaAmount(), BigDecimal.valueOf(this.getEvent_2()), 2);
        } else {
            this.mediaCpc = BigDecimal.ZERO;
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    public Long getStrategyTagAdvId() {
        return strategyTagAdvId;
    }

    public void setStrategyTagAdvId(Long strategyTagAdvId) {
        this.strategyTagAdvId = strategyTagAdvId;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Long getMediaUseTimeTotal() {
        return mediaUseTimeTotal;
    }

    public void setMediaUseTimeTotal(Long mediaUseTimeTotal) {
        this.mediaUseTimeTotal = mediaUseTimeTotal;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public Long getAdvertiserTagId() {
        return advertiserTagId;
    }

    public void setAdvertiserTagId(Long advertiserTagId) {
        this.advertiserTagId = advertiserTagId;
    }

    public Long getAdvertiserUseTimeTotal() {
        return advertiserUseTimeTotal;
    }

    public void setAdvertiserUseTimeTotal(Long advertiserUseTimeTotal) {
        this.advertiserUseTimeTotal = advertiserUseTimeTotal;
    }

    public Long getMediaReqInvalidTotal() {
        return mediaReqInvalidTotal;
    }

    public void setMediaReqInvalidTotal(Long mediaReqInvalidTotal) {
        this.mediaReqInvalidTotal = mediaReqInvalidTotal;
    }

    public Long getMediaRespFailTotal() {
        return mediaRespFailTotal;
    }

    public void setMediaRespFailTotal(Long mediaRespFailTotal) {
        this.mediaRespFailTotal = mediaRespFailTotal;
    }

    public Long getMediaMaxTime() {
        return mediaMaxTime;
    }

    public void setMediaMaxTime(Long mediaMaxTime) {
        this.mediaMaxTime = mediaMaxTime;
    }

    public Long getMediaAvgTime() {
        return mediaAvgTime;
    }

    public void setMediaAvgTime(Long mediaAvgTime) {
        this.mediaAvgTime = mediaAvgTime;
    }

    public Long getMediaMinTime() {
        return mediaMinTime;
    }

    public void setMediaMinTime(Long mediaMinTime) {
        this.mediaMinTime = mediaMinTime;
    }

    public Long getAdvertiserReqSuccessTotal() {
        return advertiserReqSuccessTotal;
    }

    public void setAdvertiserReqSuccessTotal(Long advertiserReqSuccessTotal) {
        this.advertiserReqSuccessTotal = advertiserReqSuccessTotal;
    }

    public Long getAdvertiserReqFailTotal() {
        return advertiserReqFailTotal;
    }

    public void setAdvertiserReqFailTotal(Long advertiserReqFailTotal) {
        this.advertiserReqFailTotal = advertiserReqFailTotal;
    }

    public Long getAdvertiserRespFailTotal() {
        return advertiserRespFailTotal;
    }

    public void setAdvertiserRespFailTotal(Long advertiserRespFailTotal) {
        this.advertiserRespFailTotal = advertiserRespFailTotal;
    }

    public Long getAdvertiserReqTimeoutTotal() {
        return advertiserReqTimeoutTotal;
    }

    public void setAdvertiserReqTimeoutTotal(Long advertiserReqTimeoutTotal) {
        this.advertiserReqTimeoutTotal = advertiserReqTimeoutTotal;
    }

    public Long getAdvertiserMaxTime() {
        return advertiserMaxTime;
    }

    public void setAdvertiserMaxTime(Long advertiserMaxTime) {
        this.advertiserMaxTime = advertiserMaxTime;
    }

    public Long getAdvertiserAvgTime() {
        return advertiserAvgTime;
    }

    public void setAdvertiserAvgTime(Long advertiserAvgTime) {
        this.advertiserAvgTime = advertiserAvgTime;
    }

    public Long getAdvertiserMinTime() {
        return advertiserMinTime;
    }

    public void setAdvertiserMinTime(Long advertiserMinTime) {
        this.advertiserMinTime = advertiserMinTime;
    }

    public String getStatisticsType() {
        return statisticsType;
    }

    public void setStatisticsType(String statisticsType) {
        this.statisticsType = statisticsType;
    }

    public String getStatisticsTime() {
        return statisticsTime;
    }

    public void setStatisticsTime(String statisticsTime) {
        this.statisticsTime = statisticsTime;
    }

    public String getMediaName() {
        return mediaName;
    }

    public void setMediaName(String mediaName) {
        this.mediaName = mediaName;
    }

    public String getMediaCode() {
        return mediaCode;
    }

    public void setMediaCode(String mediaCode) {
        this.mediaCode = mediaCode;
    }

    public String getMediaAppName() {
        return mediaAppName;
    }

    public void setMediaAppName(String mediaAppName) {
        this.mediaAppName = mediaAppName;
    }

    public String getMediaAppCode() {
        return mediaAppCode;
    }

    public void setMediaAppCode(String mediaAppCode) {
        this.mediaAppCode = mediaAppCode;
    }

    public Integer getMediaAppType() {
        return mediaAppType;
    }

    public void setMediaAppType(Integer mediaAppType) {
        this.mediaAppType = mediaAppType;
    }

    public String getMediaAppTypeName() {
        return mediaAppTypeName;
    }

    public void setMediaAppTypeName(String mediaAppTypeName) {
        this.mediaAppTypeName = mediaAppTypeName;
    }

    public String getMediaTagName() {
        return mediaTagName;
    }

    public void setMediaTagName(String mediaTagName) {
        this.mediaTagName = mediaTagName;
    }

    public String getMediaTagCode() {
        return mediaTagCode;
    }

    public void setMediaTagCode(String mediaTagCode) {
        this.mediaTagCode = mediaTagCode;
    }

    public Integer getMediaTagType() {
        return mediaTagType;
    }

    public void setMediaTagType(Integer mediaTagType) {
        this.mediaTagType = mediaTagType;
    }

    public String getMediaTagTypeName() {
        return mediaTagTypeName;
    }

    public void setMediaTagTypeName(String mediaTagTypeName) {
        this.mediaTagTypeName = mediaTagTypeName;
    }

    public String getAdvertiserName() {
        return advertiserName;
    }

    public void setAdvertiserName(String advertiserName) {
        this.advertiserName = advertiserName;
    }

    public String getAdvertiserAppName() {
        return advertiserAppName;
    }

    public void setAdvertiserAppName(String advertiserAppName) {
        this.advertiserAppName = advertiserAppName;
    }

    public String getAdvertiserAppCode() {
        return advertiserAppCode;
    }

    public void setAdvertiserAppCode(String advertiserAppCode) {
        this.advertiserAppCode = advertiserAppCode;
    }

    public Integer getAdvertiserAppType() {
        return advertiserAppType;
    }

    public void setAdvertiserAppType(Integer advertiserAppType) {
        this.advertiserAppType = advertiserAppType;
    }

    public String getAdvertiserAppTypeName() {
        return advertiserAppTypeName;
    }

    public void setAdvertiserAppTypeName(String advertiserAppTypeName) {
        this.advertiserAppTypeName = advertiserAppTypeName;
    }

    public String getAdvertiserTagName() {
        return advertiserTagName;
    }

    public void setAdvertiserTagName(String advertiserTagName) {
        this.advertiserTagName = advertiserTagName;
    }

    public String getAdvertiserTagCode() {
        return advertiserTagCode;
    }

    public void setAdvertiserTagCode(String advertiserTagCode) {
        this.advertiserTagCode = advertiserTagCode;
    }

    public Integer getAdvertiserTagType() {
        return advertiserTagType;
    }

    public void setAdvertiserTagType(Integer advertiserTagType) {
        this.advertiserTagType = advertiserTagType;
    }

    public String getAdvertiserTagTypeName() {
        return advertiserTagTypeName;
    }

    public void setAdvertiserTagTypeName(String advertiserTagTypeName) {
        this.advertiserTagTypeName = advertiserTagTypeName;
    }

    public Long getMediaReqTotal() {
        return mediaReqTotal;
    }

    public void setMediaReqTotal(Long mediaReqTotal) {
        this.mediaReqTotal = mediaReqTotal;
    }

    public Long getMediaParticipatingTotal() {
        return mediaParticipatingTotal;
    }

    public void setMediaParticipatingTotal(Long mediaParticipatingTotal) {
        this.mediaParticipatingTotal = mediaParticipatingTotal;
    }

    public BigDecimal getMediaParticipatingRate() {
        return mediaParticipatingRate;
    }

    public void setMediaParticipatingRate(BigDecimal mediaParticipatingRate) {
        this.mediaParticipatingRate = mediaParticipatingRate;
    }

    public Long getMediaWinTotal() {
        return mediaWinTotal;
    }

    public void setMediaWinTotal(Long mediaWinTotal) {
        this.mediaWinTotal = mediaWinTotal;
    }

    public BigDecimal getMediaWinRate() {
        return mediaWinRate;
    }

    public void setMediaWinRate(BigDecimal mediaWinRate) {
        this.mediaWinRate = mediaWinRate;
    }

    public BigDecimal getMediaAmount() {
        return mediaAmount;
    }

    public void setMediaAmount(BigDecimal mediaAmount) {
        this.mediaAmount = mediaAmount;
    }

    public Long getAdvertiserReqTotal() {
        return advertiserReqTotal;
    }

    public void setAdvertiserReqTotal(Long advertiserReqTotal) {
        this.advertiserReqTotal = advertiserReqTotal;
    }

    public Long getAdvertiserParticipatingTotal() {
        return advertiserParticipatingTotal;
    }

    public void setAdvertiserParticipatingTotal(Long advertiserParticipatingTotal) {
        this.advertiserParticipatingTotal = advertiserParticipatingTotal;
    }

    public BigDecimal getAdvertiserParticipatingRate() {
        return advertiserParticipatingRate;
    }

    public void setAdvertiserParticipatingRate(BigDecimal advertiserParticipatingRate) {
        this.advertiserParticipatingRate = advertiserParticipatingRate;
    }

    public Long getAdvertiserWinTotal() {
        return advertiserWinTotal;
    }

    public void setAdvertiserWinTotal(Long advertiserWinTotal) {
        this.advertiserWinTotal = advertiserWinTotal;
    }

    public BigDecimal getAdvertiserWinRate() {
        return advertiserWinRate;
    }

    public void setAdvertiserWinRate(BigDecimal advertiserWinRate) {
        this.advertiserWinRate = advertiserWinRate;
    }

    public BigDecimal getAdvertiserAmount() {
        return advertiserAmount;
    }

    public void setAdvertiserAmount(BigDecimal advertiserAmount) {
        this.advertiserAmount = advertiserAmount;
    }

    public Long getEvent_1() {
        return event_1;
    }

    public void setEvent_1(Long event_1) {
        this.event_1 = event_1;
    }

    public BigDecimal getEvent_1Rate() {
        return event_1Rate;
    }

    public void setEvent_1Rate(BigDecimal event_1Rate) {
        this.event_1Rate = event_1Rate;
    }

    public Long getEvent_2() {
        return event_2;
    }

    public void setEvent_2(Long event_2) {
        this.event_2 = event_2;
    }

    public BigDecimal getEvent_2Rate() {
        return event_2Rate;
    }

    public void setEvent_2Rate(BigDecimal event_2Rate) {
        this.event_2Rate = event_2Rate;
    }

    public Long getEvent_3() {
        return event_3;
    }

    public void setEvent_3(Long event_3) {
        this.event_3 = event_3;
    }

    public Long getEvent_4() {
        return event_4;
    }

    public void setEvent_4(Long event_4) {
        this.event_4 = event_4;
    }

    public Long getEvent_5() {
        return event_5;
    }

    public void setEvent_5(Long event_5) {
        this.event_5 = event_5;
    }

    public Long getEvent_6() {
        return event_6;
    }

    public void setEvent_6(Long event_6) {
        this.event_6 = event_6;
    }

    public Long getEvent_7() {
        return event_7;
    }

    public void setEvent_7(Long event_7) {
        this.event_7 = event_7;
    }

    public Long getEvent_8() {
        return event_8;
    }

    public void setEvent_8(Long event_8) {
        this.event_8 = event_8;
    }

    public Long getEvent_9() {
        return event_9;
    }

    public void setEvent_9(Long event_9) {
        this.event_9 = event_9;
    }

    public Long getEvent_10() {
        return event_10;
    }

    public void setEvent_10(Long event_10) {
        this.event_10 = event_10;
    }

    public Long getEvent_11() {
        return event_11;
    }

    public void setEvent_11(Long event_11) {
        this.event_11 = event_11;
    }

    public Long getEvent_12() {
        return event_12;
    }

    public void setEvent_12(Long event_12) {
        this.event_12 = event_12;
    }

    public Long getEvent_13() {
        return event_13;
    }

    public void setEvent_13(Long event_13) {
        this.event_13 = event_13;
    }

    public Long getEvent_14() {
        return event_14;
    }

    public void setEvent_14(Long event_14) {
        this.event_14 = event_14;
    }

    public Long getEvent_15() {
        return event_15;
    }

    public void setEvent_15(Long event_15) {
        this.event_15 = event_15;
    }

    public Long getEvent_16() {
        return event_16;
    }

    public void setEvent_16(Long event_16) {
        this.event_16 = event_16;
    }

    public Long getEvent_17() {
        return event_17;
    }

    public void setEvent_17(Long event_17) {
        this.event_17 = event_17;
    }

    public Long getEvent_18() {
        return event_18;
    }

    public void setEvent_18(Long event_18) {
        this.event_18 = event_18;
    }

    public Long getEvent_19() {
        return event_19;
    }

    public void setEvent_19(Long event_19) {
        this.event_19 = event_19;
    }

    public Long getEvent_20() {
        return event_20;
    }

    public void setEvent_20(Long event_20) {
        this.event_20 = event_20;
    }

    public Long getEvent_21() {
        return event_21;
    }

    public void setEvent_21(Long event_21) {
        this.event_21 = event_21;
    }

    public Long getEvent_22() {
        return event_22;
    }

    public void setEvent_22(Long event_22) {
        this.event_22 = event_22;
    }

    public Long getEvent_23() {
        return event_23;
    }

    public void setEvent_23(Long event_23) {
        this.event_23 = event_23;
    }

    public Long getEvent_24() {
        return event_24;
    }

    public void setEvent_24(Long event_24) {
        this.event_24 = event_24;
    }

    public Long getEvent_25() {
        return event_25;
    }

    public void setEvent_25(Long event_25) {
        this.event_25 = event_25;
    }

    public Long getEvent_26() {
        return event_26;
    }

    public void setEvent_26(Long event_26) {
        this.event_26 = event_26;
    }

    public Long getEvent_27() {
        return event_27;
    }

    public void setEvent_27(Long event_27) {
        this.event_27 = event_27;
    }

    public Long getEvent_28() {
        return event_28;
    }

    public void setEvent_28(Long event_28) {
        this.event_28 = event_28;
    }

    public Long getEvent_29() {
        return event_29;
    }

    public void setEvent_29(Long event_29) {
        this.event_29 = event_29;
    }

    public Long getEvent_30() {
        return event_30;
    }

    public void setEvent_30(Long event_30) {
        this.event_30 = event_30;
    }

    public Long getEvent_31() {
        return event_31;
    }

    public void setEvent_31(Long event_31) {
        this.event_31 = event_31;
    }

    public Long getEvent_32() {
        return event_32;
    }

    public void setEvent_32(Long event_32) {
        this.event_32 = event_32;
    }

    public Long getEvent_33() {
        return event_33;
    }

    public void setEvent_33(Long event_33) {
        this.event_33 = event_33;
    }

    public Long getEvent_34() {
        return event_34;
    }

    public void setEvent_34(Long event_34) {
        this.event_34 = event_34;
    }

    public Long getEvent_35() {
        return event_35;
    }

    public void setEvent_35(Long event_35) {
        this.event_35 = event_35;
    }

    public Long getEvent_36() {
        return event_36;
    }

    public void setEvent_36(Long event_36) {
        this.event_36 = event_36;
    }

    public Long getEvent_37() {
        return event_37;
    }

    public void setEvent_37(Long event_37) {
        this.event_37 = event_37;
    }

    public BigDecimal getMediaEcPm() {
        return mediaEcPm;
    }

    public void setMediaEcPm(BigDecimal mediaEcPm) {
        this.mediaEcPm = mediaEcPm;
    }

    public BigDecimal getAdvEcPm() {
        return advEcPm;
    }

    public void setAdvEcPm(BigDecimal advEcPm) {
        this.advEcPm = advEcPm;
    }

    public Integer getBidType() {
        return bidType;
    }

    public void setBidType(Integer bidType) {
        this.bidType = bidType;
    }

    public Integer getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(Integer settlementType) {
        this.settlementType = settlementType;
    }

    public String getBidTypeName() {
        return bidTypeName;
    }

    public void setBidTypeName(String bidTypeName) {
        this.bidTypeName = bidTypeName;
    }

    public String getSettlementTypeName() {
        return settlementTypeName;
    }

    public void setSettlementTypeName(String settlementTypeName) {
        this.settlementTypeName = settlementTypeName;
    }

    public BigDecimal getMediaCpc() {
        return mediaCpc;
    }

    public void setMediaCpc(BigDecimal mediaCpc) {
        this.mediaCpc = mediaCpc;
    }

    public BigDecimal getAdvCpc() {
        return advCpc;
    }

    public void setAdvCpc(BigDecimal advCpc) {
        this.advCpc = advCpc;
    }
}
