package cn.taken.ad.api.oper.base;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.constant.web.WebAuth;
import cn.taken.ad.core.dto.web.oper.base.industry.*;
import cn.taken.ad.core.pojo.base.BaseAppIndustry;
import cn.taken.ad.core.service.base.BaseAppIndustryService;
import cn.taken.ad.core.service.system.OperLogService;
import cn.taken.ad.utils.web.OperWebUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * app行业
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/o/base/app/industry")
public class BaseAppIndustryApi {

    @Resource
    private BaseAppIndustryService baseAppIndustryService;
    @Resource
    private OperLogService operLogService;

    /**
     * 列表
     */
    @WebAuth("BASE_APP_INDUSTRY_VIEW")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public SuperResult<Page<BaseAppIndustryInfo>> pageAppIndustry(@RequestBody @Valid BaseAppIndustryPageReq req) {
        Page<BaseAppIndustryInfo> page = baseAppIndustryService.findPage(req);
        operLogService.saveOperLog("App行业管理", "查询App行业页面", OperWebUtils.getWebToken().getUserId());
        return SuperResult.rightResult(page);
    }

    /**
     * 列表
     */
    @WebAuth()
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public SuperResult<List<BaseAppIndustry>> listAppIndustry(@RequestBody @Valid BaseAppIndustryListReq req) {
        List<BaseAppIndustry> page = baseAppIndustryService.findList(req);
        operLogService.saveOperLog("App行业管理", "查询App行业列表", OperWebUtils.getWebToken().getUserId());
        return SuperResult.rightResult(page);
    }

    /**
     * 详情
     */
    @WebAuth("BASE_APP_INDUSTRY_VIEW")
    @RequestMapping(value = "/info", method = RequestMethod.POST)
    public SuperResult<BaseAppIndustry> infoAppIndustry(@RequestBody @Valid BaseAppIndustryInfoReq req) {
        BaseAppIndustry info = baseAppIndustryService.findById(req);
        if (info == null) {
            return SuperResult.badResult("未找到数据");
        } else {
            operLogService.saveOperLog("App行业管理", "查询App行业详情", OperWebUtils.getWebToken().getUserId());
            return SuperResult.rightResult(info);
        }
    }

    /**
     * 新增
     */
    @WebAuth("BASE_APP_INDUSTRY_ADD")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public SuperResult<String> addAppIndustry(@RequestBody @Valid BaseAppIndustryAddReq req) {
        SuperResult<String> result = baseAppIndustryService.add(req, OperWebUtils.getWebToken().getUserId());
        if (result.getSuccess()) {
            operLogService.saveOperLog("App行业管理", "新增App行业：" + req.getName(), OperWebUtils.getWebToken().getUserId());
        }
        return result;
    }

    /**
     * 修改
     */
    @WebAuth("BASE_APP_INDUSTRY_MODIFY")
    @RequestMapping(value = "/modify", method = RequestMethod.POST)
    public SuperResult<String> modifyAppIndustry(@RequestBody @Valid BaseAppIndustryModifyReq req) {
        SuperResult<String> result = baseAppIndustryService.modify(req, OperWebUtils.getWebToken().getUserId());
        if (result.getSuccess()) {
            operLogService.saveOperLog("App行业管理", "编辑App行业：" + req.getId() + " | " + req.getName(), OperWebUtils.getWebToken().getUserId());
        }
        return result;
    }

}
