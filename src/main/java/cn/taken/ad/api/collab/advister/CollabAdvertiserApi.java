package cn.taken.ad.api.collab.advister;

import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.web.WebAuth;
import cn.taken.ad.core.dto.web.collab.CollabUserDataAuthDto;
import cn.taken.ad.core.dto.web.oper.advertiser.app.AdvertiserAppListReq;
import cn.taken.ad.core.dto.web.oper.advertiser.main.AdvertiserListReq;
import cn.taken.ad.core.dto.web.oper.advertiser.tag.AdvertiserTagListReq;
import cn.taken.ad.core.pojo.advertiser.Advertiser;
import cn.taken.ad.core.pojo.advertiser.AdvertiserApp;
import cn.taken.ad.core.pojo.advertiser.AdvertiserTag;
import cn.taken.ad.core.service.advertiser.AdvertiserAppService;
import cn.taken.ad.core.service.advertiser.AdvertiserService;
import cn.taken.ad.core.service.advertiser.AdvertiserTagService;
import cn.taken.ad.utils.web.CollabWebToken;
import cn.taken.ad.utils.web.CollabWebUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 媒体接口
 */
@RestController
@RequestMapping(value = "/b/advertiser")
public class CollabAdvertiserApi {
    @Resource
    private AdvertiserService advertiserService;

    @Resource
    private AdvertiserAppService advertiserAppService;
    @Resource
    private AdvertiserTagService advertiserTagService;

    @WebAuth()
    @RequestMapping(value = "list")
    public SuperResult<List<Advertiser>> list(@RequestBody AdvertiserListReq req) {
        CollabWebToken token = CollabWebUtils.getWebToken();
        if (StringUtils.isEmpty(token.getAdvertiserIds())) {
            return SuperResult.rightResult(Collections.emptyList());
        }
        CollabUserDataAuthDto collabUserDataAuthDto = new CollabUserDataAuthDto();
        collabUserDataAuthDto.setUserId(token.getUserId());
        collabUserDataAuthDto.setAdvertiserDataList(token.getAdvertiserIds().split(","));
        List<Advertiser> advertiserInfoList = advertiserService.findCollabList(req, collabUserDataAuthDto);
        return SuperResult.rightResult(advertiserInfoList);
    }

    @WebAuth()
    @RequestMapping(value = "listApp")
    public SuperResult<List<AdvertiserApp>> list(@RequestBody AdvertiserAppListReq req) {
        CollabWebToken token = CollabWebUtils.getWebToken();
        if (StringUtils.isEmpty(token.getAdvertiserIds())) {
            return SuperResult.rightResult(Collections.emptyList());
        }
        CollabUserDataAuthDto collabUserDataAuthDto = new CollabUserDataAuthDto();
        collabUserDataAuthDto.setUserId(token.getUserId());
        collabUserDataAuthDto.setAdvertiserDataList(token.getAdvertiserIds().split(","));
        List<AdvertiserApp> advertiserAppsList = advertiserAppService.findCollabList(req, collabUserDataAuthDto);
        return SuperResult.rightResult(advertiserAppsList);
    }


    @WebAuth()
    @RequestMapping(value = "listTag")
    public SuperResult<List<AdvertiserTag>> findList(@RequestBody @Valid AdvertiserTagListReq req) {
        CollabWebToken token = CollabWebUtils.getWebToken();
        if (StringUtils.isEmpty(token.getAdvertiserIds())) {
            return SuperResult.rightResult(Collections.emptyList());
        }
        CollabUserDataAuthDto collabUserDataAuthDto = new CollabUserDataAuthDto();
        collabUserDataAuthDto.setUserId(token.getUserId());
        collabUserDataAuthDto.setAdvertiserDataList(token.getAdvertiserIds().split(","));
        List<AdvertiserTag> advertiserTagsList = advertiserTagService.findCollabList(req, collabUserDataAuthDto);
        return SuperResult.rightResult(advertiserTagsList);
    }

}
