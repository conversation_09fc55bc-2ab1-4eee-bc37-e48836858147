package cn.taken.ad.api.collab.media;

import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.web.WebAuth;
import cn.taken.ad.core.dto.web.collab.CollabUserDataAuthDto;
import cn.taken.ad.core.dto.web.oper.media.app.MediaAppListReq;
import cn.taken.ad.core.dto.web.oper.media.main.MediaListReq;
import cn.taken.ad.core.dto.web.oper.media.tag.MediaTagListReq;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.core.pojo.media.MediaApp;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.core.service.media.MediaAppService;
import cn.taken.ad.core.service.media.MediaService;
import cn.taken.ad.core.service.media.MediaTagService;
import cn.taken.ad.utils.web.CollabWebToken;
import cn.taken.ad.utils.web.CollabWebUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 媒体接口
 */
@RestController
@RequestMapping(value = "/b/media")
public class CollabMediaApi {

    @Resource
    private MediaService mediaService;
    @Resource
    private MediaAppService mediaAppService;
    @Resource
    private MediaTagService mediaTagService;

    @WebAuth()
    @RequestMapping(value = "list")
    public SuperResult<List<Media>> list(@RequestBody MediaListReq req) {
        CollabWebToken token = CollabWebUtils.getWebToken();
        if (StringUtils.isEmpty(token.getMediaIds())) {
            return SuperResult.rightResult(Collections.emptyList());
        }
        CollabUserDataAuthDto collabUserDataAuthDto = new CollabUserDataAuthDto();
        collabUserDataAuthDto.setUserId(token.getUserId());
        collabUserDataAuthDto.setMediaDataList(token.getMediaIds().split(","));
        List<Media> mediaList = mediaService.findCollabList(req, collabUserDataAuthDto);
        return SuperResult.rightResult(mediaList);
    }

    @WebAuth()
    @RequestMapping(value = "listApp")
    public SuperResult<List<MediaApp>> list(@RequestBody @Valid MediaAppListReq req) {
        CollabWebToken token = CollabWebUtils.getWebToken();
        if (StringUtils.isEmpty(token.getMediaIds())) {
            return SuperResult.rightResult(Collections.emptyList());
        }
        CollabUserDataAuthDto collabUserDataAuthDto = new CollabUserDataAuthDto();
        collabUserDataAuthDto.setUserId(token.getUserId());
        collabUserDataAuthDto.setMediaDataList(token.getMediaIds().split(","));
        List<MediaApp> mediaApps = mediaAppService.findCollabList(req, collabUserDataAuthDto);
        return SuperResult.rightResult(mediaApps);
    }


    @WebAuth()
    @RequestMapping(value = "listTag")
    public SuperResult<List<MediaTag>> findList(@RequestBody MediaTagListReq req) {
        CollabWebToken token = CollabWebUtils.getWebToken();
        if (StringUtils.isEmpty(token.getMediaIds())) {
            return SuperResult.rightResult(Collections.emptyList());
        }
        CollabUserDataAuthDto collabUserDataAuthDto = new CollabUserDataAuthDto();
        collabUserDataAuthDto.setUserId(token.getUserId());
        collabUserDataAuthDto.setMediaDataList(token.getMediaIds().split(","));
        List<MediaTag> mediaTags = mediaTagService.findCollabList(req, collabUserDataAuthDto);
        return SuperResult.rightResult(mediaTags);
    }
}
