package cn.taken.ad.configuration.redis;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.redis.conf.ClusterRedisConf;
import cn.taken.ad.component.redis.conf.SingleRedisConf;
import cn.taken.ad.component.redis.impl.RedisClusterClient;
import cn.taken.ad.component.redis.impl.RedisSingleClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * Redis组件配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "redis.base")
@Order(0)
public class BaseRedisConfiguration {

    /**
     * 单点Redis参数
     */
    private SingleRedisConf single;
    /**
     * 集群Redis参数
     */
    private ClusterRedisConf cluster;

    @Bean(name = "BaseRedis", destroyMethod = "close")
    public RedisClient baseRedis() {
        if (single != null) {
            return new RedisSingleClient(single);
        } else if (cluster != null) {
            return new RedisClusterClient(cluster);
        } else {
            throw new RuntimeException("redis conf is null");
        }
    }

    public SingleRedisConf getSingle() {
        return single;
    }

    public void setSingle(SingleRedisConf single) {
        this.single = single;
    }

    public ClusterRedisConf getCluster() {
        return cluster;
    }

    public void setCluster(ClusterRedisConf cluster) {
        this.cluster = cluster;
    }


}
