package cn.taken.ad.configuration.cache;

import cn.taken.ad.component.redis.RedisClient;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Scheduler;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 二级缓存
 *
 * <AUTHOR>
 */
@Component
public class MemoryCacheConfiguration {

    @Bean(name = "MemoryCache")
    public Cache<String, Object> genMemoryCache() {
        return Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(60, TimeUnit.SECONDS)
                .scheduler(Scheduler.systemScheduler())
                .build();
    }


}
