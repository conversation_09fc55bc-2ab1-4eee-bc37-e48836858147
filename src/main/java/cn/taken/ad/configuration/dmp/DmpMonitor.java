package cn.taken.ad.configuration.dmp;

import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.dto.business.dmp.DmpPackChunk;
import cn.taken.ad.core.pojo.dmp.DmpPackage;
import cn.taken.ad.logic.base.event.RtbEventDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestDeviceDto;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

@Component
public class DmpMonitor {

    @Resource(name = "BaseRedisL2Cache")
    private BaseRedisL2Cache baseRedisL2Cache;

    private final Queue<DmpPackChunk> queue = new ConcurrentLinkedQueue<>();

    private final Map<String, Queue<String>> writerMap = new ConcurrentHashMap<>();

    public void acceptMemory(DmpPackChunk chunk) {
        for (String id : chunk.getDeviceIds()) {
            String[] ids = id.split("_");
            String deviceType = ids[0];
            String deviceValue = ids[1];
            writerMap.computeIfAbsent(chunk.getId() + "_" + deviceType, k -> new ConcurrentLinkedQueue<>()).add(deviceValue);
        }
    }

    public Set<String> getMemoryKeys() {
        return writerMap.keySet();
    }

    public String poolMemory(String key) {
        return writerMap.computeIfAbsent(key, k -> new ConcurrentLinkedQueue<>()).poll();
    }

    public void acceptRedis(DmpPackChunk chunk) {
        queue.add(chunk);
    }

    public DmpPackChunk pollRedis() {
        return queue.poll();
    }

    public void collectPackDevice(RtbEventDto dto, Integer eventType) {
        Set<String> packageIds = baseRedisL2Cache.smembers(BaseRedisKeys.SET_DMP_PACK_REAL_TIME);
        if (packageIds == null || packageIds.isEmpty()) {
            return;
        }
        if (dto.getDeviceIds() == null || dto.getDeviceIds().isEmpty()) {
            return;
        }
        for (String packageIdStr : packageIds) {
            DmpPackage pack = baseRedisL2Cache.get(BaseRedisKeys.KV_DMP_PACK_ID_ + packageIdStr, DmpPackage.class);
            if (pack != null) {
                doCollect(pack, dto.getDeviceIds(), dto.getMediaId(), dto.getMediaAppId(), dto.getMediaTagId(), dto.getAdvertiserId(), dto.getAdvertiserAppId(), dto.getAdvertiserTagId(), eventType);
            }
        }
    }

    public void collectPackDevice(RequestDeviceDto deviceInfo, Long mediaId, Long mediaAppId, Long mediaTagId, Long advId, Long advAppId, Long advTagId, Integer eventType) {
        Set<String> packageIds = baseRedisL2Cache.smembers(BaseRedisKeys.SET_DMP_PACK_REAL_TIME);
        if (packageIds == null || packageIds.isEmpty()) {
            return;
        }
        if (deviceInfo == null) {
            return;
        }
        Set<String> deviceIds = deviceInfo.toDeviceMd5List();
        if (deviceIds == null || deviceIds.isEmpty()) {
            return;
        }
        for (String packageIdStr : packageIds) {
            DmpPackage pack = baseRedisL2Cache.get(BaseRedisKeys.KV_DMP_PACK_ID_ + packageIdStr, DmpPackage.class);
            if (pack != null) {
                doCollect(pack, deviceIds, mediaId, mediaAppId, mediaTagId, advId, advAppId, advTagId, eventType);
            }
        }
    }

    private void doCollect(DmpPackage pack, Set<String> deviceIds, Long mediaId, Long mediaAppId, Long mediaTagId, Long advId, Long advAppId, Long advTagId, Integer eventType) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return;
        }
        if (eventType != pack.getBaseType().intValue()) {
            return;
        }

        List<Long> mediaIds = Collections.emptyList();
        if (StringUtils.isNotBlank(pack.getBaseMediaIds())) {
            mediaIds = JsonHelper.fromJson(new TypeToken<List<Long>>() {
            }, pack.getBaseMediaIds());
        }
        List<Long> mediaAppIds = Collections.emptyList();
        if (StringUtils.isNotBlank(pack.getBaseMediaAppIds())) {
            mediaAppIds = JsonHelper.fromJson(new TypeToken<List<Long>>() {
            }, pack.getBaseMediaAppIds());
        }
        List<Long> mediaTagIds = Collections.emptyList();
        if (StringUtils.isNotBlank(pack.getBaseMediaTagIds())) {
            mediaTagIds = JsonHelper.fromJson(new TypeToken<List<Long>>() {
            }, pack.getBaseMediaTagIds());
        }
        List<Long> advIds = Collections.emptyList();
        if (StringUtils.isNotBlank(pack.getBaseAdvIds())) {
            advIds = JsonHelper.fromJson(new TypeToken<List<Long>>() {
            }, pack.getBaseAdvIds());
        }
        List<Long> advAppIds = Collections.emptyList();
        if (StringUtils.isNotBlank(pack.getBaseAdvAppIds())) {
            advAppIds = JsonHelper.fromJson(new TypeToken<List<Long>>() {
            }, pack.getBaseAdvAppIds());
        }
        List<Long> advTagIds = Collections.emptyList();
        if (StringUtils.isNotBlank(pack.getBaseAdvTagIds())) {
            advTagIds = JsonHelper.fromJson(new TypeToken<List<Long>>() {
            }, pack.getBaseAdvTagIds());
        }
        if (!mediaIds.isEmpty()) {
            if (!mediaIds.contains(mediaId)) {
                return;
            }
        }
        if (!mediaAppIds.isEmpty()) {
            if (!mediaAppIds.contains(mediaAppId)) {
                return;
            }
        }
        if (!mediaTagIds.isEmpty()) {
            if (!mediaTagIds.contains(mediaTagId)) {
                return;
            }
        }
        if (!advIds.isEmpty()) {
            if (!advIds.contains(advId)) {
                return;
            }
        }
        if (!advAppIds.isEmpty()) {
            if (!advAppIds.contains(advAppId)) {
                return;
            }
        }
        if (!advTagIds.isEmpty()) {
            if (!advTagIds.contains(advTagId)) {
                return;
            }
        }

        DmpPackChunk chunk = new DmpPackChunk(pack.getId(), deviceIds, pack.getCacheType(), pack.getCacheDay());
        // 收集
        this.acceptMemory(chunk);
        // 缓存
        this.acceptRedis(chunk);
    }

}
