package cn.taken.ad.configuration.obs;

import cn.taken.ad.component.obs.ObjectSaveClient;
import cn.taken.ad.component.obs.impl.tos.TosConfig;
import cn.taken.ad.component.obs.impl.tos.TosObjectSaveClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "dmp")
public class DmpObjectSaveClientConfiguration {

    private TosConfig tos;

    @Bean("dmpObs")
    public ObjectSaveClient objectSaveClient() {
        if (tos != null) {
            return new TosObjectSaveClient(this.tos);
        }
        return null;
    }

    public TosConfig getTos() {
        return tos;
    }

    public void setTos(TosConfig tos) {
        this.tos = tos;
    }
}
