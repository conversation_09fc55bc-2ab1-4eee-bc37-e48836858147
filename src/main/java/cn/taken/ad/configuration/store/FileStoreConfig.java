package cn.taken.ad.configuration.store;

import cn.taken.ad.component.store.file.queue.FileQueue;
import cn.taken.ad.configuration.PropertiesConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.io.File;

@Configuration
public class FileStoreConfig {

    @Resource
    private PropertiesConfiguration propertiesConfiguration;

    @Bean(name = "BillFileQueue")
    public FileQueue genServerInfo() {
        return new FileQueue(propertiesConfiguration.getStoreDir() + File.separator + "bill");
    }

}
