package cn.taken.ad.logic.adv.yaya;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;

public class YaAes {

    public static void main(String[] args) {

        String testAesKey = "8761345854715781";
        String value = "2000";

        // 加密
        String encrypted = aesEncryptTest(value, testAesKey);
        System.out.println(encrypted); // 输出应与Go版本相似

        // 解密
        String decrypted = aesTestDecrypt(encrypted, testAesKey);
        System.out.println(decrypted); // 应输出 "2000"
    }

    public static String aesEncryptTest(String rawData, String key) {
        try {
            byte[] encrypted = aesTestCBCEncrypt(rawData.getBytes(), key.getBytes());
            return Base64.getUrlEncoder().withoutPadding().encodeToString(encrypted);
        } catch (Exception e) {
            return "";
        }
    }
    private static byte[] aesTestCBCEncrypt(byte[] rawData, byte[] key) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "AES");

        // 生成随机IV
        byte[] iv = new byte[16];
        SecureRandom random = new SecureRandom();
        random.nextBytes(iv);
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encrypted = cipher.doFinal(rawData);

        // 合并IV和密文
        byte[] result = new byte[iv.length + encrypted.length];
        System.arraycopy(iv, 0, result, 0, iv.length);
        System.arraycopy(encrypted, 0, result, iv.length, encrypted.length);
        return result;
    }
    public static String aesTestDecrypt(String rawData, String key) {
        try {
            byte[] decoded = Base64.getUrlDecoder().decode(rawData);
            byte[] decrypted = aesTestCBCDecrypt(decoded, key.getBytes());
            return new String(decrypted);
        } catch (Exception e) {
            return "";
        }
    }
    private static byte[] aesTestCBCDecrypt(byte[] encryptData, byte[] key) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "AES");

        // 分离IV和密文
        byte[] iv = Arrays.copyOfRange(encryptData, 0, 16);
        byte[] cipherText = Arrays.copyOfRange(encryptData, 16, encryptData.length);

        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        return cipher.doFinal(cipherText);
    }
}
