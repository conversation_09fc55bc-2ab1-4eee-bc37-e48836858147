// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: most_mob_api.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.mostmob.dto;

/**
 * <pre>
 * 操作系统类型
 * </pre>
 *
 * Protobuf enum {@code OSType}
 */
public enum OSType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * 未知
   * </pre>
   *
   * <code>UnknownOS = 0;</code>
   */
  UnknownOS(0),
  /**
   * <pre>
   * android
   * </pre>
   *
   * <code>Android = 1;</code>
   */
  Android(1),
  /**
   * <pre>
   * ios
   * </pre>
   *
   * <code>IOS = 2;</code>
   */
  IOS(2),
  /**
   * <pre>
   * 鸿蒙
   * </pre>
   *
   * <code>HarmonyOs = 3;</code>
   */
  HarmonyOs(3),
  UNRECOGNIZED(-1),
  ;

  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      OSType.class.getName());
  }
  /**
   * <pre>
   * 未知
   * </pre>
   *
   * <code>UnknownOS = 0;</code>
   */
  public static final int UnknownOS_VALUE = 0;
  /**
   * <pre>
   * android
   * </pre>
   *
   * <code>Android = 1;</code>
   */
  public static final int Android_VALUE = 1;
  /**
   * <pre>
   * ios
   * </pre>
   *
   * <code>IOS = 2;</code>
   */
  public static final int IOS_VALUE = 2;
  /**
   * <pre>
   * 鸿蒙
   * </pre>
   *
   * <code>HarmonyOs = 3;</code>
   */
  public static final int HarmonyOs_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static OSType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static OSType forNumber(int value) {
    switch (value) {
      case 0: return UnknownOS;
      case 1: return Android;
      case 2: return IOS;
      case 3: return HarmonyOs;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<OSType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      OSType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<OSType>() {
          public OSType findValueByNumber(int number) {
            return OSType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return cn.taken.ad.logic.adv.mostmob.dto.MostMobAdvRequest.getDescriptor().getEnumTypes().get(0);
  }

  private static final OSType[] VALUES = values();

  public static OSType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private OSType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:OSType)
}

