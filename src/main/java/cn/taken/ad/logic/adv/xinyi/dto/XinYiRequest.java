package cn.taken.ad.logic.adv.xinyi.dto;

import java.io.Serializable;
import java.util.List;

public class XinYiRequest implements Serializable {
    private static final long serialVersionUID = -1197451486753442268L;
    /**
     * 请求 id
     */
    private String id;
    /**
     * 协议版本, 当前协议版本 2.0.0
     */
    private String version;
    private List<XinYiRequestAd> ads;
    private XinYiRequestApp app;
    private XinYiRequestDevice device;
    private XinYiRequestUser user;
    private Boolean need_https;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public List<XinYiRequestAd> getAds() {
        return ads;
    }

    public void setAds(List<XinYiRequestAd> ads) {
        this.ads = ads;
    }

    public XinYiRequestApp getApp() {
        return app;
    }

    public void setApp(XinYiRequestApp app) {
        this.app = app;
    }

    public XinYiRequestDevice getDevice() {
        return device;
    }

    public void setDevice(XinYiRequestDevice device) {
        this.device = device;
    }

    public XinYiRequestUser getUser() {
        return user;
    }

    public void setUser(XinYiRequestUser user) {
        this.user = user;
    }

    public Boolean getNeed_https() {
        return need_https;
    }

    public void setNeed_https(Boolean need_https) {
        this.need_https = need_https;
    }
}
