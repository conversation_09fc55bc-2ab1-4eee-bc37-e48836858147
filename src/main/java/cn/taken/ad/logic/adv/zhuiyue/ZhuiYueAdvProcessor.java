package cn.taken.ad.logic.adv.zhuiyue;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Md5;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.zhuiyue.dto.ZhuiYueRequest;
import cn.taken.ad.logic.adv.zhuiyue.dto.ZhuiYueResponse;
import cn.taken.ad.logic.adv.zhuiyue.dto.req.*;
import cn.taken.ad.logic.adv.zhuiyue.dto.resp.Video;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.ParamParser;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Component("ZHUIYUE" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class ZhuiYueAdvProcessor implements AdvProcessor {

    private static final String API_VERSION = "apiVersion";

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        Map<String, String> params = ParamParser.parseParamByJson(advDto.getPnyParam());
        ZhuiYueRequest request = new ZhuiYueRequest();
        request.setVersion(params.get(API_VERSION));
        request.setApp(createReqApp(rtbDto, advDto));
        request.setDevice(createReqDevice(rtbDto));
        request.setNetwork(createReqNetwork(rtbDto));
        request.setGps(createReqGps(rtbDto));
        request.setUser(createReqUser(rtbDto));
        request.setAdSlots(createReqSlot(rtbDto, advDto));
        if (rtbDto.getTag().getPrice() != null) {
            Bid bid = new Bid();
            bid.setType(1);
            //分->元
            bid.setPrice(new BigDecimal(rtbDto.getTag().getPrice() / 100).setScale(3, RoundingMode.HALF_UP).doubleValue());
            request.setBid(bid);
        }
        if (StringUtils.isNotBlank(rtbDto.getDevice().getCountry())) {
            Region region = new Region();
            region.setCountry(rtbDto.getDevice().getCountry());
            request.setRegion(region);
        }
        request.setTimestamp(System.currentTimeMillis());
        request.setResponseTime(advDto.getTimeout());
        String sign = request.getVersion() + "|" + advDto.getAppCode() + "|" + advDto.getTagCode() + "|" + request.getTimestamp() + "|" + request.getDevice().getDeviceId();
        sign = Md5.md5(sign);
        request.setSign(sign);
        advDto.setReqObj(request);
        String json = JsonHelper.toJsonStringWithoutNull(request);
        HttpResult httpResult = httpClient.postJson(advDto.getRtburl() + "/" + advDto.getTagCode(), json, "utf-8", new Header[]{
                new BasicHeader("Content-Type", "application/json"),
                new BasicHeader("Accept-Encoding", "gzip")
        }, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        return parseRtbResponse(httpClient, httpResult, rtbDto, advDto);
    }

    private RtbResponseDto parseRtbResponse(FastHttpClient httpClient, HttpResult httpResult, RtbRequestDto rtbDto, RtbAdvDto advDto) {
        ZhuiYueResponse response = null;
        try {
            response = httpResult.getDataObjectByJson(ZhuiYueResponse.class);
            advDto.setRespObj(response);
        } catch (Exception e) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Serializable resp fail");
        }
        int code = response.getCode();
        if (code != 0) {
            if (code == 204) {
                return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
            } else {
                new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), code + "");
            }
        }
        if (CollectionUtils.isEmpty(response.getAds())) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", code + "");
        response.getAds().forEach(ad -> {
            TagResponseDto tag = new TagResponseDto();
            tag.setTitle(ad.getTitle());
            tag.setDesc(ad.getDescription());
            tag.setLogoUrl(ad.getLogoUrl());
            tag.setIconUrl(ad.getIconUrl());
            tag.setImgUrls(ad.getImgs());
            tag.setClickUrl(ad.getClickUrl());
            tag.setDeepLinkUrl(ad.getDeepLinkUrl());
            if (ad.getCpmPrice() != null) {
                //元->分
                tag.setPrice(ad.getCpmPrice() * 100);
            }
            ResponseAppDto app = new ResponseAppDto();
            app.setAppName(ad.getAppName());
            app.setPackageName(ad.getPackageName());
            app.setRating(ad.getRating());
            app.setRatingCount(ad.getComment());
            String clickId = null;
            tag.setActionType(ActionType.WEB_VIEW_H5);
            if (ad.getActionType() == 1) {
                tag.setActionType(ActionType.DOWNLOAD);
                if (ad.getGdt() == 1) {
                    HttpResult result = httpClient.get(ad.getClickUrl(), null, null, advDto.getTimeout());
                    if (result.isSuccess()) {
                        JsonObject obj = JsonHelper.fromJson(JsonElement.class, result.getDataStringUTF8()).getAsJsonObject();
                        if (obj.get("ret").getAsInt() == 0) {
                            JsonObject data = obj.get("data").getAsJsonObject();
                            tag.setClickUrl(data.get("dstlink").getAsString());
                            clickId = data.get("clickid").getAsString();
                        }
                    }
                }
            }
            tag.setMaterialType(MaterialType.IMAGE_TEXT);
            if (ad.getMaterialType() != null) {
                if (1 == ad.getMaterialType()) {
                    tag.setMaterialType(MaterialType.VIDEO);
                } else if (3 == ad.getMaterialType()) {
                    tag.setMaterialType(MaterialType.TEXT);
                } else if (4 == ad.getMaterialType()) {
                    tag.setMaterialType(MaterialType.HTML);
                }
            }
            tag.setHtmlContent(ad.getHtml());
            tag.setWinNoticeUrls(new ArrayList<>());
            tag.setFailNoticeUrls(new ArrayList<>());
            List<ResponseTrackDto> tracks = new ArrayList<>();
            tag.setTracks(tracks);
            if (!CollectionUtils.isEmpty(ad.getTracks())) {
                ad.getTracks().forEach(t -> {
                    int type = t.getTrackType();
                    switch (type) {
                        case 1:
                            tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 2:
                            tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 3:
                            tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 4:
                            tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 5:
                            tracks.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 6:
                            tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 7:
                            tracks.add(new ResponseTrackDto(EventType.ACTIVE_APP.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 8:
                            tracks.add(new ResponseTrackDto(EventType.CLOSE_AD.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 9:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 10:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_25.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 11:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_50.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 12:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_75.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 13:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 14:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_SKIP.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 15:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_FULL_SCREEN.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 16:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_EXITS_FULL_SCREEN.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 17:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_LOADED_SUCCESS.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 18:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_LOADED_FAIL.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 19:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_MUTE.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 20:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_UNMUTE.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 21:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 22:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_CONTINUE.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 23:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_ERROR.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 24:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_REPLAY.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 25:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_SLIDE_UP.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 26:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_SLIDE_DOWN.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 27:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_EDN_DISPLAYED.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 28:
                            tag.getWinNoticeUrls().addAll(t.getUrls());
                            break;
                        case 29:
                            tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 30:
                            tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 31:
                            tracks.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 32:
                        case 33:
                        case 34:
                        case 35:
                        case 36:
                        case 37:
                            break;
                        case 38:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_CLOSE.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                        case 39:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_CLICK.getType(), new ArrayList<>(t.getUrls()), t.getMethod(), t.getContentType(), t.getContent()));
                            break;
                    }
                });
            }
            tag.setAppInfo(app);
            if (ad.getVideo() != null) {
                ResponseVideoDto videoDto = new ResponseVideoDto();
                Video video = ad.getVideo();
                videoDto.setVideoUrl(video.getVideoUrl());
                if (video.getDuration() != null) {
                    videoDto.setDuration((int) (video.getDuration() / 1000));
                }
                videoDto.setVideoWidth(video.getWidth());
                videoDto.setVideoHeight(video.getHeight());
                videoDto.setCoverImgUrls(video.getCoverImageUrls());
                videoDto.setButtonText(video.getButtonText());
                videoDto.setEndHtml(video.getEndHtml());
                videoDto.setAutoLanding(video.getAutoJump());
                videoDto.setPrefetch(video.getPrefetch());
                videoDto.setClickAble(video.getCanJump());
                videoDto.setSkipSeconds(video.getSkip());
                videoDto.setEndButtonText(video.getEndButtonText());
                videoDto.setEndIconUrl(video.getEndIconUrl());
                videoDto.setEndTitle(video.getEndTitle());
                videoDto.setEndDesc(video.getEndDescription());
                tag.setVideoInfo(videoDto);
            }
            //宏替换
            String finalCid = clickId;
            RequestDeviceDto deviceDto = rtbDto.getDevice();
            tag.getTracks().forEach(track -> {
                List<String> urls = track.getTrackUrls();
                if (CollectionUtils.isEmpty(urls)) {
                    return;
                }
                urls = replaceMacro("__WIDTH__", urls, MacroType.WIDTH.getCode());
                urls = replaceMacro("__HEIGHT__", urls, MacroType.HEIGHT.getCode());
                urls = replaceMacro("__D_X__", urls, MacroType.DOWN_X.getCode());
                urls = replaceMacro("__D_Y__", urls, MacroType.DOWN_Y.getCode());
                urls = replaceMacro("__U_X__", urls, MacroType.UP_X.getCode());
                urls = replaceMacro("__U_Y__", urls, MacroType.UP_Y.getCode());

                urls = replaceMacro("__D_X_ABS__", urls, MacroType.ABS_DOWN_X.getCode());
                urls = replaceMacro("__D_Y_ABS__", urls, MacroType.ABS_DOWN_Y.getCode());
                urls = replaceMacro("__U_X_ABS__", urls, MacroType.ABS_UP_X.getCode());
                urls = replaceMacro("__U_Y_ABS__", urls, MacroType.ABS_UP_Y.getCode());


                urls = replaceMacro("__T__", urls, MacroType.TIME.getCode());
                urls = replaceMacro("__T_S__", urls, MacroType.TIME_SECONDS.getCode());

                urls = replaceMacro("__T_END__", urls, MacroType.END_TIME.getCode());
                urls = replaceMacro("__T_S_END__", urls, MacroType.END_TIME_SECONDS.getCode());
                urls = replaceMacro("__BEHAVIOR__", urls, MacroType.VIDEO_BEHAVIOR.getCode());
                urls = replaceMacro("__BEGIN_TIME__", urls, MacroType.VIDEO_BEGIN_TIME.getCode());
                urls = replaceMacro("__END_TIME__", urls, MacroType.VIDEO_END_TIME.getCode());
                urls = replaceMacro("__PLAY_FIRST_FRAME__", urls, MacroType.VIDEO_PLAY_FIRST_FRAME.getCode());
                urls = replaceMacro("__PLAY_LAST_FRAME__", urls, MacroType.VIDEO_PLAY_LAST_FRAME.getCode());

                urls = replaceMacro("__SCENE__", urls, MacroType.VIDEO_SCENE.getCode());
                urls = replaceMacro("__PLAY_TYPE__", urls, MacroType.VIDEO_TYPE.getCode());
                urls = replaceMacro("__STATUS__", urls, MacroType.VIDEO_STATUS.getCode());
                urls = replaceMacro("__PLAY_SECOND__", urls, MacroType.VIDEO_PROGRESS_SEC.getCode());
                urls = replaceMacro("__PLAY_MILLSECOND__", urls, MacroType.VIDEO_PROGRESS.getCode());
                urls = replaceMacro("__VIDEO_DURATION__", urls, MacroType.VIDEO_TIME.getCode());
                if (finalCid != null) {
                    urls = replaceMacro("__CLICK_ID__", urls, finalCid);
                }
                if (StringUtils.isNotBlank(deviceDto.getOaid())) {
                    urls = replaceMacro("__OAID__", urls, deviceDto.getOaid());
                }
                if (StringUtils.isNotBlank(deviceDto.getImei())) {
                    urls = replaceMacro("__IMEI__", urls, deviceDto.getImei());
                }
                if (StringUtils.isNotBlank(deviceDto.getIdfa())) {
                    urls = replaceMacro("__IDFA__", urls, deviceDto.getIdfa());
                }
                if (StringUtils.isNotBlank(deviceDto.getIdfaMd5())) {
                    urls = replaceMacro("__IDFA_MD5__", urls, deviceDto.getIdfaMd5());
                }
                if (StringUtils.isNotBlank(deviceDto.getAndroidId())) {
                    urls = replaceMacro("__ANDROIDID__", urls, deviceDto.getAndroidId());
                }
                if (StringUtils.isNotBlank(deviceDto.getAndroidIdMd5())) {
                    urls = replaceMacro("__ANDROIDID_MD5__", urls, deviceDto.getAndroidIdMd5());
                }
                urls = replaceMacro("__IP__", urls, MacroType.IP.getCode());
                urls = replaceMacro("__UA__", urls, MacroType.UA.getCode());
                if (StringUtils.isNotBlank(rtbDto.getNetwork().getMac())) {
                    urls = replaceMacro("__MAC1__", urls, rtbDto.getNetwork().getMac());
                    String tmp = rtbDto.getNetwork().getMac().replaceAll(":", "");
                    urls = replaceMacro("__MAC__", urls, tmp);
                }
                if (StringUtils.isNotBlank(deviceDto.getAaid())) {
                    urls = replaceMacro("__ALL_AAID__", urls, deviceDto.getAaid());
                }
                if (!CollectionUtils.isEmpty(deviceDto.getCaids())) {
                    RequestCaidDto caidDto = null;
                    for (RequestCaidDto caid : deviceDto.getCaids()) {
                        if (StringUtils.isNotBlank(caid.getCaid())) {
                            caidDto = caid;
                            break;
                        }
                    }
                    if (caidDto != null) {
                        urls = replaceMacro("__CAID__", urls, caidDto.getCaid());
                    }
                }
                /*
                urls = replaceMacro("__DP_WIDTH__", urls, MacroType.DP_WIDTH.getCode());
                urls = replaceMacro("__DP_HEIGHT__", urls, MacroType.DP_HEIGHT.getCode());

                urls = replaceMacro("__DP_DOWN_X__", urls, MacroType.DP_DOWN_X.getCode());
                urls = replaceMacro("__DP_DOWN_Y__", urls, MacroType.DP_DOWN_Y.getCode());
                urls = replaceMacro("__DP_UP_X__", urls, MacroType.DP_UP_X.getCode());
                urls = replaceMacro("__DP_UP_Y__", urls, MacroType.DP_UP_Y.getCode());

                urls = replaceMacro("__TARGET_APP_INSTALL__", urls, MacroType.TARGET_APP_INSTALL.getCode());
                urls = replaceMacro("__DISPLAY_LUX__", urls, MacroType.DISPLAY_LUX.getCode());
                urls = replaceMacro("__DISPLAY_LUY__", urls, MacroType.DISPLAY_LUY.getCode());
                urls = replaceMacro("__DISPLAY_RDX__", urls, MacroType.DISPLAY_RDX.getCode());
                urls = replaceMacro("__DISPLAY_RDY__", urls, MacroType.DISPLAY_RDY.getCode());

                urls = replaceMacro("__BUTTON_LUX__", urls, MacroType.BUTTON_LUX.getCode());
                urls = replaceMacro("__BUTTON_LUY__", urls, MacroType.BUTTON_LUY.getCode());
                urls = replaceMacro("__BUTTON_RDX__", urls, MacroType.BUTTON_RDX.getCode());
                urls = replaceMacro("__BUTTON_RDY__", urls, MacroType.BUTTON_RDY.getCode());
                 */
                track.setTrackUrls(urls);
            });
            responseDto.getTags().add(tag);
        });
        return responseDto;
    }

    private List<AdSlot> createReqSlot(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        List<AdSlot> list = new ArrayList<>();
        AdSlot slot = new AdSlot();
        slot.setSlotId(advDto.getTagCode());
        slot.setWidth(rtbDto.getTag().getWidth());
        slot.setHeight(rtbDto.getTag().getHeight());
        list.add(slot);
        return list;
    }

    private User createReqUser(RtbRequestDto rtbDto) {
        User builder = new User();
        RequestUserDto dto = rtbDto.getUser();
        builder.setId(dto.getUserId());
        if ("M".equals(dto.getGender())) {
            builder.setSex(1);
        } else if ("FM".equals(dto.getGender())) {
            builder.setSex(2);
        }
        builder.setAge(dto.getAge());
        if (dto.getInterest() != null) {
            builder.setLabel(Arrays.asList(dto.getInterest()));
        }
        return builder;
    }

    private Gps createReqGps(RtbRequestDto rtbDto) {
        Gps builder = new Gps();
        RequestGeoDto dto = rtbDto.getGeo();
        if (CoordinateType.GLOBAL == dto.getCoordinateType()) {
            builder.setType(1);
        } else if (CoordinateType.STATE == dto.getCoordinateType()) {
            builder.setType(2);
        } else if (CoordinateType.BAIDU == dto.getCoordinateType()) {
            builder.setType(3);
        } else {
            builder.setType(0);
        }
        builder.setLat(dto.getLatitude() == null ? "" : dto.getLatitude().toString());
        builder.setLon(dto.getLongitude() == null ? "" : dto.getLongitude().toString());
        return builder;
    }

    private Network createReqNetwork(RtbRequestDto rtbDto) {
        Network builder = new Network();
        RequestNetworkDto dto = rtbDto.getNetwork();
        builder.setIp(dto.getIp());
        builder.setIpv6(dto.getIpv6());
        builder.setNetwork("未知");
        if (ConnectionType.WIFI == dto.getConnectType()) {
            builder.setNetwork("wifi");
        } else if (ConnectionType.NETWORK_2G == dto.getConnectType()) {
            builder.setNetwork("2g");
        } else if (ConnectionType.NETWORK_3G == dto.getConnectType()) {
            builder.setNetwork("3g");
        } else if (ConnectionType.NETWORK_4G == dto.getConnectType()) {
            builder.setNetwork("4g");
        } else if (ConnectionType.NETWORK_5G == dto.getConnectType()) {
            builder.setNetwork("5g");
        }
        builder.setCarrier(0);
        if (CarrierType.CM == dto.getCarrierType()) {
            builder.setCarrier(1);
        } else if (CarrierType.CU == dto.getCarrierType()) {
            builder.setCarrier(2);
        } else if (CarrierType.CT == dto.getCarrierType()) {
            builder.setCarrier(4);
        }
        builder.setUa(rtbDto.getDevice().getUserAgent());
        builder.setWifiMacAddress(StringUtils.isBlank(dto.getWifiMac()) ? "" : dto.getWifiMac());
        return builder;
    }

    private Device createReqDevice(RtbRequestDto rtbDto) {
        Device builder = new Device();
        RequestDeviceDto dto = rtbDto.getDevice();
        if (OsType.IOS == dto.getOsType()) {
            builder.setDeviceId(dto.getIdfa());
            builder.setDeviceIdMd5(dto.getIdfaMd5());
            builder.setOsType(1);
        } else {
            builder.setDeviceId(dto.getImei());
            builder.setDeviceIdMd5(dto.getImeiMd5());
            if (OsType.ANDROID == dto.getOsType()) {
                builder.setOsType(0);
            } else if (OsType.WINDOWS_PHONE == dto.getOsType()) {
                builder.setOsType(2);
            } else {
                builder.setOsType(3);
            }
        }
        if (StringUtils.isBlank(builder.getDeviceIdMd5()) && StringUtils.isNotBlank(builder.getDeviceId())) {
            builder.setDeviceIdMd5(Md5.md5(builder.getDeviceId()));
        }
        if (StringUtils.isBlank(builder.getDeviceId())) {
            builder.setDeviceId("");
        }
        builder.setOsVersion(dto.getOsVersion());
        builder.setOsApiLevel(dto.getApiLevel());
        builder.setBrand(dto.getBrand());
        builder.setModel(dto.getModel());
        builder.setManufacturer(dto.getVendor());
        builder.setOaid(StringUtils.isNotBlank(dto.getOaid()) ? dto.getOaid() : "");
        builder.setImsi(StringUtils.isNotBlank(dto.getImsi()) ? dto.getImsi() : "");
        builder.setDeviceType(0);
        if (DeviceType.PHONE == dto.getDeviceType()) {
            builder.setDeviceType(2);
        } else if (DeviceType.PAD == dto.getDeviceType()) {
            builder.setDeepLink(1);
        }
        builder.setAndroidId(dto.getAndroidId());
        builder.setAndroidIdMd5(dto.getAndroidIdMd5());
        RequestNetworkDto networkDto = rtbDto.getNetwork();
        builder.setMacAddress(networkDto.getMac());
        builder.setMacAddressMd5(networkDto.getMacMd5());
        if (StringUtils.isBlank(builder.getMacAddressMd5()) && StringUtils.isNotBlank(builder.getMacAddress())) {
            builder.setMacAddressMd5(Md5.md5(builder.getMacAddress()));
        }
        builder.setIdfv(dto.getIdfv());
        builder.setOpenUDID(dto.getOpenUdId());
        builder.setSerialNumber(dto.getSerialNO());
        builder.setDeepLink(1);
        builder.setScreenWidth(dto.getWidth());
        builder.setScreenHeight(dto.getHeight());
        builder.setScreenDensity(dto.getScreenDensity());
        if (dto.getScreenDensity() != null) {
            builder.setScreenDpi((int) (dto.getScreenDensity() * 160));
        }
        builder.setScreenSizeInInches(dto.getScreenInch());
        if (OrientationType.HORIZONTAL == dto.getOrientation()) {
            builder.setScreenOrientation(2);
        } else if (OrientationType.VERTICAL == dto.getOrientation()) {
            builder.setScreenOrientation(1);
        }
        builder.setSsid(StringUtils.isNotBlank(networkDto.getSsid()) ? networkDto.getSsid() : "");
        builder.setRomVersion(StringUtils.isNotBlank(dto.getRomVersion()) ? dto.getRomVersion() : "");
        builder.setCaid("");
        if (!CollectionUtils.isEmpty(dto.getCaids())) {
            List<Caid> caids = new ArrayList<>();
            dto.getCaids().forEach(v -> {
                Caid caid = new Caid();
                caid.setId(StringUtils.isNotBlank(v.getCaid()) ? v.getCaid() : "");
                caid.setVersion(StringUtils.isNotBlank(v.getVersion()) ? v.getVersion() : "");
            });
            builder.setCaids(caids);
        } else {
            builder.setCaids(new ArrayList<>());
        }
        builder.setHms(StringUtils.isNotBlank(dto.getHmsVersion()) ? dto.getHmsVersion() : "");
        builder.setAppStoreVersion(StringUtils.isNotBlank(dto.getAppStoreVersion()) ? dto.getAppStoreVersion() : "");
        builder.setInitTime(dto.getSysInitTime());
        builder.setBooTime(dto.getSysStartTime());
        builder.setName(StringUtils.isNotBlank(dto.getDeviceName()) ? dto.getDeviceName() : "");
        builder.setNameMd5(StringUtils.isNotBlank(dto.getDeviceNameMd5()) ? dto.getDeviceNameMd5() : "");
        builder.setMemorySize(dto.getDeviceMemory() == null ? 0 : dto.getDeviceMemory().intValue());
        builder.setStorageSize(dto.getDeviceHardDisk() == null ? 0 : dto.getDeviceHardDisk().intValue());
        builder.setCpuNumber(dto.getCpuNum() == null ? "" : dto.getCpuNum().toString());
        builder.setCpuFrequency(dto.getCpuFreq() == null ? 0f : dto.getCpuFreq().floatValue());
        builder.setHardwareMachine(dto.getHardwareMachine());
        builder.setHardwareModel(dto.getHardwareModel());
        builder.setElapsedRealtime(StringUtils.isNotBlank(dto.getSysElapseTime()) ? dto.getSysElapseTime() : "");
        builder.setSysUpdateMark(dto.getUpdateMark());
        builder.setSysBootMark(dto.getBootMark());
        builder.setSysUpdateTime(dto.getSysUpdateTime());
        builder.setSysCompileTime(StringUtils.isNotBlank(dto.getSysCompileTime()) ? dto.getSysCompileTime() : "");
        builder.setPddPaid(dto.getPaid());
        builder.setAaid(dto.getAaid());
        builder.setIdfaPolicy(dto.getIdfaPolicy());
        builder.setBatteryStatus(dto.getBatteryStatus());
        builder.setBatteryPower(dto.getBatteryPower());
        builder.setTimeZone(dto.getTimeZone());
        builder.setSysUiVersion(StringUtils.isNotBlank(dto.getSysUiVersion()) ? dto.getSysUiVersion() : "");
        builder.setLocalName(StringUtils.isNotBlank(dto.getLocalName()) ? dto.getLocalName() : "");
        return builder;
    }

    private App createReqApp(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        App builder = new App();
        RequestAppDto dto = rtbDto.getApp();
        builder.setAppId(advDto.getAppCode());
        builder.setAppName(dto.getAppName());
        builder.setPackageName(dto.getBundle());
        builder.setVersionName(dto.getAppVersion());
        if (StringUtils.isNotBlank(dto.getAppVersionCode())) {
            try {
                builder.setVersionCode(Integer.valueOf(dto.getAppVersionCode()));
            } catch (Exception e) {

            }
        }
        if (!CollectionUtils.isEmpty(rtbDto.getDevice().getInstalledAppInfo())) {
            List<String> appPackageNames = new LinkedList<>();
            rtbDto.getDevice().getInstalledAppInfo().forEach(v -> {
                if (v != null && StringUtils.isNotBlank(v.getPackageName())) {
                    appPackageNames.add(v.getPackageName());
                }
            });
            if (appPackageNames.size() > 0) {
                builder.setInstalledAppPackageNames(appPackageNames);
            }
        }
        return builder;
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam dto) throws Exception {
        if (!dto.isBiddingSuccess()) {
            return SuperResult.rightResult();
        }
        List<String> ulrs = dto.getUrls();
        if (CollectionUtils.isEmpty(ulrs)) {
            return SuperResult.rightResult();
        }
        boolean success = false;
        for (String url : ulrs) {
            HttpResult result = httpClient.get(url, null, null, -1);
            if (result.isSuccess()) {
                success = true;
            }
        }
        return success ? SuperResult.rightResult() : SuperResult.badResult();
    }
}
