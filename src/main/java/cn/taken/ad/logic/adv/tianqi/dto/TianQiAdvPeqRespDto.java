// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: tianqi_bidding.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.tianqi.dto;

public final class TianQiAdvPeqRespDto {
  private TianQiAdvPeqRespDto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      TianQiAdvPeqRespDto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidRequest_TianQiImp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidRequest_TianQiImp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidRequest_TianQiImp_TianQiVideo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidRequest_TianQiImp_TianQiVideo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidRequest_TianQiApp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidRequest_TianQiApp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidRequest_TianQiDevice_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidRequest_TianQiDevice_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidRequest_TianQiCaid_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidRequest_TianQiCaid_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidRequest_TianQiGeo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidRequest_TianQiGeo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidRequest_TianQiUser_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidRequest_TianQiUser_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidResponse_TianQiSeatbid_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidResponse_TianQiSeatbid_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidResponse_TianQiSeatbid_Bid_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidResponse_TianQiSeatbid_Bid_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidResponse_TianQiSeatbid_Bid_Video_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidResponse_TianQiSeatbid_Bid_Video_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TianQiBidResponse_TianQiSeatbid_Bid_Tracker_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_TianQiBidResponse_TianQiSeatbid_Bid_Tracker_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024tianqi_bidding.proto\"\253\020\n\020TianQiBidRequ" +
      "est\022\n\n\002id\030\001 \001(\t\022(\n\003imp\030\002 \003(\0132\033.TianQiBid" +
      "Request.TianQiImp\022(\n\003app\030\003 \001(\0132\033.TianQiB" +
      "idRequest.TianQiApp\022.\n\006device\030\004 \001(\0132\036.Ti" +
      "anQiBidRequest.TianQiDevice\022*\n\004user\030\005 \001(" +
      "\0132\034.TianQiBidRequest.TianQiUser\022\n\n\002at\030\006 " +
      "\001(\005\022\014\n\004tmax\030\007 \001(\005\032\237\002\n\tTianQiImp\022\n\n\002id\030\001 " +
      "\001(\t\022\r\n\005tagid\030\002 \001(\t\022\t\n\001w\030\003 \001(\005\022\t\n\001h\030\004 \001(\005" +
      "\0226\n\005video\030\005 \001(\0132\'.TianQiBidRequest.TianQ" +
      "iImp.TianQiVideo\022\020\n\010bidfloor\030\006 \001(\001\022\023\n\013bi" +
      "dfloorcur\030\007 \001(\t\032\201\001\n\013TianQiVideo\022\t\n\001w\030\001 \001" +
      "(\005\022\t\n\001h\030\002 \001(\005\022\023\n\013minduration\030\003 \001(\005\022\023\n\013ma" +
      "xduration\030\004 \001(\005\022\021\n\tmaxlength\030\005 \001(\005\022\r\n\005mi" +
      "mes\030\006 \003(\t\022\020\n\010delivery\030\007 \001(\005\032v\n\tTianQiApp" +
      "\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\016\n\006bundle\030\003 \001" +
      "(\t\022\013\n\003ver\030\004 \001(\t\022\016\n\006domain\030\005 \001(\t\022\020\n\010store" +
      "url\030\006 \001(\t\022\020\n\010keywords\030\007 \001(\t\032\266\t\n\014TianQiDe" +
      "vice\022\n\n\002ip\030\001 \001(\t\022\014\n\004ipv6\030\002 \001(\t\022\n\n\002ua\030\003 \001" +
      "(\t\022\013\n\003dnt\030\004 \001(\005\022(\n\003geo\030\005 \001(\0132\033.TianQiBid" +
      "Request.TianQiGeo\022\017\n\007carrier\030\006 \001(\005\022\016\n\006mc" +
      "cmnc\030\007 \001(\t\022\026\n\016connectiontype\030\010 \001(\005\022\022\n\nde" +
      "vicetype\030\t \001(\005\022\014\n\004make\030\n \001(\t\022\r\n\005brand\030\013 " +
      "\001(\t\022\r\n\005model\030\014 \001(\t\022\023\n\013orientation\030\r \001(\005\022" +
      "\n\n\002os\030\016 \001(\005\022\013\n\003osv\030\017 \001(\t\022\013\n\003osl\030\020 \001(\005\022\t\n" +
      "\001w\030\021 \001(\005\022\t\n\001h\030\022 \001(\005\022\013\n\003ppi\030\023 \001(\005\022\013\n\003dpi\030" +
      "\024 \001(\005\022\017\n\007density\030\025 \001(\002\022\013\n\003hwv\030\026 \001(\t\022\020\n\010l" +
      "anguage\030\027 \001(\t\022\014\n\004idfa\030\030 \001(\t\022\017\n\007idfamd5\030\031" +
      " \001(\t\022\014\n\004idfv\030\032 \001(\t\022\020\n\010openudid\030\033 \001(\t\022\014\n\004" +
      "caid\030\034 \001(\t\022\023\n\013caidversion\030\035 \001(\t\022\014\n\004imei\030" +
      "\036 \001(\t\022\017\n\007imeimd5\030\037 \001(\t\022\021\n\tandroidid\030  \001(" +
      "\t\022\024\n\014androididmd5\030! \001(\t\022\014\n\004oaid\030\" \001(\t\022\017\n" +
      "\007oaidmd5\030# \001(\t\022\014\n\004paid\030$ \001(\t\022\013\n\003mac\030% \001(" +
      "\t\022\016\n\006macmd5\030& \001(\t\022\014\n\004imsi\030\' \001(\t\022\014\n\004ssid\030" +
      "( \001(\t\022\017\n\007wifimac\030) \001(\t\022\016\n\006romver\030* \001(\t\022\030" +
      "\n\020syscompilingtime\030+ \001(\t\022\027\n\017appstorevers" +
      "ion\030, \001(\t\022\020\n\010bootmark\030- \001(\t\022\022\n\nupdatemar" +
      "k\030. \001(\t\022\020\n\010timezone\030/ \001(\t\022\017\n\007country\0300 \001" +
      "(\t\022\021\n\tmodelcode\0301 \001(\t\022\025\n\rdevicenamemd5\0302" +
      " \001(\t\022\027\n\017hardwaremachine\0303 \001(\t\022\025\n\rhardwar" +
      "emodel\0304 \001(\t\022\020\n\010inittime\0305 \001(\t\022\023\n\013startu" +
      "ptime\0306 \001(\t\022\023\n\013upgradetime\0307 \001(\t\022\026\n\016phys" +
      "icalmemory\0308 \001(\t\022\020\n\010harddisk\0309 \001(\t\022\016\n\006cp" +
      "unum\030: \001(\005\022\017\n\007cpufreq\030; \001(\002\022\022\n\nidfapolic" +
      "y\030< \001(\005\022\025\n\rbatterystatus\030= \001(\005\022\024\n\014batter" +
      "ypower\030> \001(\021\022\022\n\nhmsversion\030? \001(\t\022\021\n\tagve" +
      "rsion\030@ \001(\t\022\017\n\007applist\030A \003(\t\022.\n\010caidlist" +
      "\030B \003(\0132\034.TianQiBidRequest.TianQiCaid\032=\n\n" +
      "TianQiCaid\022\014\n\004caid\030\001 \001(\t\022\017\n\007version\030\002 \001(" +
      "\t\022\020\n\010caid_md5\030\003 \001(\t\032T\n\tTianQiGeo\022\013\n\003lat\030" +
      "\001 \001(\002\022\013\n\003lon\030\002 \001(\002\022\017\n\007country\030\003 \001(\t\022\016\n\006r" +
      "egion\030\004 \001(\t\022\014\n\004city\030\005 \001(\t\032Y\n\nTianQiUser\022" +
      "\n\n\002id\030\001 \001(\t\022\020\n\010buyeruid\030\002 \001(\t\022\013\n\003yob\030\003 \001" +
      "(\005\022\016\n\006gender\030\004 \001(\t\022\020\n\010keywords\030\005 \001(\t\"\275\010\n" +
      "\021TianQiBidResponse\022\n\n\002id\030\001 \001(\t\0221\n\007seatbi" +
      "d\030\002 \003(\0132 .TianQiBidResponse.TianQiSeatbi" +
      "d\022\r\n\005bidid\030\003 \001(\t\022\013\n\003cur\030\004 \001(\t\032\314\007\n\rTianQi" +
      "Seatbid\0221\n\003bid\030\001 \003(\0132$.TianQiBidResponse" +
      ".TianQiSeatbid.Bid\032\207\007\n\003Bid\022\n\n\002id\030\001 \001(\t\022\r" +
      "\n\005impid\030\002 \001(\t\022\r\n\005price\030\003 \001(\002\022\014\n\004adid\030\004 \001" +
      "(\t\022\014\n\004crid\030\005 \001(\t\022\013\n\003adm\030\006 \001(\t\022\024\n\014materia" +
      "ltype\030\007 \001(\005\022\027\n\017interactiontype\030\010 \001(\005\022\014\n\004" +
      "icon\030\t \001(\t\022\t\n\001w\030\n \001(\005\022\t\n\001h\030\013 \001(\005\022\016\n\006imag" +
      "es\030\014 \003(\t\022\r\n\005title\030\r \001(\t\022\014\n\004desc\030\016 \001(\t\022\017\n" +
      "\007appname\030\017 \001(\t\022\021\n\tappbundle\030\020 \001(\t\022\017\n\007app" +
      "size\030\021 \001(\005\022\016\n\006source\030\022 \001(\t\022\020\n\010appletid\030\023" +
      " \001(\t\022\022\n\nappletpath\030\024 \001(\t\0229\n\005video\030\025 \001(\0132" +
      "*.TianQiBidResponse.TianQiSeatbid.Bid.Vi" +
      "deo\022\021\n\ttargeturl\030\026 \001(\t\022\023\n\013deeplinkurl\030\027 " +
      "\001(\t\022\025\n\runiversallink\030\030 \001(\t\022>\n\010trackers\030\031" +
      " \003(\0132,.TianQiBidResponse.TianQiSeatbid.B" +
      "id.Tracker\032\340\002\n\005Video\022\020\n\010duration\030\001 \001(\005\022\013" +
      "\n\003url\030\002 \001(\t\022\020\n\010coverurl\030\003 \001(\t\022\t\n\001w\030\004 \001(\005" +
      "\022\t\n\001h\030\005 \001(\005\022\016\n\006length\030\006 \001(\005\022\014\n\004skip\030\007 \001(" +
      "\005\022\023\n\013skipmintime\030\010 \001(\005\022\023\n\013preloadtime\030\t " +
      "\001(\005\022\022\n\nendcardurl\030\n \001(\t\022\023\n\013endcardhtml\030\013" +
      " \001(\t\022\017\n\007endicon\030\014 \001(\t\022\020\n\010endtitle\030\r \001(\t\022" +
      "\017\n\007enddesc\030\016 \001(\t\022\025\n\rendbuttontext\030\017 \001(\t\022" +
      "\024\n\014endbuttonurl\030\020 \001(\t\022>\n\010trackers\030\030 \003(\0132" +
      ",.TianQiBidResponse.TianQiSeatbid.Bid.Tr" +
      "acker\032%\n\007Tracker\022\014\n\004type\030\001 \001(\021\022\014\n\004urls\030\002" +
      " \003(\tB9\n cn.taken.ad.logic.adv.tianqi.dto" +
      "B\023TianQiAdvPeqRespDtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_TianQiBidRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_TianQiBidRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidRequest_descriptor,
        new java.lang.String[] { "Id", "Imp", "App", "Device", "User", "At", "Tmax", });
    internal_static_TianQiBidRequest_TianQiImp_descriptor =
      internal_static_TianQiBidRequest_descriptor.getNestedTypes().get(0);
    internal_static_TianQiBidRequest_TianQiImp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidRequest_TianQiImp_descriptor,
        new java.lang.String[] { "Id", "Tagid", "W", "H", "Video", "Bidfloor", "Bidfloorcur", });
    internal_static_TianQiBidRequest_TianQiImp_TianQiVideo_descriptor =
      internal_static_TianQiBidRequest_TianQiImp_descriptor.getNestedTypes().get(0);
    internal_static_TianQiBidRequest_TianQiImp_TianQiVideo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidRequest_TianQiImp_TianQiVideo_descriptor,
        new java.lang.String[] { "W", "H", "Minduration", "Maxduration", "Maxlength", "Mimes", "Delivery", });
    internal_static_TianQiBidRequest_TianQiApp_descriptor =
      internal_static_TianQiBidRequest_descriptor.getNestedTypes().get(1);
    internal_static_TianQiBidRequest_TianQiApp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidRequest_TianQiApp_descriptor,
        new java.lang.String[] { "Id", "Name", "Bundle", "Ver", "Domain", "Storeurl", "Keywords", });
    internal_static_TianQiBidRequest_TianQiDevice_descriptor =
      internal_static_TianQiBidRequest_descriptor.getNestedTypes().get(2);
    internal_static_TianQiBidRequest_TianQiDevice_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidRequest_TianQiDevice_descriptor,
        new java.lang.String[] { "Ip", "Ipv6", "Ua", "Dnt", "Geo", "Carrier", "Mccmnc", "Connectiontype", "Devicetype", "Make", "Brand", "Model", "Orientation", "Os", "Osv", "Osl", "W", "H", "Ppi", "Dpi", "Density", "Hwv", "Language", "Idfa", "Idfamd5", "Idfv", "Openudid", "Caid", "Caidversion", "Imei", "Imeimd5", "Androidid", "Androididmd5", "Oaid", "Oaidmd5", "Paid", "Mac", "Macmd5", "Imsi", "Ssid", "Wifimac", "Romver", "Syscompilingtime", "Appstoreversion", "Bootmark", "Updatemark", "Timezone", "Country", "Modelcode", "Devicenamemd5", "Hardwaremachine", "Hardwaremodel", "Inittime", "Startuptime", "Upgradetime", "Physicalmemory", "Harddisk", "Cpunum", "Cpufreq", "Idfapolicy", "Batterystatus", "Batterypower", "Hmsversion", "Agversion", "Applist", "Caidlist", });
    internal_static_TianQiBidRequest_TianQiCaid_descriptor =
      internal_static_TianQiBidRequest_descriptor.getNestedTypes().get(3);
    internal_static_TianQiBidRequest_TianQiCaid_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidRequest_TianQiCaid_descriptor,
        new java.lang.String[] { "Caid", "Version", "CaidMd5", });
    internal_static_TianQiBidRequest_TianQiGeo_descriptor =
      internal_static_TianQiBidRequest_descriptor.getNestedTypes().get(4);
    internal_static_TianQiBidRequest_TianQiGeo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidRequest_TianQiGeo_descriptor,
        new java.lang.String[] { "Lat", "Lon", "Country", "Region", "City", });
    internal_static_TianQiBidRequest_TianQiUser_descriptor =
      internal_static_TianQiBidRequest_descriptor.getNestedTypes().get(5);
    internal_static_TianQiBidRequest_TianQiUser_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidRequest_TianQiUser_descriptor,
        new java.lang.String[] { "Id", "Buyeruid", "Yob", "Gender", "Keywords", });
    internal_static_TianQiBidResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_TianQiBidResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidResponse_descriptor,
        new java.lang.String[] { "Id", "Seatbid", "Bidid", "Cur", });
    internal_static_TianQiBidResponse_TianQiSeatbid_descriptor =
      internal_static_TianQiBidResponse_descriptor.getNestedTypes().get(0);
    internal_static_TianQiBidResponse_TianQiSeatbid_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidResponse_TianQiSeatbid_descriptor,
        new java.lang.String[] { "Bid", });
    internal_static_TianQiBidResponse_TianQiSeatbid_Bid_descriptor =
      internal_static_TianQiBidResponse_TianQiSeatbid_descriptor.getNestedTypes().get(0);
    internal_static_TianQiBidResponse_TianQiSeatbid_Bid_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidResponse_TianQiSeatbid_Bid_descriptor,
        new java.lang.String[] { "Id", "Impid", "Price", "Adid", "Crid", "Adm", "Materialtype", "Interactiontype", "Icon", "W", "H", "Images", "Title", "Desc", "Appname", "Appbundle", "Appsize", "Source", "Appletid", "Appletpath", "Video", "Targeturl", "Deeplinkurl", "Universallink", "Trackers", });
    internal_static_TianQiBidResponse_TianQiSeatbid_Bid_Video_descriptor =
      internal_static_TianQiBidResponse_TianQiSeatbid_Bid_descriptor.getNestedTypes().get(0);
    internal_static_TianQiBidResponse_TianQiSeatbid_Bid_Video_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidResponse_TianQiSeatbid_Bid_Video_descriptor,
        new java.lang.String[] { "Duration", "Url", "Coverurl", "W", "H", "Length", "Skip", "Skipmintime", "Preloadtime", "Endcardurl", "Endcardhtml", "Endicon", "Endtitle", "Enddesc", "Endbuttontext", "Endbuttonurl", "Trackers", });
    internal_static_TianQiBidResponse_TianQiSeatbid_Bid_Tracker_descriptor =
      internal_static_TianQiBidResponse_TianQiSeatbid_Bid_descriptor.getNestedTypes().get(1);
    internal_static_TianQiBidResponse_TianQiSeatbid_Bid_Tracker_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_TianQiBidResponse_TianQiSeatbid_Bid_Tracker_descriptor,
        new java.lang.String[] { "Type", "Urls", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
