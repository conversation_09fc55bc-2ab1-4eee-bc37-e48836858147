package cn.taken.ad.logic.adv.mangguo;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.encryption.Base64;
import cn.taken.ad.component.utils.encryption.Md5;
import cn.taken.ad.component.utils.result.Result;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.constant.business.ActionType;
import cn.taken.ad.constant.business.CarrierType;
import cn.taken.ad.constant.business.ConnectionType;
import cn.taken.ad.constant.business.DeviceType;
import cn.taken.ad.constant.business.EventType;
import cn.taken.ad.constant.business.MacroType;
import cn.taken.ad.constant.business.MaterialType;
import cn.taken.ad.constant.business.OsType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.mangguo.dto.MangGuoRequest;
import cn.taken.ad.logic.adv.mangguo.dto.MangGuoRequestAdspace;
import cn.taken.ad.logic.adv.mangguo.dto.MangGuoRequestDevice;
import cn.taken.ad.logic.adv.mangguo.dto.MangGuoRequestUser;
import cn.taken.ad.logic.adv.mangguo.dto.MangGuoResponse;
import cn.taken.ad.logic.adv.mangguo.dto.MangGuoResponseLurl;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestCaidDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestDeviceDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestGeoDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestNetworkDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestUserDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.ParamParser;
import cn.taken.ad.utils.time.TimeUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 芒果
 */
@Component("MANGGUO" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class MangGuoAdvProcessor implements AdvProcessor {

    public static final String TOKEN = "token";
    public static final String API_VERSION = "apiVersion";

    private static final Logger log = LoggerFactory.getLogger(MangGuoAdvProcessor.class);

    @Resource
    private BaseRedisL2Cache baseRedisL2Cache;

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        Map<String, String> accountParam = ParamParser.parseParamByJson(advDto.getPnyParam());
        String apiVersion = accountParam.get(API_VERSION);
        String token = ParamParser.parseParamByJson(advDto.getAppPnyParam()).get(TOKEN);
        if (StringUtils.isEmpty(token)) {
            // 获取 账户级参数
            token = accountParam.get(TOKEN);
        }
        MangGuoRequest request = convertRequest(rtbDto, advDto, apiVersion, token);
        advDto.setReqObj(request);
        String json = JsonHelper.toJsonString(request);
        HttpResult httpResult = httpClient.postJson(advDto.getRtburl(), json, "UTF-8", new Header[]{new BasicHeader("Content-Type", "application/json")}, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        String resp = httpResult.getDataStringUTF8();
        return parseResponse(rtbDto, resp, token, advDto);
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) {
        // 是否有请求成功的
        boolean hasRight = false;

        List<String> urls = reqDto.getUrls();
        if (reqDto.getBiddingSuccess() && null != reqDto.getPrice()) {
            // 宏替换
            // 竟胜 价格加密
            String price;
            try {
                String token = ParamParser.parseParamByJson(reqDto.getAppCustomParam()).get(TOKEN);
                if (StringUtils.isEmpty(token)){
                    token = ParamParser.parseParamByJson(reqDto.getAdvCustomParam()).get(TOKEN);
                }
                if (StringUtils.isEmpty(token)) {
                    return SuperResult.badResult("price token has not found");
                }
                price = encrypt(reqDto.getPrice() + "", token);
                urls = replaceMacro("[AUCTION_PRICE]", urls, price);
            } catch (Exception e) {
                log.error("Error Info:{}", JsonHelper.toJsonString(reqDto), e);
                return SuperResult.badResult("price encrypt fail");
            }
        }
        for (String url : urls) {
            HttpResult result = httpClient.get(url, null, null, -1);
            if (!hasRight) {
                hasRight = result.isSuccess();
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", reqDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return hasRight ? SuperResult.rightResult() : Result.badResult("no right");
    }

    public RtbResponseDto parseResponse(RtbRequestDto rtbDto, String resp, String token, RtbAdvDto advDto) throws Exception {
        MangGuoResponse response;
        try {
            advDto.setRespObj(resp);
            response = JsonHelper.fromJson(MangGuoResponse.class, resp);
        } catch (Exception e) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Serializable resp fail");
        }
        if (200 != response.getError_code()) {
            if (204 == response.getError_code()) {
                return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
            } else {
                return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), response.getError_code() + "");
            }
        }
        if (null == response.getAds() || response.getAds().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", response.getError_code() + "");
        response.getAds().forEach(tag -> {
            TagResponseDto tagResponseDto = new TagResponseDto();
            // type 广告类型	字符串，开屏boot，贴片front，信息流banner等
            // space_id	广告位id
            if (StringUtils.isNotEmpty(tag.getMaterial_url())) {
                tagResponseDto.setImgUrls(new ArrayList<>(Collections.singletonList(tag.getMaterial_url())));
            }
            if (StringUtils.isNotEmpty(tag.getMaterial_type())) {
                if (tag.getMaterial_type().equals("1")) {
                    tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                } else if (tag.getMaterial_type().equals("2")) {
                    tagResponseDto.setMaterialType(MaterialType.VIDEO);
                }
            }
            if (StringUtils.isNotEmpty(tag.getLanding_page_url())) {
                tagResponseDto.setClickUrl(tag.getLanding_page_url());
            }
            // 暂无 schema_url 广告app 唤醒地址
            // 暂无 wx_mini_user 微信小程序原始id
            // 暂无 wx_mini_path 微信小程序路径
            if (null != tag.getClick_action()) {
                if (tag.getClick_action() == 1) {
                    tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
                } else if (tag.getClick_action() == 2) {
                    tagResponseDto.setActionType(ActionType.DOWNLOAD);
                }
            }
            tagResponseDto.setTitle(tag.getTitle());
            tagResponseDto.setDesc(tag.getDesc());
            if (null != tag.getMaterial_width()) {
                tagResponseDto.setMaterialWidth(tag.getMaterial_width());
            }
            if (null != tag.getMaterial_height()) {
                tagResponseDto.setMaterialHeight(tag.getMaterial_height());
            }
            if (StringUtils.isNotEmpty(tag.getIcon_url())) {
                tagResponseDto.setIconUrl(tag.getIcon_url());
            }
            ResponseVideoDto videoDto = new ResponseVideoDto();
            tagResponseDto.setVideoInfo(videoDto);
            if (null != tag.getDuration()) {
                videoDto.setDuration(tag.getDuration());
            }
            if (StringUtils.isNotEmpty(tag.getButton_text())) {
                videoDto.setButtonText(tag.getButton_text());
            }
            if (StringUtils.isNotEmpty(tag.getCover_url())) {
                videoDto.setCoverImgUrls(new ArrayList<>(Collections.singletonList(tag.getCover_url())));
            }
            if (null != tag.getCover_width()) {
                videoDto.setCoverWidth(tag.getCover_width());
            }
            if (null != tag.getCover_height()) {
                videoDto.setCoverHeight(tag.getCover_height());
            }

            List<ResponseTrackDto> tracks = new ArrayList<>();
            tagResponseDto.setTracks(tracks);
            // 监控事件
            List<MangGuoResponseLurl> impUrls = tag.getImpression_urls();
            if (null != impUrls && !impUrls.isEmpty()) {
                impUrls.forEach(impUrl -> {
                    if (null != impUrl.getEvent() && StringUtils.isNotEmpty(impUrl.getUrl())) {
                        switch (impUrl.getEvent()) {
                            case 0:
                                // 曝光
                                tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), new ArrayList<>(Collections.singletonList(impUrl.getUrl()))));
                                break;
                            case 1:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_25.getType(), new ArrayList<>(Collections.singletonList(impUrl.getUrl()))));
                                break;
                            case 2:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_50.getType(), new ArrayList<>(Collections.singletonList(impUrl.getUrl()))));
                                break;
                            case 3:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_75.getType(), new ArrayList<>(Collections.singletonList(impUrl.getUrl()))));
                                break;
                            case 4:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), new ArrayList<>(Collections.singletonList(impUrl.getUrl()))));
                                break;
                            case 5:
                                tracks.add(new ResponseTrackDto(EventType.CLOSE_AD.getType(), new ArrayList<>(Collections.singletonList(impUrl.getUrl()))));
                                break;
                            case 6:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), new ArrayList<>(Collections.singletonList(impUrl.getUrl()))));
                                break;
                            //case 101: // 101 deeplink检测到已安装
                            //case 102: // 102 deeplink检测未安装（未安装调起失败）
                            case 103:
                                // 这里替换 宏 [DPLINK]
                                String url = impUrl.getUrl();
                                if (url.contains("[DPLINK]")) {
                                    url = url.replace("[DPLINK]", "1");
                                }
                                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), new ArrayList<>(Collections.singletonList(url))));
                                break;
                            case 104:
                                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), new ArrayList<>(Collections.singletonList(impUrl.getUrl()))));
                                break;

                        }
                    }
                });
            }
            if (null != tag.getClick_urls() && !tag.getClick_urls().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), tag.getClick_urls()));
            }
            if (null != tag.getDownload_track_urls() && !tag.getDownload_track_urls().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), tag.getDownload_track_urls()));
            }
            if (null != tag.getDownloaded_track_urls() && !tag.getDownloaded_track_urls().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), tag.getDownloaded_track_urls()));
            }
            if (null != tag.getInstalled_track_urls() && !tag.getInstalled_track_urls().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), tag.getInstalled_track_urls()));
            }
            if (null != tag.getOpen_track_urls() && !tag.getOpen_track_urls().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.INSTALLED_OPEN.getType(), tag.getInstalled_track_urls()));
            }
            if (StringUtils.isNotEmpty(tag.getSsp_win_price())) {
                // 价格解密
                String price;
                try {
                    price = decrypt(tag.getSsp_win_price(), token);
                } catch (Exception e) {
                    log.error("decrypt price error,price:{}", tag.getSsp_win_price(), e);
                    return;
                }
                tagResponseDto.setPrice(Double.parseDouble(price));
            }
            if (null != tag.getWin_notices() && !tag.getWin_notices().isEmpty()) {
                tagResponseDto.setWinNoticeUrls(tag.getWin_notices());
            }
            if (null != tag.getLoss_notices() && !tag.getLoss_notices().isEmpty()) {
                tagResponseDto.setFailNoticeUrls(tag.getLoss_notices());
            }
            // 宏替换
            tagResponseDto.getTracks().forEach(track -> {
                List<String> urls = track.getTrackUrls();
                urls = replaceMacro("[CS_D_X]", urls, MacroType.ABS_DOWN_X.getCode());
                urls = replaceMacro("[CS_D_Y]", urls, MacroType.ABS_DOWN_Y.getCode());
                urls = replaceMacro("[CS_A_X]", urls, MacroType.DOWN_X.getCode());
                urls = replaceMacro("[CS_A_Y]", urls, MacroType.DOWN_Y.getCode());
                urls = replaceMacro("[CE_D_X]", urls, MacroType.ABS_UP_X.getCode());
                urls = replaceMacro("[CE_D_Y]", urls, MacroType.ABS_UP_Y.getCode());
                urls = replaceMacro("[CE_A_X]", urls, MacroType.UP_X.getCode());
                urls = replaceMacro("[CE_A_Y]", urls, MacroType.UP_Y.getCode());
                track.setTrackUrls(urls);
            });
            responseDto.getTags().add(tagResponseDto);
        });
        return responseDto;
    }

    /**
     * 参数转换为广告主需要的格式
     */
    private MangGuoRequest convertRequest(RtbRequestDto rtbDto, RtbAdvDto advDto, String apiVersion, String token) throws Exception {
        MangGuoRequest request = new MangGuoRequest();
        Long ts = System.currentTimeMillis();
        String sign = Md5.md5(rtbDto.getReqId() + "_" + ts + "_" + token);
        request.setSign(sign);
        request.setTs(ts);
        request.setMid_id(advDto.getAppCode());
        request.setReq_id(rtbDto.getReqId());
        request.setVersion(apiVersion);
        request.setAdspace(convertRequestAdspace(rtbDto, advDto, token));
        MangGuoRequestUser user = convertRequestUser(rtbDto);
        if (null != user) {
            request.setUser(user);
        }
        request.setDevice(convertRequestDevice(rtbDto));
        return request;
    }

    private MangGuoRequestDevice convertRequestDevice(RtbRequestDto rtbDto) {
        MangGuoRequestDevice request = new MangGuoRequestDevice();
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        RequestNetworkDto networkDto = rtbDto.getNetwork();
        RequestGeoDto geoDto = rtbDto.getGeo();
        RequestAppDto appDto = rtbDto.getApp();

        if (StringUtils.isNotEmpty(deviceDto.getImei())) {
            request.setImei(deviceDto.getImei());
        } else {
            request.setImei("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getImeiMd5())) {
            request.setImei_md5(deviceDto.getImeiMd5().toLowerCase());
        } else {
            request.setImei_md5("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaid())) {
            request.setOaid(deviceDto.getOaid());
        } else {
            request.setOaid("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidId())) {
            request.setAndroid_id(deviceDto.getAndroidId());
        } else {
            request.setAndroid_id("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidIdMd5())) {
            request.setAndroid_id_md5(deviceDto.getAndroidIdMd5().toLowerCase());
        } else {
            request.setAndroid_id_md5("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfa())) {
            request.setIdfa(deviceDto.getIdfa());
        } else {
            request.setIdfa("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getOpenUdId())) {
            request.setOpenudid(deviceDto.getOpenUdId());
        } else {
            request.setOpenudid("");
        }
        request.setCookie(StringUtils.isNotEmpty(deviceDto.getCookie()) ? deviceDto.getCookie() : "");
        if (StringUtils.isNotEmpty(networkDto.getMac())) {
            request.setMac(networkDto.getMac());
        } else {
            request.setMac("");
        }
        boolean isAndroid = false;
        boolean isIos = false;
        OsType osType = deviceDto.getOsType();
        if (null != osType && !osType.equals(OsType.UNKNOWN)) {
            request.setOs(osType.name().toLowerCase());
            isAndroid = osType == OsType.ANDROID || osType == OsType.HARMONY;
            isIos = osType == OsType.IOS;
        }
        if (StringUtils.isNotEmpty(deviceDto.getOsVersion())) {
            String osVersion = "";
            if (isAndroid) {
                osVersion = "Android_";
            } else if (isIos) {
                osVersion = "ios_";
            }
            if (deviceDto.getOsVersion().startsWith("v") || deviceDto.getOsVersion().startsWith("V")) {
                osVersion += deviceDto.getOsVersion().substring(1);
            } else {
                osVersion += deviceDto.getOsVersion();
            }
            request.setOsver(osVersion);
        }
        if (StringUtils.isNotEmpty(deviceDto.getBrand())) {
            request.setBrand(deviceDto.getBrand());
        } else {
            request.setBrand("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getModel())) {
            request.setModel(deviceDto.getModel());
        } else {
            request.setModel("");
        }
        if (null != deviceDto.getWidth()) {
            request.setSw(deviceDto.getWidth());
        } else {
            request.setSw(0);
        }
        if (null != deviceDto.getHeight()) {
            request.setSh(deviceDto.getHeight());
        } else {
            request.setSh(0);
        }
        if (null != deviceDto.getPpi()) {
            request.setPpi(deviceDto.getPpi());
        } else {
            request.setPpi(0);
        }
        request.setIp(networkDto.getIp());
        request.setUa(deviceDto.getUserAgent());
        request.setReferer(StringUtils.isNotEmpty(deviceDto.getReferer()) ? deviceDto.getReferer() : "");
        ConnectionType connectionType = networkDto.getConnectType();
        switch (connectionType) {
            case WIFI:
                request.setConnection_type(1);
                break;
            case NETWORK_2G:
                request.setConnection_type(4);
                break;
            case NETWORK_3G:
                request.setConnection_type(2);
                break;
            case NETWORK_4G:
                request.setConnection_type(3);
                break;
            case NETWORK_5G:
                request.setConnection_type(5);
                break;
        }
        CarrierType carrierType = networkDto.getCarrierType();
        request.setCarrier_name("");
        switch (carrierType) {
            case CM:
                request.setCarrier_type(0);
                request.setCarrier_name("中国移动");
                break;
            case CU:
                request.setCarrier_type(1);
                request.setCarrier_name("中国联通");
                break;
            case CT:
                request.setCarrier_type(3);
                request.setCarrier_name("中国电信");
                break;
            default:
                request.setCarrier_type(-1);
                break;
        }
        // device_type
        DeviceType deviceType = deviceDto.getDeviceType();
        if (null != deviceType && !deviceType.equals(DeviceType.UNKNOWN)) {
            switch (deviceType) {
                case PC:
                    request.setDevice_type(1);
                    break;
                case PHONE:
                    if (isIos) {
                        request.setDevice_type(34);
                    } else if (isAndroid) {
                        request.setDevice_type(32);
                    }
                    break;
                case PAD:
                    if (isIos) {
                        request.setDevice_type(33);
                    } else if (isAndroid) {
                        request.setDevice_type(31);
                    }
                    break;
                //case TV:
            }
        }

        request.setOrientation(0);
        if (null != deviceDto.getOrientation()) {
            if (deviceDto.getOrientation().getType() == 1) {
                request.setOrientation(2);
            } else if (deviceDto.getOrientation().getType() == 2) {
                request.setOrientation(1);
            }
        }
        if (null != geoDto.getLongitude()) {
            request.setLg(geoDto.getLongitude().floatValue());
        } else {
            request.setLg(0.0f);
        }
        if (null != geoDto.getLatitude()) {
            request.setLt(geoDto.getLatitude().floatValue());
        } else {
            request.setLt(0.0f);
        }
        if (StringUtils.isNotEmpty(appDto.getBundle())) {
            request.setPkgname(appDto.getBundle());
        } else {
            request.setPkgname("");
        }
        if (StringUtils.isNotEmpty(appDto.getAppVersion())) {
            request.setApp_version(appDto.getAppVersion());
        } else {
            request.setApp_version("");
        }
        if (StringUtils.isNotEmpty(networkDto.getSsid())) {
            request.setSsid(networkDto.getSsid());
        } else {
            request.setSsid("");
        }
        if (StringUtils.isNotEmpty(networkDto.getWifiMac())) {
            request.setWifi_mac(networkDto.getWifiMac());
        } else {
            request.setWifi_mac("");
        }
        request.setRom_version(StringUtils.isNotEmpty(deviceDto.getRomVersion()) ? deviceDto.getRomVersion() : "");
        Long sysCpmTime = TimeUtils.convertMilliSecond(deviceDto.getSysCompileTime());
        if (null != sysCpmTime) {
            request.setSys_compling_time(sysCpmTime.toString());
        } else if (null != deviceDto.getSysUpdateTime()) {
            request.setSys_compling_time(deviceDto.getSysUpdateTime());
        } else {
            request.setSys_compling_time("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getHardwareMachine())) {
            request.setHardware_machine(deviceDto.getHardwareMachine());
        } else {
            request.setHardware_machine("");
        }
        if (null != deviceDto.getSysStartTime()) {
            // 示例中 字符串时间单位是秒
            Long next = TimeUtils.convertMilliSecond(deviceDto.getSysStartTime());
            if (next != null) {
                request.setStartup_time(String.valueOf(next / 1000));
            }
        } else {
            request.setStartup_time("");
        }
        if (null != deviceDto.getSysUpdateTime()) {
            Long next = TimeUtils.convertMilliSecond(deviceDto.getSysUpdateTime());
            request.setMb_time(TimeUtils.formatUnixTimeWithPrecision(next, false));
        } else {
            request.setMb_time("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getCountry())) {
            request.setCountry_code(deviceDto.getCountry());
        } else {
            request.setCountry_code("");
        }
        if (null != deviceDto.getDeviceMemory()) {
            request.setMem_total(deviceDto.getDeviceMemory());
        } else {
            request.setMem_total(0L);
        }
        if (null != deviceDto.getDeviceHardDisk()) {
            request.setDisk_total(deviceDto.getDeviceHardDisk());
        } else {
            request.setDisk_total(0L);
        }
        if (StringUtils.isNotEmpty(deviceDto.getTimeZone())) {
            request.setLocal_tz_name(deviceDto.getTimeZone());
        } else {
            request.setLocal_tz_name("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getHardwareModel())) {
            request.setHardware_model(deviceDto.getHardwareModel());
        } else if (StringUtils.isNotEmpty(deviceDto.getModel())) {
            request.setHardware_model(deviceDto.getModel());
        } else {
            request.setHardware_model("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getOsVersion())) {
            request.setOs_version(deviceDto.getOsVersion());
        } else {
            request.setOs_version("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getLanguage())) {
            request.setLanguage(deviceDto.getLanguage());
        } else {
            request.setLanguage("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getDeviceName())) {
            request.setPhone_name(Md5.md5(deviceDto.getDeviceName()).toLowerCase());
        } else if (StringUtils.isNotEmpty(deviceDto.getDeviceNameMd5())) {
            request.setPhone_name(deviceDto.getDeviceNameMd5().toLowerCase());
        } else {
            request.setPhone_name("");
        }
        // auth_status 暂无 广告标识授权情况，如“3”（代表authorized），仅ios需要回传，安卓可不填写该字段;
        request.setAuth_status(0); // 暂时固定传0

        if (null != deviceDto.getCpuNum()) {
            request.setCpu_num(deviceDto.getCpuNum());
        } else {
            request.setCpu_num(0);
        }
        String caid = "";
        if (null != deviceDto.getCaids() && !deviceDto.getCaids().isEmpty()) {
            for (RequestCaidDto item : deviceDto.getCaids()) {
                if (StringUtils.isNotEmpty(item.getCaid()) && StringUtils.isNotEmpty(item.getVersion())) {
                    caid = item.getVersion() + "_" + item.getCaid();
                    break;
                }
            }
        }
        request.setIos_caid(caid);
        if (StringUtils.isNotEmpty(deviceDto.getAppStoreVersion())) {
            request.setHms(deviceDto.getAppStoreVersion());
        } else {
            request.setHms("");
        }
        request.setAg(StringUtils.isNotEmpty(deviceDto.getHmsAgVersion()) ? deviceDto.getHmsAgVersion() : "");
        if (isIos) {
            request.setMaker("Apple");
        }
        return request;
    }

    private MangGuoRequestUser convertRequestUser(RtbRequestDto rtbDto) {
        RequestUserDto userDto = rtbDto.getUser();
        if (null != userDto.getAge() || StringUtils.isNotEmpty(userDto.getGender())) {
            MangGuoRequestUser request = new MangGuoRequestUser();
            if (null != userDto.getAge()) {
                request.setAge(userDto.getAge());
            }
            if (StringUtils.isNotEmpty(userDto.getGender())) {
                if (userDto.getGender().equals("M")) {
                    request.setGender(1);
                } else if (userDto.getGender().equals("F")) {
                    request.setGender(2);
                } else {
                    request.setGender(0);
                }
            }
            return request;
        }
        return null;
    }

    private MangGuoRequestAdspace convertRequestAdspace(RtbRequestDto rtbDto, RtbAdvDto advDto, String token) throws Exception {
        MangGuoRequestAdspace request = new MangGuoRequestAdspace();
        request.setAdspace_id(advDto.getTagCode());
        request.setSupport_dl(true);
        //暂时不支持 微信小程序
        request.setSupport_wx(false);
        if (null != rtbDto.getTag().getMaxDuration() && rtbDto.getTag().getMaxDuration() > 0){
            request.setPlay_time(rtbDto.getTag().getMaxDuration());
        }
        request.setAdlen(1);
        request.setWidth(rtbDto.getTag().getWidth());
        request.setHeight(rtbDto.getTag().getHeight());
        // 价格
        if (null != rtbDto.getTag().getPrice() && rtbDto.getTag().getPrice() > 0d) {
            String enPrice = encrypt(rtbDto.getTag().getPrice() + "", token);
            request.setMin_price(enPrice);
        }
        return request;
    }


    private String encrypt(String content, String slatKey) throws Exception {
        return URLEncoder.encode(Base64.encode(Aes.encrypt(content, slatKey)), "UTF-8");
    }

    private String decrypt(String content, String slatKey) throws UnsupportedEncodingException {
        byte[] price = Aes.decrypt(Base64.decode(URLDecoder.decode(content, "UTF-8")), slatKey.getBytes(StandardCharsets.UTF_8));
        return new String(price);
    }

}
