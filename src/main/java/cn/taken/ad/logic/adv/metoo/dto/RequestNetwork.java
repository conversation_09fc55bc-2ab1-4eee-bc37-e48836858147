package cn.taken.ad.logic.adv.metoo.dto;

import java.io.Serializable;

public class RequestNetwork implements Serializable {

    private String ua;//用户设备 HTTP 请求头中的 User-Agent 字段
    private String ip;//用户设备的公网出口 ipv4 地址，点分字符串形式，IP 示例： ***********,
    private String ipv6	;//用户设备的公网出口 ipv6 地址，与 ip 必须存在一个有效值
    private String mac;//	mac 地址
    private String macMd5;//	mac 地址 MD5值
    private String macSha1;//	mac 地址 Sha1值
    private Integer carrier;//	运营商
    private Integer connectionType;//	客户端网络类型:
    private String mcc;//	基站MCC，如:460
    private String mnc;//	基站MNC，如:00
    private String country;//	国家编码(ISO-3166-1/alpha-2)例如：CN
    private Integer coordinateType;//

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpv6() {
        return ipv6;
    }

    public void setIpv6(String ipv6) {
        this.ipv6 = ipv6;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getMacMd5() {
        return macMd5;
    }

    public void setMacMd5(String macMd5) {
        this.macMd5 = macMd5;
    }

    public String getMacSha1() {
        return macSha1;
    }

    public void setMacSha1(String macSha1) {
        this.macSha1 = macSha1;
    }

    public Integer getCarrier() {
        return carrier;
    }

    public void setCarrier(Integer carrier) {
        this.carrier = carrier;
    }

    public Integer getConnectionType() {
        return connectionType;
    }

    public void setConnectionType(Integer connectionType) {
        this.connectionType = connectionType;
    }

    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public String getMnc() {
        return mnc;
    }

    public void setMnc(String mnc) {
        this.mnc = mnc;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getCoordinateType() {
        return coordinateType;
    }

    public void setCoordinateType(Integer coordinateType) {
        this.coordinateType = coordinateType;
    }
}
