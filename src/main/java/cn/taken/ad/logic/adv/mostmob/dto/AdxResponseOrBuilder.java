// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: most_mob_api.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.mostmob.dto;

public interface AdxResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:AdxResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * code 响应码
   * </pre>
   *
   * <code>int32 code = 1;</code>
   * @return The code.
   */
  int getCode();

  /**
   * <pre>
   * message 响应信息
   * </pre>
   *
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <pre>
   * message 响应信息
   * </pre>
   *
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <pre>
   * ad_list 广告列表
   * </pre>
   *
   * <code>repeated .AdxAdInfo ad_list = 3;</code>
   */
  java.util.List<cn.taken.ad.logic.adv.mostmob.dto.AdxAdInfo> 
      getAdListList();
  /**
   * <pre>
   * ad_list 广告列表
   * </pre>
   *
   * <code>repeated .AdxAdInfo ad_list = 3;</code>
   */
  cn.taken.ad.logic.adv.mostmob.dto.AdxAdInfo getAdList(int index);
  /**
   * <pre>
   * ad_list 广告列表
   * </pre>
   *
   * <code>repeated .AdxAdInfo ad_list = 3;</code>
   */
  int getAdListCount();
  /**
   * <pre>
   * ad_list 广告列表
   * </pre>
   *
   * <code>repeated .AdxAdInfo ad_list = 3;</code>
   */
  java.util.List<? extends cn.taken.ad.logic.adv.mostmob.dto.AdxAdInfoOrBuilder> 
      getAdListOrBuilderList();
  /**
   * <pre>
   * ad_list 广告列表
   * </pre>
   *
   * <code>repeated .AdxAdInfo ad_list = 3;</code>
   */
  cn.taken.ad.logic.adv.mostmob.dto.AdxAdInfoOrBuilder getAdListOrBuilder(
      int index);
}
