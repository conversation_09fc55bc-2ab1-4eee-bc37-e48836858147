package cn.taken.ad.logic.adv.tianmai.dto;

import java.io.Serializable;
import java.util.List;

public class ResponseDto implements Serializable {

   private String  request_id;//天脉生成的唯一id，请记录此值方便后续问题排查
   private Integer error_code;//请求响应出错时的错误码，用于问题排查 ，错误码列表详见：错误码对照表
   private List<ResponseAd> adms;//广告清单
   private Long expiration_time;//广告清单过期时间戳，单位秒（保留字段）

    public String getRequest_id() {
        return request_id;
    }

    public void setRequest_id(String request_id) {
        this.request_id = request_id;
    }

    public Integer getError_code() {
        return error_code;
    }

    public void setError_code(Integer error_code) {
        this.error_code = error_code;
    }

    public List<ResponseAd> getAdms() {
        return adms;
    }

    public void setAdms(List<ResponseAd> adms) {
        this.adms = adms;
    }

    public Long getExpiration_time() {
        return expiration_time;
    }

    public void setExpiration_time(Long expiration_time) {
        this.expiration_time = expiration_time;
    }
}
