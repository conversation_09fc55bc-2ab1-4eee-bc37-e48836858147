// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: rta-v1.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.dsp.jiuwei.dto;

public interface RTARequest_1_3OrBuilder extends
    // @@protoc_insertion_point(interface_extends:RTARequest_1_3)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 请求id
   * </pre>
   *
   * <code>string request_id = 1;</code>
   * @return The requestId.
   */
  java.lang.String getRequestId();
  /**
   * <pre>
   * 请求id
   * </pre>
   *
   * <code>string request_id = 1;</code>
   * @return The bytes for requestId.
   */
  com.google.protobuf.ByteString
      getRequestIdBytes();

  /**
   * <pre>
   * 每个rta_id可对应多个账户id
   * </pre>
   *
   * <code>repeated string rta_id = 2;</code>
   * @return A list containing the rtaId.
   */
  java.util.List<java.lang.String>
      getRtaIdList();
  /**
   * <pre>
   * 每个rta_id可对应多个账户id
   * </pre>
   *
   * <code>repeated string rta_id = 2;</code>
   * @return The count of rtaId.
   */
  int getRtaIdCount();
  /**
   * <pre>
   * 每个rta_id可对应多个账户id
   * </pre>
   *
   * <code>repeated string rta_id = 2;</code>
   * @param index The index of the element to return.
   * @return The rtaId at the given index.
   */
  java.lang.String getRtaId(int index);
  /**
   * <pre>
   * 每个rta_id可对应多个账户id
   * </pre>
   *
   * <code>repeated string rta_id = 2;</code>
   * @param index The index of the value to return.
   * @return The bytes of the rtaId at the given index.
   */
  com.google.protobuf.ByteString
      getRtaIdBytes(int index);

  /**
   * <pre>
   * 平台ANDROIDIOS
   * </pre>
   *
   * <code>string os = 3;</code>
   * @return The os.
   */
  java.lang.String getOs();
  /**
   * <pre>
   * 平台ANDROIDIOS
   * </pre>
   *
   * <code>string os = 3;</code>
   * @return The bytes for os.
   */
  com.google.protobuf.ByteString
      getOsBytes();

  /**
   * <code>string idfa = 4;</code>
   * @return The idfa.
   */
  java.lang.String getIdfa();
  /**
   * <code>string idfa = 4;</code>
   * @return The bytes for idfa.
   */
  com.google.protobuf.ByteString
      getIdfaBytes();

  /**
   * <code>string idfa_md5 = 5;</code>
   * @return The idfaMd5.
   */
  java.lang.String getIdfaMd5();
  /**
   * <code>string idfa_md5 = 5;</code>
   * @return The bytes for idfaMd5.
   */
  com.google.protobuf.ByteString
      getIdfaMd5Bytes();

  /**
   * <code>string imei = 6;</code>
   * @return The imei.
   */
  java.lang.String getImei();
  /**
   * <code>string imei = 6;</code>
   * @return The bytes for imei.
   */
  com.google.protobuf.ByteString
      getImeiBytes();

  /**
   * <code>string imei_md5 = 7;</code>
   * @return The imeiMd5.
   */
  java.lang.String getImeiMd5();
  /**
   * <code>string imei_md5 = 7;</code>
   * @return The bytes for imeiMd5.
   */
  com.google.protobuf.ByteString
      getImeiMd5Bytes();

  /**
   * <code>string oaid = 8;</code>
   * @return The oaid.
   */
  java.lang.String getOaid();
  /**
   * <code>string oaid = 8;</code>
   * @return The bytes for oaid.
   */
  com.google.protobuf.ByteString
      getOaidBytes();

  /**
   * <code>string oaid_md5 = 9;</code>
   * @return The oaidMd5.
   */
  java.lang.String getOaidMd5();
  /**
   * <code>string oaid_md5 = 9;</code>
   * @return The bytes for oaidMd5.
   */
  com.google.protobuf.ByteString
      getOaidMd5Bytes();

  /**
   * <code>string android_id = 10;</code>
   * @return The androidId.
   */
  java.lang.String getAndroidId();
  /**
   * <code>string android_id = 10;</code>
   * @return The bytes for androidId.
   */
  com.google.protobuf.ByteString
      getAndroidIdBytes();

  /**
   * <code>string android_id_md5 = 11;</code>
   * @return The androidIdMd5.
   */
  java.lang.String getAndroidIdMd5();
  /**
   * <code>string android_id_md5 = 11;</code>
   * @return The bytes for androidIdMd5.
   */
  com.google.protobuf.ByteString
      getAndroidIdMd5Bytes();

  /**
   * <code>repeated .Caid caid = 12;</code>
   */
  java.util.List<cn.taken.ad.logic.adv.dsp.jiuwei.dto.Caid> 
      getCaidList();
  /**
   * <code>repeated .Caid caid = 12;</code>
   */
  cn.taken.ad.logic.adv.dsp.jiuwei.dto.Caid getCaid(int index);
  /**
   * <code>repeated .Caid caid = 12;</code>
   */
  int getCaidCount();
  /**
   * <code>repeated .Caid caid = 12;</code>
   */
  java.util.List<? extends cn.taken.ad.logic.adv.dsp.jiuwei.dto.CaidOrBuilder> 
      getCaidOrBuilderList();
  /**
   * <code>repeated .Caid caid = 12;</code>
   */
  cn.taken.ad.logic.adv.dsp.jiuwei.dto.CaidOrBuilder getCaidOrBuilder(
      int index);
}
