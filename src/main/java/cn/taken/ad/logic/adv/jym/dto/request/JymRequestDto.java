package cn.taken.ad.logic.adv.jym.dto.request;

import java.io.Serializable;

public class JymRequestDto implements Serializable {
    private static final long serialVersionUID = 7568547888340318835L;

    /**
     * 请求 id，唯⼀标识⼀次⼴告请求；由
     * 媒体侧⽣ 成，请确保全局唯⼀
     */
    private String req_id;

    /**
     * API 版本号
     * 该参数需要做成配置项
     */
    private String api_version;
    /**
     * 应用信息
     */
    private JymRequestAppDto app;
    /**
     * 广告位信息
     */
    private JymRequestSlotDto slot;
    /**
     * 设备信息
     */
    private JymRequestDeviceDto device;
    /**
     * 网络信息
     */
    private JymRequestNetworkDto network;

    public String getReq_id() {
        return req_id;
    }

    public void setReq_id(String req_id) {
        this.req_id = req_id;
    }

    public String getApi_version() {
        return api_version;
    }

    public void setApi_version(String api_version) {
        this.api_version = api_version;
    }

    public JymRequestAppDto getApp() {
        return app;
    }

    public void setApp(JymRequestAppDto app) {
        this.app = app;
    }

    public JymRequestSlotDto getSlot() {
        return slot;
    }

    public void setSlot(JymRequestSlotDto slot) {
        this.slot = slot;
    }

    public JymRequestDeviceDto getDevice() {
        return device;
    }

    public void setDevice(JymRequestDeviceDto device) {
        this.device = device;
    }

    public JymRequestNetworkDto getNetwork() {
        return network;
    }

    public void setNetwork(JymRequestNetworkDto network) {
        this.network = network;
    }
}
