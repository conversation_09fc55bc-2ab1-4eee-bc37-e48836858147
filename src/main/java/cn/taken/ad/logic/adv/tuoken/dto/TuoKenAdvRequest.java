// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: bidrequest.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.tuoken.dto;

public final class TuoKenAdvRequest {
  private TuoKenAdvRequest() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      TuoKenAdvRequest.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Imp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Imp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Imp_Banner_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Imp_Banner_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Imp_Video_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Imp_Video_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Imp_Native_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Imp_Native_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Imp_Pmp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Imp_Pmp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Imp_Pmp_Deal_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Imp_Pmp_Deal_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Imp_SlotExt_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Imp_SlotExt_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Publisher_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Publisher_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Content_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Content_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Content_Producer_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Content_Producer_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Site_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Site_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_App_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_App_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Geo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Geo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Device_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Device_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Device_Caid_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Device_Caid_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_User_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_User_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_User_Data_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_User_Data_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_User_Data_Segment_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_User_Data_Segment_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Regs_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Regs_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Ext_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Ext_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Ext_Macro_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Ext_Macro_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidModel_BidRequest_Ext_Gdt_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidModel_BidRequest_Ext_Gdt_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020bidrequest.proto\022\010BidModel\"\241.\n\nBidRequ" +
      "est\022\n\n\002id\030\001 \001(\t\022%\n\003imp\030\002 \003(\0132\030.BidModel." +
      "BidRequest.Imp\022\'\n\004site\030\003 \001(\0132\031.BidModel." +
      "BidRequest.Site\022%\n\003app\030\004 \001(\0132\030.BidModel." +
      "BidRequest.App\022+\n\006device\030\005 \001(\0132\033.BidMode" +
      "l.BidRequest.Device\022\'\n\004user\030\006 \001(\0132\031.BidM" +
      "odel.BidRequest.User\022\014\n\004test\030\007 \001(\005\022\n\n\002at" +
      "\030\010 \001(\005\022\014\n\004tmax\030\t \001(\005\022\r\n\005wseat\030\n \003(\t\022\017\n\007a" +
      "llimps\030\013 \001(\005\022\013\n\003cur\030\014 \003(\t\022\014\n\004bcat\030\r \003(\t\022" +
      "\014\n\004wcat\030\017 \003(\t\022\014\n\004badv\030\020 \003(\t\022\'\n\004regs\030\021 \001(" +
      "\0132\031.BidModel.BidRequest.Regs\022\020\n\010is_https" +
      "\030\022 \001(\010\022\016\n\006adx_id\030\023 \001(\t\022\020\n\010adx_name\030\024 \001(\t" +
      "\022\025\n\rmedia_version\030\025 \001(\t\022%\n\003ext\030\026 \001(\0132\030.B" +
      "idModel.BidRequest.Ext\032\222\016\n\003Imp\022\n\n\002id\030\001 \001" +
      "(\t\022/\n\006banner\030\002 \001(\0132\037.BidModel.BidRequest" +
      ".Imp.Banner\022-\n\005video\030\003 \001(\0132\036.BidModel.Bi" +
      "dRequest.Imp.Video\022/\n\006native\030\004 \001(\0132\037.Bid" +
      "Model.BidRequest.Imp.Native\022\026\n\016displayma" +
      "nager\030\005 \001(\t\022\031\n\021displaymanagerver\030\006 \001(\t\022\r" +
      "\n\005instl\030\007 \001(\005\022\r\n\005tagid\030\010 \001(\t\022\020\n\010bidfloor" +
      "\030\t \001(\005\022\023\n\013bidfloorcur\030\n \001(\t\022\016\n\006secure\030\013 " +
      "\001(\005\022\024\n\014iframebuster\030\014 \003(\t\022)\n\003pmp\030\r \001(\0132\034" +
      ".BidModel.BidRequest.Imp.Pmp\022\022\n\nallowsty" +
      "le\030\016 \003(\t\022\024\n\014refresh_time\030\017 \001(\t\022\017\n\007ad_typ" +
      "e\030\021 \001(\t\022\017\n\007req_num\030\022 \001(\005\022!\n\031excluded_lan" +
      "ding_page_url\030\023 \003(\t\022\031\n\021excluded_category" +
      "\030\024 \003(\005\022\030\n\020allowed_category\030\025 \003(\005\022\016\n\006pos_" +
      "id\030\026 \003(\005\022\022\n\npage_index\030\027 \001(\005\022\024\n\014ad_slot_" +
      "type\030\032 \001(\t\0222\n\010slot_ext\030\033 \001(\0132 .BidModel." +
      "BidRequest.Imp.SlotExt\032\313\001\n\006Banner\022\t\n\001w\030\001" +
      " \001(\005\022\t\n\001h\030\002 \001(\005\022\014\n\004wmax\030\003 \001(\005\022\014\n\004hmax\030\004 " +
      "\001(\005\022\014\n\004wmin\030\005 \001(\005\022\014\n\004hmin\030\006 \001(\005\022\n\n\002id\030\007 " +
      "\001(\t\022\r\n\005btype\030\010 \003(\005\022\r\n\005battr\030\t \003(\005\022\013\n\003pos" +
      "\030\n \001(\005\022\r\n\005mimes\030\013 \003(\t\022\020\n\010topframe\030\014 \001(\005\022" +
      "\016\n\006expdir\030\r \003(\005\022\013\n\003api\030\016 \003(\005\032\311\002\n\005Video\022\r" +
      "\n\005mimes\030\001 \003(\t\022\023\n\013minduration\030\002 \001(\005\022\023\n\013ma" +
      "xduration\030\003 \001(\005\022\021\n\tprotocols\030\005 \003(\005\022\t\n\001w\030" +
      "\006 \001(\005\022\t\n\001h\030\007 \001(\005\022\022\n\nstartdelay\030\010 \001(\005\022\021\n\t" +
      "linearity\030\t \001(\005\022\020\n\010sequence\030\n \001(\005\022\r\n\005bat" +
      "tr\030\013 \003(\005\022\023\n\013maxextended\030\014 \001(\005\022\022\n\nminbitr" +
      "ate\030\r \001(\005\022\022\n\nmaxbitrate\030\016 \001(\005\022\025\n\rboxinga" +
      "llowed\030\017 \001(\005\022\026\n\016playbackmethod\030\020 \003(\005\022\020\n\010" +
      "delivery\030\021 \003(\005\022\013\n\003pos\030\022 \001(\005\022\013\n\003api\030\023 \003(\005" +
      "\032\366\001\n\006Native\022\017\n\007request\030\001 \001(\t\022\013\n\003ver\030\002 \001(" +
      "\t\022\013\n\003api\030\003 \003(\005\022\r\n\005battr\030\004 \003(\t\022\t\n\001w\030\005 \001(\005" +
      "\022\t\n\001h\030\006 \001(\005\022\n\n\002iw\030\016 \001(\005\022\n\n\002ih\030\017 \001(\005\022\024\n\014n" +
      "ative_field\030\007 \003(\t\022\021\n\ttitle_max\030\010 \001(\005\022\020\n\010" +
      "desc_max\030\t \001(\005\022\022\n\nimage_nums\030\n \001(\005\022\022\n\nal" +
      "lowstyle\030\013 \003(\t\022\r\n\005posid\030\014 \003(\t\022\022\n\npage_in" +
      "dex\030\r \001(\005\032\314\001\n\003Pmp\022\027\n\017private_auction\030\001 \001" +
      "(\005\0220\n\005deals\030\002 \003(\0132!.BidModel.BidRequest." +
      "Imp.Pmp.Deal\032z\n\004Deal\022\n\n\002id\030\001 \001(\t\022\020\n\010bidf" +
      "loor\030\002 \001(\005\022\023\n\013bidfloorcur\030\003 \001(\t\022\n\n\002at\030\004 " +
      "\001(\005\022\r\n\005wseat\030\005 \003(\t\022\020\n\010wadomain\030\006 \003(\t\022\022\n\n" +
      "allowstyle\030\007 \003(\t\032\260\001\n\007SlotExt\022\016\n\006pos_id\030\001" +
      " \001(\t\022\021\n\tslot_type\030\002 \001(\t\022\020\n\010slot_tag\030\003 \001(" +
      "\t\022\020\n\010media_id\030\004 \001(\t\022\022\n\nmedia_type\030\005 \001(\t\022" +
      "\021\n\tmedia_tag\030\006 \001(\t\022\023\n\013settle_type\030\007 \001(\t\022" +
      "\022\n\npublish_id\030\010 \001(\t\022\016\n\006oem_id\030\t \001(\t\032B\n\tP" +
      "ublisher\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\013\n\003ca" +
      "t\030\003 \003(\t\022\016\n\006domain\030\004 \001(\t\032\312\003\n\007Content\022\n\n\002i" +
      "d\030\001 \001(\t\022\017\n\007episode\030\002 \001(\005\022\r\n\005title\030\003 \001(\t\022" +
      "\016\n\006series\030\004 \001(\t\022\016\n\006season\030\005 \001(\t\0227\n\010produ" +
      "cer\030\006 \001(\0132%.BidModel.BidRequest.Content." +
      "Producer\022\013\n\003url\030\007 \001(\t\022\013\n\003cat\030\010 \003(\t\022\024\n\014vi" +
      "deoquality\030\t \001(\005\022\017\n\007context\030\n \001(\005\022\025\n\rcon" +
      "tentrating\030\013 \001(\t\022\022\n\nuserrating\030\014 \001(\t\022\026\n\016" +
      "qagmediarating\030\r \001(\005\022\020\n\010keywords\030\016 \001(\t\022\022" +
      "\n\nlivestream\030\017 \001(\005\022\032\n\022sourcerelationship" +
      "\030\020 \001(\005\022\013\n\003len\030\021 \001(\005\022\020\n\010language\030\022 \001(\t\022\022\n" +
      "\nembeddable\030\023 \001(\005\032A\n\010Producer\022\n\n\002id\030\001 \001(" +
      "\t\022\014\n\004name\030\002 \001(\t\022\013\n\003cat\030\003 \003(\t\022\016\n\006domain\030\004" +
      " \001(\t\032\233\002\n\004Site\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022" +
      "\016\n\006domain\030\003 \003(\t\022\022\n\nsectioncat\030\004 \003(\t\022\017\n\007p" +
      "agecat\030\005 \003(\t\022\014\n\004page\030\006 \001(\t\022\013\n\003ref\030\007 \001(\t\022" +
      "\016\n\006search\030\010 \001(\t\022\016\n\006mobile\030\t \001(\005\022\025\n\rpriva" +
      "cypolicy\030\n \001(\005\0221\n\tpublisher\030\013 \001(\0132\036.BidM" +
      "odel.BidRequest.Publisher\022-\n\007content\030\014 \001" +
      "(\0132\034.BidModel.BidRequest.Content\022\020\n\010keyw" +
      "ords\030\r \001(\t\032\251\002\n\003App\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002" +
      " \001(\t\022\016\n\006bundle\030\003 \001(\t\022\016\n\006domain\030\004 \001(\t\022\020\n\010" +
      "storeurl\030\005 \001(\t\022\013\n\003cat\030\006 \003(\t\022\022\n\nsectionca" +
      "t\030\007 \003(\t\022\017\n\007pagecat\030\010 \003(\t\022\013\n\003ver\030\t \001(\t\022\025\n" +
      "\rprivacypolicy\030\n \001(\005\022\014\n\004paid\030\013 \001(\005\0221\n\tpu" +
      "blisher\030\014 \001(\0132\036.BidModel.BidRequest.Publ" +
      "isher\022-\n\007content\030\r \001(\0132\034.BidModel.BidReq" +
      "uest.Content\022\020\n\010keywords\030\016 \001(\t\032\302\001\n\003Geo\022\013" +
      "\n\003lat\030\001 \001(\002\022\013\n\003lon\030\002 \001(\002\022\014\n\004type\030\003 \001(\005\022\017" +
      "\n\007country\030\004 \001(\t\022\016\n\006region\030\005 \001(\t\022\025\n\rregio" +
      "nfips104\030\006 \001(\t\022\r\n\005metro\030\007 \001(\t\022\014\n\004city\030\010 " +
      "\001(\t\022\013\n\003zip\030\t \001(\t\022\021\n\tutcoffset\030\n \001(\005\022\014\n\004c" +
      "ode\030\014 \001(\t\022\020\n\010province\030\r \001(\t\032\311\014\n\006Device\022\n" +
      "\n\002ua\030\001 \001(\t\022%\n\003geo\030\002 \001(\0132\030.BidModel.BidRe" +
      "quest.Geo\022\013\n\003dnt\030\003 \001(\005\022\013\n\003lmt\030\004 \001(\005\022\n\n\002i" +
      "p\030\005 \001(\t\022\014\n\004ipv6\030\006 \001(\t\022\022\n\ndevicetype\030\007 \001(" +
      "\t\022\014\n\004make\030\010 \001(\t\022\r\n\005brand\030+ \001(\t\022\r\n\005model\030" +
      "\t \001(\t\022\n\n\002os\030\n \001(\t\022\013\n\003osv\030\013 \001(\t\022\013\n\003hwv\030\014 " +
      "\001(\t\022\017\n\007hwmodel\030? \001(\t\022\016\n\006hwname\030@ \001(\t\022\021\n\t" +
      "hwmachine\030A \001(\t\022\t\n\001h\030\r \001(\005\022\t\n\001w\030\016 \001(\005\022\013\n" +
      "\003ppi\030\017 \001(\005\022\017\n\007pxratio\030\020 \001(\002\022\n\n\002js\030\021 \001(\005\022" +
      "\020\n\010flashver\030\022 \001(\t\022\020\n\010language\030\023 \001(\t\022\017\n\007c" +
      "arrier\030\024 \001(\t\022\026\n\016connectiontype\030\025 \001(\t\022\014\n\004" +
      "gaid\030\026 \001(\t\022\014\n\004idfa\030\027 \001(\t\022\020\n\010idfa_md5\030\030 \001" +
      "(\t\022\021\n\tidfa_sha1\030\031 \001(\t\022\014\n\004imei\030\032 \001(\t\022\020\n\010i" +
      "mei_md5\030\033 \001(\t\022\021\n\timei_sha1\030\034 \001(\t\022\022\n\nandr" +
      "oid_id\030\035 \001(\t\022\026\n\016android_id_md5\030\036 \001(\t\022\027\n\017" +
      "android_id_sha1\030\037 \001(\t\022\021\n\tdevice_id\030  \001(\t" +
      "\022\025\n\rdevice_id_md5\030! \001(\t\022\026\n\016device_id_sha" +
      "1\030\" \001(\t\022\013\n\003mac\030# \001(\t\022\017\n\007mac_md5\030$ \001(\t\022\020\n" +
      "\010mac_sha1\030% \001(\t\022\023\n\013orientation\030& \001(\005\022\021\n\t" +
      "open_udid\030\' \001(\t\022\017\n\007referer\030( \001(\t\022\023\n\013devi" +
      "ce_size\030) \001(\t\022\014\n\004oaid\030* \001(\t\022\024\n\014vivostore" +
      "ver\030, \001(\t\022\027\n\017sys_rom_version\030K \001(\t\022\024\n\014sy" +
      "s_rom_name\030V \001(\t\022\035\n\025oppo_appstore_versio" +
      "n\030P \001(\t\022\024\n\014appstore_ver\030T \001(\t\022\014\n\004vaid\030- " +
      "\001(\t\022\023\n\013elapse_time\030. \001(\003\022\014\n\004idfv\030/ \001(\t\022\021" +
      "\n\tphoneName\0300 \001(\t\022\023\n\013bootTimeSec\0301 \001(\t\022\027" +
      "\n\017osUpdateTimeSec\0302 \001(\t\022\022\n\nsyscpmtime\030B " +
      "\001(\t\022\020\n\010diskSize\0303 \001(\005\022\025\n\rbatteryStatus\0304" +
      " \001(\t\022\024\n\014batteryPower\0305 \001(\005\022\022\n\nmemorySize" +
      "\0306 \001(\005\022\021\n\tcpuNumber\0307 \001(\005\022\024\n\014cpuFrequenc" +
      "y\0308 \001(\002\022\021\n\tmodelCode\0309 \001(\t\022\020\n\010timeZone\030:" +
      " \001(\t\022\r\n\005laccu\030; \001(\005\022\014\n\004deny\030< \001(\002\022\030\n\020ver" +
      "sion_code_hms\030= \001(\t\022\027\n\017version_code_ag\030>" +
      " \001(\t\022\014\n\004ssid\030C \001(\t\022\020\n\010wifi_mac\030D \001(\t\022\022\n\n" +
      "authstatus\030R \001(\005\022\021\n\tboot_mark\030G \001(\t\022\023\n\013u" +
      "pdate_mark\030H \001(\t\022\016\n\006as_ver\030I \001(\t\022\013\n\003osl\030" +
      "J \001(\t\022\024\n\014miui_version\030L \001(\t\022/\n\005caids\030M \003" +
      "(\0132 .BidModel.BidRequest.Device.Caid\022\020\n\010" +
      "oaid_md5\030N \001(\t\022\021\n\toaid_sha1\030O \001(\t\022\020\n\010ali" +
      "_aaid\030Q \001(\t\022\014\n\004paid\030S \001(\t\022\017\n\007applist\030U \003" +
      "(\t\032#\n\004Caid\022\017\n\007version\030\001 \001(\t\022\n\n\002id\030\002 \001(\t\032" +
      "\363\002\n\004User\022\n\n\002id\030\001 \001(\t\022\020\n\010buyeruid\030\002 \001(\t\022\013" +
      "\n\003yob\030\003 \001(\005\022\016\n\006gender\030\004 \001(\t\022\020\n\010keywords\030" +
      "\005 \001(\t\022\022\n\ncustomdata\030\006 \001(\t\022%\n\003geo\030\007 \001(\0132\030" +
      ".BidModel.BidRequest.Geo\022,\n\004data\030\010 \003(\0132\036" +
      ".BidModel.BidRequest.User.Data\022\013\n\003age\030\t " +
      "\001(\005\022\n\n\002ip\030\n \001(\t\022\014\n\004tags\030\013 \003(\t\032\215\001\n\004Data\022\n" +
      "\n\002id\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\0227\n\007segment\030\003 \003(" +
      "\0132&.BidModel.BidRequest.User.Data.Segmen" +
      "t\0322\n\007Segment\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\r" +
      "\n\005value\030\003 \001(\t\032\025\n\004Regs\022\r\n\005coppa\030\001 \001(\005\032\221\002\n" +
      "\003Ext\022-\n\005macro\030\001 \001(\0132\036.BidModel.BidReques" +
      "t.Ext.Macro\022)\n\003gdt\030\002 \001(\0132\034.BidModel.BidR" +
      "equest.Ext.Gdt\032\224\001\n\005Macro\022\024\n\014click_down_x" +
      "\030\001 \001(\t\022\024\n\014click_down_y\030\002 \001(\t\022\022\n\nclick_up" +
      "_x\030\003 \001(\t\022\022\n\nclick_up_y\030\004 \001(\t\022\020\n\010click_id" +
      "\030\005 \001(\t\022\021\n\tpos_width\030\006 \001(\t\022\022\n\npos_height\030" +
      "\007 \001(\t\032\031\n\003Gdt\022\022\n\nclick_type\030\001 \001(\005B6\n cn.t" +
      "aken.ad.logic.adv.tuoken.dtoB\020TuoKenAdvR" +
      "equestP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_BidModel_BidRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_BidModel_BidRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_descriptor,
        new java.lang.String[] { "Id", "Imp", "Site", "App", "Device", "User", "Test", "At", "Tmax", "Wseat", "Allimps", "Cur", "Bcat", "Wcat", "Badv", "Regs", "IsHttps", "AdxId", "AdxName", "MediaVersion", "Ext", });
    internal_static_BidModel_BidRequest_Imp_descriptor =
      internal_static_BidModel_BidRequest_descriptor.getNestedTypes().get(0);
    internal_static_BidModel_BidRequest_Imp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Imp_descriptor,
        new java.lang.String[] { "Id", "Banner", "Video", "Native", "Displaymanager", "Displaymanagerver", "Instl", "Tagid", "Bidfloor", "Bidfloorcur", "Secure", "Iframebuster", "Pmp", "Allowstyle", "RefreshTime", "AdType", "ReqNum", "ExcludedLandingPageUrl", "ExcludedCategory", "AllowedCategory", "PosId", "PageIndex", "AdSlotType", "SlotExt", });
    internal_static_BidModel_BidRequest_Imp_Banner_descriptor =
      internal_static_BidModel_BidRequest_Imp_descriptor.getNestedTypes().get(0);
    internal_static_BidModel_BidRequest_Imp_Banner_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Imp_Banner_descriptor,
        new java.lang.String[] { "W", "H", "Wmax", "Hmax", "Wmin", "Hmin", "Id", "Btype", "Battr", "Pos", "Mimes", "Topframe", "Expdir", "Api", });
    internal_static_BidModel_BidRequest_Imp_Video_descriptor =
      internal_static_BidModel_BidRequest_Imp_descriptor.getNestedTypes().get(1);
    internal_static_BidModel_BidRequest_Imp_Video_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Imp_Video_descriptor,
        new java.lang.String[] { "Mimes", "Minduration", "Maxduration", "Protocols", "W", "H", "Startdelay", "Linearity", "Sequence", "Battr", "Maxextended", "Minbitrate", "Maxbitrate", "Boxingallowed", "Playbackmethod", "Delivery", "Pos", "Api", });
    internal_static_BidModel_BidRequest_Imp_Native_descriptor =
      internal_static_BidModel_BidRequest_Imp_descriptor.getNestedTypes().get(2);
    internal_static_BidModel_BidRequest_Imp_Native_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Imp_Native_descriptor,
        new java.lang.String[] { "Request", "Ver", "Api", "Battr", "W", "H", "Iw", "Ih", "NativeField", "TitleMax", "DescMax", "ImageNums", "Allowstyle", "Posid", "PageIndex", });
    internal_static_BidModel_BidRequest_Imp_Pmp_descriptor =
      internal_static_BidModel_BidRequest_Imp_descriptor.getNestedTypes().get(3);
    internal_static_BidModel_BidRequest_Imp_Pmp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Imp_Pmp_descriptor,
        new java.lang.String[] { "PrivateAuction", "Deals", });
    internal_static_BidModel_BidRequest_Imp_Pmp_Deal_descriptor =
      internal_static_BidModel_BidRequest_Imp_Pmp_descriptor.getNestedTypes().get(0);
    internal_static_BidModel_BidRequest_Imp_Pmp_Deal_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Imp_Pmp_Deal_descriptor,
        new java.lang.String[] { "Id", "Bidfloor", "Bidfloorcur", "At", "Wseat", "Wadomain", "Allowstyle", });
    internal_static_BidModel_BidRequest_Imp_SlotExt_descriptor =
      internal_static_BidModel_BidRequest_Imp_descriptor.getNestedTypes().get(4);
    internal_static_BidModel_BidRequest_Imp_SlotExt_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Imp_SlotExt_descriptor,
        new java.lang.String[] { "PosId", "SlotType", "SlotTag", "MediaId", "MediaType", "MediaTag", "SettleType", "PublishId", "OemId", });
    internal_static_BidModel_BidRequest_Publisher_descriptor =
      internal_static_BidModel_BidRequest_descriptor.getNestedTypes().get(1);
    internal_static_BidModel_BidRequest_Publisher_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Publisher_descriptor,
        new java.lang.String[] { "Id", "Name", "Cat", "Domain", });
    internal_static_BidModel_BidRequest_Content_descriptor =
      internal_static_BidModel_BidRequest_descriptor.getNestedTypes().get(2);
    internal_static_BidModel_BidRequest_Content_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Content_descriptor,
        new java.lang.String[] { "Id", "Episode", "Title", "Series", "Season", "Producer", "Url", "Cat", "Videoquality", "Context", "Contentrating", "Userrating", "Qagmediarating", "Keywords", "Livestream", "Sourcerelationship", "Len", "Language", "Embeddable", });
    internal_static_BidModel_BidRequest_Content_Producer_descriptor =
      internal_static_BidModel_BidRequest_Content_descriptor.getNestedTypes().get(0);
    internal_static_BidModel_BidRequest_Content_Producer_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Content_Producer_descriptor,
        new java.lang.String[] { "Id", "Name", "Cat", "Domain", });
    internal_static_BidModel_BidRequest_Site_descriptor =
      internal_static_BidModel_BidRequest_descriptor.getNestedTypes().get(3);
    internal_static_BidModel_BidRequest_Site_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Site_descriptor,
        new java.lang.String[] { "Id", "Name", "Domain", "Sectioncat", "Pagecat", "Page", "Ref", "Search", "Mobile", "Privacypolicy", "Publisher", "Content", "Keywords", });
    internal_static_BidModel_BidRequest_App_descriptor =
      internal_static_BidModel_BidRequest_descriptor.getNestedTypes().get(4);
    internal_static_BidModel_BidRequest_App_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_App_descriptor,
        new java.lang.String[] { "Id", "Name", "Bundle", "Domain", "Storeurl", "Cat", "Sectioncat", "Pagecat", "Ver", "Privacypolicy", "Paid", "Publisher", "Content", "Keywords", });
    internal_static_BidModel_BidRequest_Geo_descriptor =
      internal_static_BidModel_BidRequest_descriptor.getNestedTypes().get(5);
    internal_static_BidModel_BidRequest_Geo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Geo_descriptor,
        new java.lang.String[] { "Lat", "Lon", "Type", "Country", "Region", "Regionfips104", "Metro", "City", "Zip", "Utcoffset", "Code", "Province", });
    internal_static_BidModel_BidRequest_Device_descriptor =
      internal_static_BidModel_BidRequest_descriptor.getNestedTypes().get(6);
    internal_static_BidModel_BidRequest_Device_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Device_descriptor,
        new java.lang.String[] { "Ua", "Geo", "Dnt", "Lmt", "Ip", "Ipv6", "Devicetype", "Make", "Brand", "Model", "Os", "Osv", "Hwv", "Hwmodel", "Hwname", "Hwmachine", "H", "W", "Ppi", "Pxratio", "Js", "Flashver", "Language", "Carrier", "Connectiontype", "Gaid", "Idfa", "IdfaMd5", "IdfaSha1", "Imei", "ImeiMd5", "ImeiSha1", "AndroidId", "AndroidIdMd5", "AndroidIdSha1", "DeviceId", "DeviceIdMd5", "DeviceIdSha1", "Mac", "MacMd5", "MacSha1", "Orientation", "OpenUdid", "Referer", "DeviceSize", "Oaid", "Vivostorever", "SysRomVersion", "SysRomName", "OppoAppstoreVersion", "AppstoreVer", "Vaid", "ElapseTime", "Idfv", "PhoneName", "BootTimeSec", "OsUpdateTimeSec", "Syscpmtime", "DiskSize", "BatteryStatus", "BatteryPower", "MemorySize", "CpuNumber", "CpuFrequency", "ModelCode", "TimeZone", "Laccu", "Deny", "VersionCodeHms", "VersionCodeAg", "Ssid", "WifiMac", "Authstatus", "BootMark", "UpdateMark", "AsVer", "Osl", "MiuiVersion", "Caids", "OaidMd5", "OaidSha1", "AliAaid", "Paid", "Applist", });
    internal_static_BidModel_BidRequest_Device_Caid_descriptor =
      internal_static_BidModel_BidRequest_Device_descriptor.getNestedTypes().get(0);
    internal_static_BidModel_BidRequest_Device_Caid_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Device_Caid_descriptor,
        new java.lang.String[] { "Version", "Id", });
    internal_static_BidModel_BidRequest_User_descriptor =
      internal_static_BidModel_BidRequest_descriptor.getNestedTypes().get(7);
    internal_static_BidModel_BidRequest_User_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_User_descriptor,
        new java.lang.String[] { "Id", "Buyeruid", "Yob", "Gender", "Keywords", "Customdata", "Geo", "Data", "Age", "Ip", "Tags", });
    internal_static_BidModel_BidRequest_User_Data_descriptor =
      internal_static_BidModel_BidRequest_User_descriptor.getNestedTypes().get(0);
    internal_static_BidModel_BidRequest_User_Data_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_User_Data_descriptor,
        new java.lang.String[] { "Id", "Name", "Segment", });
    internal_static_BidModel_BidRequest_User_Data_Segment_descriptor =
      internal_static_BidModel_BidRequest_User_Data_descriptor.getNestedTypes().get(0);
    internal_static_BidModel_BidRequest_User_Data_Segment_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_User_Data_Segment_descriptor,
        new java.lang.String[] { "Id", "Name", "Value", });
    internal_static_BidModel_BidRequest_Regs_descriptor =
      internal_static_BidModel_BidRequest_descriptor.getNestedTypes().get(8);
    internal_static_BidModel_BidRequest_Regs_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Regs_descriptor,
        new java.lang.String[] { "Coppa", });
    internal_static_BidModel_BidRequest_Ext_descriptor =
      internal_static_BidModel_BidRequest_descriptor.getNestedTypes().get(9);
    internal_static_BidModel_BidRequest_Ext_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Ext_descriptor,
        new java.lang.String[] { "Macro", "Gdt", });
    internal_static_BidModel_BidRequest_Ext_Macro_descriptor =
      internal_static_BidModel_BidRequest_Ext_descriptor.getNestedTypes().get(0);
    internal_static_BidModel_BidRequest_Ext_Macro_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Ext_Macro_descriptor,
        new java.lang.String[] { "ClickDownX", "ClickDownY", "ClickUpX", "ClickUpY", "ClickId", "PosWidth", "PosHeight", });
    internal_static_BidModel_BidRequest_Ext_Gdt_descriptor =
      internal_static_BidModel_BidRequest_Ext_descriptor.getNestedTypes().get(1);
    internal_static_BidModel_BidRequest_Ext_Gdt_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidModel_BidRequest_Ext_Gdt_descriptor,
        new java.lang.String[] { "ClickType", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
