// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

public interface ReqVideoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:ReqVideo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int32 w = 1;</code>
   * @return The w.
   */
  int getW();

  /**
   * <code>int32 h = 2;</code>
   * @return The h.
   */
  int getH();

  /**
   * <code>int32 minDuration = 3;</code>
   * @return The minDuration.
   */
  int getMinDuration();

  /**
   * <code>int32 maxDuration = 4;</code>
   * @return The maxDuration.
   */
  int getMaxDuration();

  /**
   * <code>string mines = 5;</code>
   * @return The mines.
   */
  java.lang.String getMines();
  /**
   * <code>string mines = 5;</code>
   * @return The bytes for mines.
   */
  com.google.protobuf.ByteString
      getMinesBytes();

  /**
   * <code>int32 skip = 6;</code>
   * @return The skip.
   */
  int getSkip();

  /**
   * <code>int32 skipAfter = 7;</code>
   * @return The skipAfter.
   */
  int getSkipAfter();

  /**
   * <code>int32 videoType = 8;</code>
   * @return The videoType.
   */
  int getVideoType();
}
