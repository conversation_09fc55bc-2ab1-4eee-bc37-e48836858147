// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: halomobi_ads_entry.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.hailiang.dto;

public interface ExtOrBuilder extends
    // @@protoc_insertion_point(interface_extends:dsp.Ext)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string adslot_id = 1;</code>
   * @return The adslotId.
   */
  String getAdslotId();
  /**
   * <code>string adslot_id = 1;</code>
   * @return The bytes for adslotId.
   */
  com.google.protobuf.ByteString
      getAdslotIdBytes();

  /**
   * <code>string app_id = 2;</code>
   * @return The appId.
   */
  String getAppId();
  /**
   * <code>string app_id = 2;</code>
   * @return The bytes for appId.
   */
  com.google.protobuf.ByteString
      getAppIdBytes();

  /**
   * <code>string pkg = 3;</code>
   * @return The pkg.
   */
  String getPkg();
  /**
   * <code>string pkg = 3;</code>
   * @return The bytes for pkg.
   */
  com.google.protobuf.ByteString
      getPkgBytes();

  /**
   * <code>string device_start_sec = 4;</code>
   * @return The deviceStartSec.
   */
  String getDeviceStartSec();
  /**
   * <code>string device_start_sec = 4;</code>
   * @return The bytes for deviceStartSec.
   */
  com.google.protobuf.ByteString
      getDeviceStartSecBytes();

  /**
   * <code>string country = 5;</code>
   * @return The country.
   */
  String getCountry();
  /**
   * <code>string country = 5;</code>
   * @return The bytes for country.
   */
  com.google.protobuf.ByteString
      getCountryBytes();

  /**
   * <code>string language = 6;</code>
   * @return The language.
   */
  String getLanguage();
  /**
   * <code>string language = 6;</code>
   * @return The bytes for language.
   */
  com.google.protobuf.ByteString
      getLanguageBytes();

  /**
   * <code>string device_name_md5 = 7;</code>
   * @return The deviceNameMd5.
   */
  String getDeviceNameMd5();
  /**
   * <code>string device_name_md5 = 7;</code>
   * @return The bytes for deviceNameMd5.
   */
  com.google.protobuf.ByteString
      getDeviceNameMd5Bytes();

  /**
   * <code>string hardware_machine = 8;</code>
   * @return The hardwareMachine.
   */
  String getHardwareMachine();
  /**
   * <code>string hardware_machine = 8;</code>
   * @return The bytes for hardwareMachine.
   */
  com.google.protobuf.ByteString
      getHardwareMachineBytes();

  /**
   * <code>string physical_memory_byte = 9;</code>
   * @return The physicalMemoryByte.
   */
  String getPhysicalMemoryByte();
  /**
   * <code>string physical_memory_byte = 9;</code>
   * @return The bytes for physicalMemoryByte.
   */
  com.google.protobuf.ByteString
      getPhysicalMemoryByteBytes();

  /**
   * <code>string harddisk_size_byte = 10;</code>
   * @return The harddiskSizeByte.
   */
  String getHarddiskSizeByte();
  /**
   * <code>string harddisk_size_byte = 10;</code>
   * @return The bytes for harddiskSizeByte.
   */
  com.google.protobuf.ByteString
      getHarddiskSizeByteBytes();

  /**
   * <code>string system_update_sec = 11;</code>
   * @return The systemUpdateSec.
   */
  String getSystemUpdateSec();
  /**
   * <code>string system_update_sec = 11;</code>
   * @return The bytes for systemUpdateSec.
   */
  com.google.protobuf.ByteString
      getSystemUpdateSecBytes();

  /**
   * <code>string time_zone = 12;</code>
   * @return The timeZone.
   */
  String getTimeZone();
  /**
   * <code>string time_zone = 12;</code>
   * @return The bytes for timeZone.
   */
  com.google.protobuf.ByteString
      getTimeZoneBytes();

  /**
   * <code>string hms_version = 13;</code>
   * @return The hmsVersion.
   */
  String getHmsVersion();
  /**
   * <code>string hms_version = 13;</code>
   * @return The bytes for hmsVersion.
   */
  com.google.protobuf.ByteString
      getHmsVersionBytes();

  /**
   * <code>string hms_ag_version = 14;</code>
   * @return The hmsAgVersion.
   */
  String getHmsAgVersion();
  /**
   * <code>string hms_ag_version = 14;</code>
   * @return The bytes for hmsAgVersion.
   */
  com.google.protobuf.ByteString
      getHmsAgVersionBytes();

  /**
   * <code>string compiling_time = 15;</code>
   * @return The compilingTime.
   */
  String getCompilingTime();
  /**
   * <code>string compiling_time = 15;</code>
   * @return The bytes for compilingTime.
   */
  com.google.protobuf.ByteString
      getCompilingTimeBytes();

  /**
   * <code>string rom_version = 16;</code>
   * @return The romVersion.
   */
  String getRomVersion();
  /**
   * <code>string rom_version = 16;</code>
   * @return The bytes for romVersion.
   */
  com.google.protobuf.ByteString
      getRomVersionBytes();

  /**
   * <code>string elapse_time = 17;</code>
   * @return The elapseTime.
   */
  String getElapseTime();
  /**
   * <code>string elapse_time = 17;</code>
   * @return The bytes for elapseTime.
   */
  com.google.protobuf.ByteString
      getElapseTimeBytes();
}
