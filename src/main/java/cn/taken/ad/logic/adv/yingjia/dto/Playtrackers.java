package cn.taken.ad.logic.adv.yingjia.dto;

import java.util.List;

public class Playtrackers {
    private List<String> mute; //静音事件上报地址 N
    private List<String> unmute; //关闭静音事件上报地址 N
    private List<String> play; //播放事件上报地址 N
    private List<String> pause; //暂停事件上报地址 N
    private List<String> replay; //重新播放事件上报地址 N
    private List<String> fullscreen; //全屏事件上报地址 N
    private List<String> unfullscreen; //退出全屏事件上报地址 N
    private List<String> upscroll; //上滑事件上报地址 N
    private List<String> downscroll; //下滑事件上报地址 N

    public List<String> getMute() {
        return mute;
    }

    public void setMute(List<String> mute) {
        this.mute = mute;
    }

    public List<String> getUnmute() {
        return unmute;
    }

    public void setUnmute(List<String> unmute) {
        this.unmute = unmute;
    }

    public List<String> getPlay() {
        return play;
    }

    public void setPlay(List<String> play) {
        this.play = play;
    }

    public List<String> getPause() {
        return pause;
    }

    public void setPause(List<String> pause) {
        this.pause = pause;
    }

    public List<String> getReplay() {
        return replay;
    }

    public void setReplay(List<String> replay) {
        this.replay = replay;
    }

    public List<String> getFullscreen() {
        return fullscreen;
    }

    public void setFullscreen(List<String> fullscreen) {
        this.fullscreen = fullscreen;
    }

    public List<String> getUnfullscreen() {
        return unfullscreen;
    }

    public void setUnfullscreen(List<String> unfullscreen) {
        this.unfullscreen = unfullscreen;
    }

    public List<String> getUpscroll() {
        return upscroll;
    }

    public void setUpscroll(List<String> upscroll) {
        this.upscroll = upscroll;
    }

    public List<String> getDownscroll() {
        return downscroll;
    }

    public void setDownscroll(List<String> downscroll) {
        this.downscroll = downscroll;
    }
}
