// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

public interface ReqUserOrBuilder extends
    // @@protoc_insertion_point(interface_extends:ReqUser)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string userId = 1;</code>
   * @return The userId.
   */
  java.lang.String getUserId();
  /**
   * <code>string userId = 1;</code>
   * @return The bytes for userId.
   */
  com.google.protobuf.ByteString
      getUserIdBytes();

  /**
   * <code>string buyerId = 2;</code>
   * @return The buyerId.
   */
  java.lang.String getBuyerId();
  /**
   * <code>string buyerId = 2;</code>
   * @return The bytes for buyerId.
   */
  com.google.protobuf.ByteString
      getBuyerIdBytes();

  /**
   * <code>string tags = 3;</code>
   * @return The tags.
   */
  java.lang.String getTags();
  /**
   * <code>string tags = 3;</code>
   * @return The bytes for tags.
   */
  com.google.protobuf.ByteString
      getTagsBytes();

  /**
   * <code>.MobReqGenderType gender = 4;</code>
   * @return The enum numeric value on the wire for gender.
   */
  int getGenderValue();
  /**
   * <code>.MobReqGenderType gender = 4;</code>
   * @return The gender.
   */
  cn.taken.ad.logic.adv.yaya.dto.MobReqGenderType getGender();

  /**
   * <code>int32 age = 5;</code>
   * @return The age.
   */
  int getAge();
}
