// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

public interface RespNativeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:RespNative)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string title = 1;</code>
   * @return The title.
   */
  java.lang.String getTitle();
  /**
   * <code>string title = 1;</code>
   * @return The bytes for title.
   */
  com.google.protobuf.ByteString
      getTitleBytes();

  /**
   * <code>string desc = 2;</code>
   * @return The desc.
   */
  java.lang.String getDesc();
  /**
   * <code>string desc = 2;</code>
   * @return The bytes for desc.
   */
  com.google.protobuf.ByteString
      getDescBytes();

  /**
   * <code>repeated .ResImage images = 3;</code>
   */
  java.util.List<cn.taken.ad.logic.adv.yaya.dto.ResImage> 
      getImagesList();
  /**
   * <code>repeated .ResImage images = 3;</code>
   */
  cn.taken.ad.logic.adv.yaya.dto.ResImage getImages(int index);
  /**
   * <code>repeated .ResImage images = 3;</code>
   */
  int getImagesCount();
  /**
   * <code>repeated .ResImage images = 3;</code>
   */
  java.util.List<? extends cn.taken.ad.logic.adv.yaya.dto.ResImageOrBuilder> 
      getImagesOrBuilderList();
  /**
   * <code>repeated .ResImage images = 3;</code>
   */
  cn.taken.ad.logic.adv.yaya.dto.ResImageOrBuilder getImagesOrBuilder(
      int index);

  /**
   * <code>string videoUrl = 4;</code>
   * @return The videoUrl.
   */
  java.lang.String getVideoUrl();
  /**
   * <code>string videoUrl = 4;</code>
   * @return The bytes for videoUrl.
   */
  com.google.protobuf.ByteString
      getVideoUrlBytes();

  /**
   * <code>int32 videoDuration = 5;</code>
   * @return The videoDuration.
   */
  int getVideoDuration();

  /**
   * <code>string cover = 6;</code>
   * @return The cover.
   */
  java.lang.String getCover();
  /**
   * <code>string cover = 6;</code>
   * @return The bytes for cover.
   */
  com.google.protobuf.ByteString
      getCoverBytes();
}
