package cn.taken.ad.logic.adv.jym.dto.response;

import java.io.Serializable;
import java.util.List;

public class JymVideoEndDto implements Serializable {
    private static final long serialVersionUID = 7741561508095265810L;

    /**
     * 如果该字段内容存在，播放完成后使用 webview 打开该 html 页面
     */
    private String end_html;
    /**
     * 当用户关闭 end_html 落地页之后发送监控
     */
    private List<String> end_html_close_monitors;
    /**
     * 当展示 end_html 之后发送监控
     */
    private List<String> end_html_imp_monitors;
    /**
     * 后贴内容标题
     */
    private String end_title;
    /**
     * 后贴内容描述
     */
    private String end_desc;
    /**
     * 后贴 logo 地址
     */
    private String end_icon_url;
    /**
     * 后贴推广图地址
     */
    private String end_cover_url;
    /**
     * 后贴点击动作文案
     */
    private String end_action_text;
    /**
     * 后贴点击落地页
     */
    private String end_click_url;
    /**
     * 后贴内容评分
     */
    private Float end_rating;
    /**
     * 后贴内容评论数
     */
    private Integer end_rating_count;

    public String getEnd_html() {
        return end_html;
    }

    public void setEnd_html(String end_html) {
        this.end_html = end_html;
    }

    public List<String> getEnd_html_close_monitors() {
        return end_html_close_monitors;
    }

    public void setEnd_html_close_monitors(List<String> end_html_close_monitors) {
        this.end_html_close_monitors = end_html_close_monitors;
    }

    public List<String> getEnd_html_imp_monitors() {
        return end_html_imp_monitors;
    }

    public void setEnd_html_imp_monitors(List<String> end_html_imp_monitors) {
        this.end_html_imp_monitors = end_html_imp_monitors;
    }

    public String getEnd_title() {
        return end_title;
    }

    public void setEnd_title(String end_title) {
        this.end_title = end_title;
    }

    public String getEnd_desc() {
        return end_desc;
    }

    public void setEnd_desc(String end_desc) {
        this.end_desc = end_desc;
    }

    public String getEnd_icon_url() {
        return end_icon_url;
    }

    public void setEnd_icon_url(String end_icon_url) {
        this.end_icon_url = end_icon_url;
    }

    public String getEnd_cover_url() {
        return end_cover_url;
    }

    public void setEnd_cover_url(String end_cover_url) {
        this.end_cover_url = end_cover_url;
    }

    public String getEnd_action_text() {
        return end_action_text;
    }

    public void setEnd_action_text(String end_action_text) {
        this.end_action_text = end_action_text;
    }

    public String getEnd_click_url() {
        return end_click_url;
    }

    public void setEnd_click_url(String end_click_url) {
        this.end_click_url = end_click_url;
    }

    public Float getEnd_rating() {
        return end_rating;
    }

    public void setEnd_rating(Float end_rating) {
        this.end_rating = end_rating;
    }

    public Integer getEnd_rating_count() {
        return end_rating_count;
    }

    public void setEnd_rating_count(Integer end_rating_count) {
        this.end_rating_count = end_rating_count;
    }
}
