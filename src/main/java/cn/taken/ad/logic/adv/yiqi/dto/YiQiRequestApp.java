package cn.taken.ad.logic.adv.yiqi.dto;

import java.util.List;

public class YiQiRequestApp {

    /**
     * 应用ID
     */
    private String appId;
    /**
     * APP 名称。如 有道词典
     */
    private String appName;
    /**
     * APP 包名。如 com.yiqi.newpiflow
     */
    private String pkg;
    /**
     * APP 版本号，来源于 manifest 的 versionName，而不是versionCode。如 3.5.6
     */
    private String appVer;
    /**
     * 应用程序版本号，来源于 manifest 的 versionCode。如 119
     */
    private String appVerCode;
    /**
     * 设备 App 应用包名集合
     */
    private List<String> apps;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getPkg() {
        return pkg;
    }

    public void setPkg(String pkg) {
        this.pkg = pkg;
    }

    public String getAppVer() {
        return appVer;
    }

    public void setAppVer(String appVer) {
        this.appVer = appVer;
    }

    public String getAppVerCode() {
        return appVerCode;
    }

    public void setAppVerCode(String appVerCode) {
        this.appVerCode = appVerCode;
    }

    public List<String> getApps() {
        return apps;
    }

    public void setApps(List<String> apps) {
        this.apps = apps;
    }
}
