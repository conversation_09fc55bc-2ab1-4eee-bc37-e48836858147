package cn.taken.ad.logic.adv.dsp.jiuwei;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.configuration.dsp.DspConfiguration;
import cn.taken.ad.constant.business.ActionType;
import cn.taken.ad.constant.business.MacroType;
import cn.taken.ad.constant.business.MaterialType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.dto.business.dsp.DspTrackDefine;
import cn.taken.ad.core.pojo.advertiser.AdvertiserTag;
import cn.taken.ad.core.pojo.dsp.ad.DspAdvertiserAccount;
import cn.taken.ad.core.pojo.dsp.ad.DspAdvertiserAd;
import cn.taken.ad.core.pojo.dsp.ad.DspAdvertiserResource;
import cn.taken.ad.core.pojo.dsp.app.DspAdvertiserApp;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.dsp.jiuwei.dto.Caid;
import cn.taken.ad.logic.adv.dsp.jiuwei.dto.RTARequest_1_3;
import cn.taken.ad.logic.adv.dsp.jiuwei.dto.RTAResponse_1_3;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.*;
import cn.taken.ad.utils.ListUtils;
import com.google.gson.reflect.TypeToken;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Component("DSPRTA" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class DspRtaAdvProcessor implements AdvProcessor {
    @Resource
    private BaseRedisL2Cache baseRedisL2Cache;
    @Resource
    private DspConfiguration dspConfiguration;

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        String resourceHost = dspConfiguration.getResourceDomain();
        String downloadHost = dspConfiguration.getDownloadDomain();
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        advDto.setRespObj(responseDto);
        AdvertiserTag adTag = baseRedisL2Cache.get(BaseRedisKeys.KV_ADVERTISER_TAG_ID_ + advDto.getTagId(), AdvertiserTag.class);
        if (adTag == null) {
            return responseDto;
        }
        if (adTag.getAccountId() == null) {
            //自有DSP需要账户
            return responseDto;
        }
        DspAdvertiserAccount account = baseRedisL2Cache.get(BaseRedisKeys.KV_DSP_ADV_ACCOUNT_ID_ + adTag.getAccountId(), DspAdvertiserAccount.class);
        if (account == null) {
            return responseDto;
        }
        //账户限额
        if (account.getMaxDayAmount() != null) {
            Integer flag = baseRedisL2Cache.get(BaseRedisKeys.KV_DSP_ADV_ACCOUNT_DAY_LIMIT_ID_ + account.getId(), Integer.class);
            if (flag != null && flag == 1) {
                return new RtbResponseDto(LogicState.ERROR_DSP_DAY_ACCOUNT_LIMIT.getCode(), LogicState.ERROR_DSP_DAY_ACCOUNT_LIMIT.getDesc());
            }
        }
        String[] codes = baseRedisL2Cache.get(BaseRedisKeys.KV_DSP_ADV_ACCOUNT_ID_CREATIVE_IDS_ + adTag.getAccountId(), String[].class);
        if (codes == null || codes.length == 0) {
            return responseDto;
        }
        List<DspAdvertiserAd> tempList = new LinkedList<>();
        int hour = LocalTime.now().getHour();
        for (String code : codes) {
            DspAdvertiserAd ad = baseRedisL2Cache.get(BaseRedisKeys.KV_DSP_ADVERTISER_AD_CODE_ + code, DspAdvertiserAd.class);
            DspAdvertiserApp app = baseRedisL2Cache.get(BaseRedisKeys.KV_DSP_ADVERTISER_APP_ID_ + ad.getAppId(), DspAdvertiserApp.class);
            if (app == null || app.getType() == null || rtbDto.getDevice().getOsType() == null || app.getType().intValue() != rtbDto.getDevice().getOsType().getType()) {
                continue;
            }
            if (ad != null && ad.getAdType() != null && adTag.getType().intValue() == ad.getAdType()) { //要匹配上预算的广告位类型
                //时间限制
                if (ad.getStartTime() != null && hour < ad.getStartTime()) {
                    continue;
                }
                if (ad.getEndTime() != null && hour > ad.getEndTime()) {
                    continue;
                }
                Integer flag = -1;
                if (ad.getDefaultAutoCloseRuleJson() != null || account.getDefaultAutoCloseAdRuleJson() != null) {
                    //检查事件限额标记
                    flag = baseRedisL2Cache.get(BaseRedisKeys.KV_DSP_ADV_CREATIVE_EVENT_LIMIT_ + ad.getId(), Integer.class);
                    if (flag != null && flag == 1) {
                        continue;
                    }
                }
                if (ad.getMaxDayAmount() != null) { //开启日限额的
                    //检查消耗限额标记
                    flag = baseRedisL2Cache.get(BaseRedisKeys.KV_DSP_ADV_CREATIVE_DAY_LIMIT_CODE_ + code, Integer.class);
                    if (flag != null && flag == 1) {
                        continue;
                    }
                }
                tempList.add(ad);
            }
        }
        if (tempList.isEmpty()) {
            //没有符合条件的创意
            return responseDto;
        }
        //创意竞价
        List<Map<Double, String>> priceList = new LinkedList<>();
        tempList.forEach(v -> {
            Map<Double, String> tmp = new HashMap<>();
            Double price = null;
            if (v.getBidPriceType() == 1) { //涨幅模式
                if (rtbDto.getTag().getPrice() == null) {
                    price = new BigDecimal(1 * (1 + (v.getBidRisesRatio() / 100d))).setScale(4, RoundingMode.HALF_UP).doubleValue();
                } else {
                    price = new BigDecimal(rtbDto.getTag().getPrice() * (1 + (v.getBidRisesRatio() / 100d))).setScale(4, RoundingMode.HALF_UP).doubleValue();
                }
            } else { //固价模式->分
                price = v.getFixedPrice() * 100;
            }
            if (price != null) {
                tmp.put(price, v.getAdId());
                priceList.add(tmp);
            }
        });
        if (priceList.isEmpty()) {
            //没有找到出价信息
            return responseDto;
        }
        //计算创意的最高出价
        Double maxPrice = Collections.max(priceList.stream().map(v -> Collections.max(v.keySet())).collect(Collectors.toList()));
        //获得最高出价的创意集合
        List<String> maxPriceCodes = priceList.stream().filter(v -> v.get(maxPrice) != null).map(v -> v.get(maxPrice)).collect(Collectors.toList());
        //没有找到出价信息
        if (maxPriceCodes.isEmpty()) {
            return responseDto;
        }
        //随机取一个出价最高的创意
        String adId = maxPriceCodes.get((int) (Math.random() * maxPriceCodes.size()));

        advDto.setCreativeId(adId);
        DspAdvertiserAd ad = baseRedisL2Cache.get(BaseRedisKeys.KV_DSP_ADVERTISER_AD_CODE_ + adId, DspAdvertiserAd.class);
        if (ad == null || ad.getStatus() == null || ad.getStatus() != 1) {
            return responseDto;
        }
        //检查APP是否存在
        DspAdvertiserApp app = baseRedisL2Cache.get(BaseRedisKeys.KV_DSP_ADVERTISER_APP_ID_ + ad.getAppId(), DspAdvertiserApp.class);
        if (app == null) {
            return responseDto;
        }
        resourceHost = ad.getMaterialHttps() != null && ad.getMaterialHttps() ? "https://" + resourceHost : "http://" + resourceHost;
        downloadHost = ad.getMaterialHttps() != null && ad.getMaterialHttps() ? "https://" + downloadHost : "http://" + downloadHost;
        boolean fill = false;
        if (account.getRtaOpen()) {
            RTARequest_1_3 request = createRtaRequest(rtbDto, ad, account);
            advDto.setReqObj(request);
            byte[] bytes = request.toByteArray();
            HttpResult result = httpClient.postBytes(advDto.getRtburl(), bytes, new Header[]{new BasicHeader("Content-Type", "application/x-protobuf")}, advDto.getTimeout());
            if (result.isSuccess()) {
                RTAResponse_1_3 response13 = RTAResponse_1_3.parseFrom(result.getData());
                advDto.setRespObj(response13);
                if (0 == response13.getCode()) {
                    List<RTAResponse_1_3.Result> list = response13.getResultList();
                    for (RTAResponse_1_3.Result r : list) {
                        if (r.getRtaId().equals(account.getRtaToken())) {
                            fill = r.getIsDeliver();
                            break;
                        }
                    }
                } else {
                    return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), response13.getMsg());
                }
            } else {
                advDto.setRespObj(result.getStatusLine().toString());
                return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), result.getStatusLine().toString());
            }
        } else {
            fill = true;
        }
        if (fill) {
            List<DspAdvertiserResource> resources = new ArrayList<>();
            if (StringUtils.isNotBlank(ad.getResourceIds())) {
                List<Long> ids = JsonHelper.fromJson(new TypeToken<List<Long>>() {
                }, ad.getResourceIds());
                for (Long id : ids) {
                    DspAdvertiserResource resource = baseRedisL2Cache.get(BaseRedisKeys.KV_DSP_ADVERTISER_RESOURCE_ID_ + id, DspAdvertiserResource.class);
                    if (resource != null) {
                        DspAdvertiserResource copy = copyResource(resource);
                        //映射地址
                        copy.setFileUrl(resourceHost + "/" + copy.getFileUrl());
                        resources.add(copy);
                    }
                }
                if (CollectionUtils.isEmpty(resources)) {
                    return responseDto;
                }
            }
            RequestTagDto tagDto = rtbDto.getTag();
            RequestDeviceDto deviceDto = rtbDto.getDevice();
            RequestNetworkDto networkDto = rtbDto.getNetwork();
            RequestGeoDto geoDto = rtbDto.getGeo();
            TagResponseDto dto = new TagResponseDto();
            if (adTag.getSettlementType() == 1) {//竞价模式
                //上浮出价
                Double dspPrice = null;
                Double price = tagDto.getPrice();
                if (ad.getBidPriceType() != null) {
                    if (ad.getBidPriceType() == 1) {
                        //dsp涨幅
                        if (ad.getBidRisesRatio() != null) {
                            dspPrice = new BigDecimal(price * (1 + (ad.getBidRisesRatio() / 100d))).setScale(4, RoundingMode.HALF_UP).doubleValue();
                        }
                    } else {
                        //使用固价格 -> 分
                        dspPrice = ad.getFixedPrice() * 100;
                    }
                }
                price = dspPrice;
                if (ad.getPriceCpm() == null) { //素材没有最高曝光出价,按上浮出价
                    dto.setPrice(price);
                } else {
                    //底价->换成分
                    Double cmp = ad.getPriceCpm() * 100;
                    //不管是使用固定价格还是涨幅,上浮出价高于最高曝光出价,返回无填充
                    if (price != null && cmp.compareTo(price) < 0) {
                        return responseDto;
                    }
                    //返回底价
                    dto.setPrice(price);
                }
            } else {
                //分成不出价
            }
            dto.setCreativeId(ad.getAdId());
            dto.setTitle(ad.getTitle());
            if (Objects.equals(ad.getTagType(), MaterialType.HTML.getType())) {
                dto.setHtmlContent(ad.getContent());
            } else {
                dto.setDesc(ad.getContent());
            }
            dto.setActionType(ActionType.parseType(ad.getActionType()));
            dto.setMaterialType(MaterialType.parseType(ad.getTagType()));
            //下载类广告
            if (StringUtils.isNotBlank(app.getDownloadUrl()) && ad.getType() == 1) {
                dto.setClickUrl(downloadHost + "/" + app.getDownloadUrl());
            } else {
                dto.setClickUrl(ad.getH5Url());
            }
            dto.setDeepLinkUrl(ad.getDeepLinkUrl());
            dto.setUniversalLink(ad.getUniversalLink());
            if (StringUtils.isNotBlank(ad.getLogoUrl())) {
                dto.setLogoUrl(resourceHost + "/" + ad.getLogoUrl());
            }
            //图片资源
            List<DspAdvertiserResource> imageResource = resources.stream().filter(v -> v.getType() == 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(imageResource)) {
                DspAdvertiserResource img = imageResource.get(0);
                dto.setMaterialHeight(img.getHeight());
                dto.setMaterialWidth(img.getWidth());
                dto.setImgUrls(new LinkedList<>(imageResource.stream().map(DspAdvertiserResource::getFileUrl).collect(Collectors.toList())));
            }
            //视频资源
            List<DspAdvertiserResource> videos = resources.stream().filter(v -> v.getType() == 2).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(videos)) {
                int i = (int) (Math.random() * videos.size());
                DspAdvertiserResource video = videos.get(i);
                ResponseVideoDto videoDto = new ResponseVideoDto();
                videoDto.setVideoUrl(video.getFileUrl());
                videoDto.setVideoHeight(video.getHeight());
                videoDto.setVideoWidth(video.getWidth());
                dto.setMaterialHeight(video.getHeight());
                dto.setMaterialWidth(video.getWidth());
                if (video.getVideoSize() != null) {
                    videoDto.setVideoSize(video.getVideoSize().longValue());
                }
                videoDto.setDuration(video.getDuration());
                if (StringUtils.isNotBlank(ad.getCoverUrl())) {
                    videoDto.setCoverImgUrls(ListUtils.asList(resourceHost + "/" + ad.getCoverUrl()));
                }
                dto.setVideoInfo(videoDto);
            }
            //app信息
            ResponseAppDto appDto = new ResponseAppDto();
            appDto.setAppVersion(app.getAppVersion());
            appDto.setAppName(app.getAppName());
            appDto.setPackageName(app.getAppBundle());
            appDto.setAppPrivacyUrl(app.getPrivacyUrl());
            appDto.setAppPermissionInfoUrl(app.getPermissionInfoUrl());
            dto.setAppInfo(appDto);
            //小程序
            if (Objects.equals(ad.getActionType(), ActionType.MINI_PROGRAM.getType())) {
                ResponseMiniProgramDto miniProgramDto = new ResponseMiniProgramDto();
                miniProgramDto.setId(ad.getMiniAppId());
                miniProgramDto.setName(ad.getMiniAppName());
                miniProgramDto.setPath(ad.getMiniAppPath());
                dto.setMiniProgram(miniProgramDto);
            }
            //tracks
            List<ResponseTrackDto> trackDtoList = new LinkedList<>();
            String trackJson = account.getTrackUrlJson();
            DspTrackDefine[] defines = null;
            if (StringUtils.isNotBlank(trackJson)) {
                defines = JsonHelper.fromJson(DspTrackDefine[].class, trackJson);
            }
            if (defines != null) {
                for (DspTrackDefine def : defines) {
                    if (StringUtils.isBlank(def.getUrl())) {
                        continue;
                    }
                    if (def.getTrackType() == null) {
                        continue;
                    }
                    trackDtoList.add(new ResponseTrackDto(def.getTrackType(), ListUtils.asList(def.getUrl())));
                }
            }
            //替换宏
            trackDtoList.forEach(track -> {
                List<String> urls = track.getTrackUrls();
                urls = replaceMacro("__REQUEST_ID__", urls, rtbDto.getReqId());
                urls = replaceMacro("__ADV_ID__", urls, account.getAccountId());
                urls = replaceMacro("__PLAN_ID__", urls, account.getAccountId());
                urls = replaceMacro("__UNIT_ID__", urls, account.getAccountId());
                urls = replaceMacro("__IDEA_ID__", urls, ad.getAdId());
                urls = replaceMacro("__TS__", urls, MacroType.TIME.getCode());
                if (deviceDto.getOsType() != null) {
                    urls = replaceMacro("__OS__", urls, deviceDto.getOsType().getCode());
                }
                if (StringUtils.isNotBlank(deviceDto.getOsVersion())) {
                    urls = replaceMacro("__OS_VERSION__", urls, deviceDto.getOsVersion());
                }
                if (StringUtils.isNotBlank(deviceDto.getVendor())) {
                    urls = replaceMacro("__VENDOR__", urls, deviceDto.getVendor());
                }
                if (StringUtils.isNotBlank(deviceDto.getBrand())) {
                    urls = replaceMacro("__BRAND__", urls, deviceDto.getBrand());
                }
                if (StringUtils.isNotBlank(deviceDto.getModel())) {
                    urls = replaceMacro("__MODEL__", urls, deviceDto.getModel());
                }
                urls = replaceMacro("__IP__", urls, MacroType.IP.getCode());
                urls = replaceMacro("__UA__", urls, MacroType.UA.getCode());
                if (StringUtils.isNotBlank(networkDto.getMac())) {
                    urls = replaceMacro("__MAC__", urls, networkDto.getMac());
                }
                if (StringUtils.isNotBlank(networkDto.getMacMd5())) {
                    urls = replaceMacro("__MAC_MD5__", urls, networkDto.getMacMd5());
                }
                if (StringUtils.isNotBlank(deviceDto.getAndroidId())) {
                    urls = replaceMacro("__ANDROID_ID__", urls, deviceDto.getAndroidId());
                }
                if (StringUtils.isNotBlank(deviceDto.getAndroidIdMd5())) {
                    urls = replaceMacro("__ANDROID_ID_MD5__", urls, deviceDto.getAndroidIdMd5());
                }
                if (StringUtils.isNotBlank(deviceDto.getImei())) {
                    urls = replaceMacro("__IMEI__", urls, deviceDto.getImei());
                }
                if (StringUtils.isNotBlank(deviceDto.getImeiMd5())) {
                    urls = replaceMacro("__IMEI_MD5__", urls, deviceDto.getImeiMd5());
                }
                if (StringUtils.isNotBlank(deviceDto.getIdfa())) {
                    urls = replaceMacro("__IDFA__", urls, deviceDto.getIdfa());
                }
                if (StringUtils.isNotBlank(deviceDto.getIdfaMd5())) {
                    urls = replaceMacro("__IDFA_MD5__", urls, deviceDto.getIdfaMd5());
                }
                if (StringUtils.isNotBlank(deviceDto.getOaid())) {
                    urls = replaceMacro("__OAID__", urls, deviceDto.getOaid());
                }
                if (StringUtils.isNotBlank(deviceDto.getOaidMd5())) {
                    urls = replaceMacro("__OAID_MD5__", urls, deviceDto.getOaidMd5());
                }
                if (!CollectionUtils.isEmpty(deviceDto.getCaids())) {
                    try {
                        List<Map<String, String>> idList = new LinkedList<>();
                        deviceDto.getCaids().forEach(item -> {
                            Map<String, String> idMap = new HashMap<>();
                            idMap.put("id", item.getCaid());
                            idMap.put("version", item.getVersion());
                            idList.add(idMap);
                        });
                        urls = replaceMacro("__CAID__", urls, URLEncoder.encode(JsonHelper.toJsonStringWithoutNull(idList), "utf-8"));
                    } catch (Exception ig) {
                    }
                }
                track.setTrackUrls(urls);
            });
            dto.setTracks(trackDtoList);
            responseDto.getTags().add(dto);
            responseDto.setMsg("");
        }
        return responseDto;
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam param) throws Exception {
        return SuperResult.rightResult();
    }

    private RTARequest_1_3 createRtaRequest(RtbRequestDto rtbDto, DspAdvertiserAd ad, DspAdvertiserAccount account) {
        RTARequest_1_3.Builder builder = RTARequest_1_3.newBuilder();
        builder.setRequestId(rtbDto.getReqId());
        builder.addRtaId(account.getRtaToken());
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        builder.setOs(deviceDto.getOsType().getCode());
        if (StringUtils.isNotBlank(deviceDto.getIdfa())) {
            builder.setIdfa(deviceDto.getIdfa());
        }
        if (StringUtils.isNotBlank(deviceDto.getIdfaMd5())) {
            builder.setIdfaMd5(deviceDto.getIdfaMd5());
        }
        if (StringUtils.isNotBlank(deviceDto.getImei())) {
            builder.setImei(deviceDto.getImei());
        }
        if (StringUtils.isNotBlank(deviceDto.getImeiMd5())) {
            builder.setImeiMd5(deviceDto.getImeiMd5());
        }
        if (StringUtils.isNotBlank(deviceDto.getOaid())) {
            builder.setOaid(deviceDto.getOaid());
        }

        if (StringUtils.isNotBlank(deviceDto.getOaidMd5())) {
            builder.setOaidMd5(deviceDto.getOaidMd5());
        }
        if (StringUtils.isNotBlank(deviceDto.getAndroidId())) {
            builder.setAndroidId(deviceDto.getAndroidId());
        }
        if (StringUtils.isNotBlank(deviceDto.getAndroidIdMd5())) {
            builder.setAndroidIdMd5(deviceDto.getAndroidIdMd5());
        }
        if (!CollectionUtils.isEmpty(deviceDto.getCaids())) {
            Caid.Builder caId = Caid.newBuilder();
            for (RequestCaidDto dto : deviceDto.getCaids()) {
                if (StringUtils.isNotBlank(dto.getCaid())) {
                    caId.setId(dto.getCaid());
                }
                if (StringUtils.isNotBlank(dto.getVersion())) {
                    caId.setVersion(dto.getVersion());
                }
            }
            builder.addCaid(caId.build());
        }
        return builder.build();
    }

    private DspAdvertiserResource copyResource(DspAdvertiserResource resource) {
        DspAdvertiserResource copy = new DspAdvertiserResource();
        copy.setFileUrl(resource.getFileUrl());
        copy.setHeight(resource.getHeight());
        copy.setWidth(resource.getWidth());
        copy.setType(resource.getType());
        copy.setDuration(resource.getDuration());
        copy.setTitle(resource.getTitle());
        copy.setVideoSize(resource.getVideoSize());
        copy.setUserId(resource.getUserId());
        copy.setIsDelete(resource.getIsDelete());
        copy.setCreateTime(resource.getCreateTime());
        copy.setId(resource.getId());
        return copy;
    }
}
