package cn.taken.ad.logic.adv.ainian.dto.resp;

import java.util.List;

public class Seatbid {

    private String impId; //曝光编号
    private Integer price; //竞价出价，单位（分），竞价必填
    private String title; //广告标题
    private String desc; //广告描述
    private List<Image> imageList; //广告大图片
    private Video video; //视频广告对象
    private Integer w; //宽度
    private Integer h; //高度
    private String icon; //广告图标
    private Integer urlType; //落地页类型
    private String landingUrl; //落地页地址
    private String deeplinkUrl; //deeplinkUrl有值优先，没有则打开落地页landingUrl
    private String backupLinkUrl; //Deeplink唤醒广告退化链接
    private Integer adType; //广告类型：1=开屏，2=横屏，3=插屏，4=信息流，5=视频，6=激励视频
    private Integer adOperationType; //交互类型：1 打开网页;2 APP下载; 3 广点通落地页
    private List<Track> trackList; //上报跟踪链接
    private String adLogoTxt; //广告图标文字
    private String adLogoImg; //广告图标
    private String adBrand; //广告品牌
    private String adSource; //广告来源
    private String actionText; //按钮文本
    private String actionImg; //按钮图标
    private Applet applet; //微信小程序
    private String clickAreaReportUrl; //点击坐标上报url，详情参考

    public String getImpId() {
        return impId;
    }

    public void setImpId(String impId) {
        this.impId = impId;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<Image> getImageList() {
        return imageList;
    }

    public void setImageList(List<Image> imageList) {
        this.imageList = imageList;
    }

    public Video getVideo() {
        return video;
    }

    public void setVideo(Video video) {
        this.video = video;
    }

    public Integer getW() {
        return w;
    }

    public void setW(Integer w) {
        this.w = w;
    }

    public Integer getH() {
        return h;
    }

    public void setH(Integer h) {
        this.h = h;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getUrlType() {
        return urlType;
    }

    public void setUrlType(Integer urlType) {
        this.urlType = urlType;
    }

    public String getLandingUrl() {
        return landingUrl;
    }

    public void setLandingUrl(String landingUrl) {
        this.landingUrl = landingUrl;
    }

    public String getDeeplinkUrl() {
        return deeplinkUrl;
    }

    public void setDeeplinkUrl(String deeplinkUrl) {
        this.deeplinkUrl = deeplinkUrl;
    }

    public String getBackupLinkUrl() {
        return backupLinkUrl;
    }

    public void setBackupLinkUrl(String backupLinkUrl) {
        this.backupLinkUrl = backupLinkUrl;
    }

    public Integer getAdType() {
        return adType;
    }

    public void setAdType(Integer adType) {
        this.adType = adType;
    }

    public Integer getAdOperationType() {
        return adOperationType;
    }

    public void setAdOperationType(Integer adOperationType) {
        this.adOperationType = adOperationType;
    }

    public List<Track> getTrackList() {
        return trackList;
    }

    public void setTrackList(List<Track> trackList) {
        this.trackList = trackList;
    }

    public String getAdLogoTxt() {
        return adLogoTxt;
    }

    public void setAdLogoTxt(String adLogoTxt) {
        this.adLogoTxt = adLogoTxt;
    }

    public String getAdLogoImg() {
        return adLogoImg;
    }

    public void setAdLogoImg(String adLogoImg) {
        this.adLogoImg = adLogoImg;
    }

    public String getAdBrand() {
        return adBrand;
    }

    public void setAdBrand(String adBrand) {
        this.adBrand = adBrand;
    }

    public String getAdSource() {
        return adSource;
    }

    public void setAdSource(String adSource) {
        this.adSource = adSource;
    }

    public String getActionText() {
        return actionText;
    }

    public void setActionText(String actionText) {
        this.actionText = actionText;
    }

    public String getActionImg() {
        return actionImg;
    }

    public void setActionImg(String actionImg) {
        this.actionImg = actionImg;
    }

    public Applet getApplet() {
        return applet;
    }

    public void setApplet(Applet applet) {
        this.applet = applet;
    }

    public String getClickAreaReportUrl() {
        return clickAreaReportUrl;
    }

    public void setClickAreaReportUrl(String clickAreaReportUrl) {
        this.clickAreaReportUrl = clickAreaReportUrl;
    }
}
