package cn.taken.ad.logic.adv.liwei.dto;

import java.io.Serializable;
import java.util.List;

public class ResponseBid implements Serializable {

    // 基础竞价信息
    private String id; // 唯一标识
    private String impid; // 对应的Imp对象ID
    private Integer price; // 出价(单位：分)
    private String adid; // 广告ID
    private String dealid; // 私有交易规则ID

    // 广告内容信息
    private String title; // 广告标题
    private String desc; // 广告描述
    private ResponseLogo logo; // Logo信息
    private RespImageAndIcon icon; // Icon信息
    private List<RespImageAndIcon> images; // 主图列表
    private ResponseVideo video; // 视频信息
    private ResponseApp app; // 应用信息

    // 跳转链接信息
    private String clickurl; // 点击跳转URL
    private String deeplink; // Deeplink地址
    private String universalurl; // iOS Universal Link
    private String mini_program_id; // 微信小程序ID
    private String mini_program_path; // 微信小程序路径

    // 广告类型标识
    private Integer isdown; // 是否下载类广告
    private Integer isdeep; // 是否Deeplink唤起
    private Integer ismin; // 是否小程序跳转

    // 宏替换配置
    private Integer macro; // 宏替换处理类型

    // 监测上报信息
    private String nurl; // 胜出通知地址
    private List<ResponseTracking> tracking; // 上报URL列表

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getImpid() {
        return impid;
    }

    public void setImpid(String impid) {
        this.impid = impid;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public String getAdid() {
        return adid;
    }

    public void setAdid(String adid) {
        this.adid = adid;
    }

    public String getDealid() {
        return dealid;
    }

    public void setDealid(String dealid) {
        this.dealid = dealid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public ResponseLogo getLogo() {
        return logo;
    }

    public void setLogo(ResponseLogo logo) {
        this.logo = logo;
    }

    public RespImageAndIcon getIcon() {
        return icon;
    }

    public void setIcon(RespImageAndIcon icon) {
        this.icon = icon;
    }

    public List<RespImageAndIcon> getImages() {
        return images;
    }

    public void setImages(List<RespImageAndIcon> images) {
        this.images = images;
    }

    public ResponseVideo getVideo() {
        return video;
    }

    public void setVideo(ResponseVideo video) {
        this.video = video;
    }

    public ResponseApp getApp() {
        return app;
    }

    public void setApp(ResponseApp app) {
        this.app = app;
    }

    public String getClickurl() {
        return clickurl;
    }

    public void setClickurl(String clickurl) {
        this.clickurl = clickurl;
    }

    public String getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }

    public String getUniversalurl() {
        return universalurl;
    }

    public void setUniversalurl(String universalurl) {
        this.universalurl = universalurl;
    }

    public String getMini_program_id() {
        return mini_program_id;
    }

    public void setMini_program_id(String mini_program_id) {
        this.mini_program_id = mini_program_id;
    }

    public String getMini_program_path() {
        return mini_program_path;
    }

    public void setMini_program_path(String mini_program_path) {
        this.mini_program_path = mini_program_path;
    }

    public Integer getIsdown() {
        return isdown;
    }

    public void setIsdown(Integer isdown) {
        this.isdown = isdown;
    }

    public Integer getIsdeep() {
        return isdeep;
    }

    public void setIsdeep(Integer isdeep) {
        this.isdeep = isdeep;
    }

    public Integer getIsmin() {
        return ismin;
    }

    public void setIsmin(Integer ismin) {
        this.ismin = ismin;
    }

    public Integer getMacro() {
        return macro;
    }

    public void setMacro(Integer macro) {
        this.macro = macro;
    }

    public String getNurl() {
        return nurl;
    }

    public void setNurl(String nurl) {
        this.nurl = nurl;
    }

    public List<ResponseTracking> getTracking() {
        return tracking;
    }

    public void setTracking(List<ResponseTracking> tracking) {
        this.tracking = tracking;
    }
}
