// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

public interface ReqBannerOrBuilder extends
    // @@protoc_insertion_point(interface_extends:ReqBanner)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int32 w = 1;</code>
   * @return The w.
   */
  int getW();

  /**
   * <code>int32 h = 2;</code>
   * @return The h.
   */
  int getH();

  /**
   * <code>int32 type = 3;</code>
   * @return The type.
   */
  int getType();

  /**
   * <code>string mines = 4;</code>
   * @return The mines.
   */
  java.lang.String getMines();
  /**
   * <code>string mines = 4;</code>
   * @return The bytes for mines.
   */
  com.google.protobuf.ByteString
      getMinesBytes();

  /**
   * <code>int32 isSupportVideo = 5;</code>
   * @return The isSupportVideo.
   */
  int getIsSupportVideo();
}
