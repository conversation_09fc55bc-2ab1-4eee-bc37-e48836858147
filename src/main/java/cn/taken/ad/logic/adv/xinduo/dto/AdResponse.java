// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xinduo_9.0.1.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.xinduo.dto;

/**
 * <pre>
 * 响应协议
 * </pre>
 *
 * Protobuf type {@code AdResponse}
 */
public final class AdResponse extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:AdResponse)
    AdResponseOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      AdResponse.class.getName());
  }
  // Use AdResponse.newBuilder() to construct.
  private AdResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private AdResponse() {
    msg_ = "";
    ads_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return XinDuoAdvReqResp.internal_static_AdResponse_descriptor;
  }

  @Override
  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return XinDuoAdvReqResp.internal_static_AdResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            AdResponse.class, Builder.class);
  }

  public static final int CODE_FIELD_NUMBER = 1;
  private int code_ = 0;
  /**
   * <pre>
   * 状态码
   * </pre>
   *
   * <code>int32 code = 1;</code>
   * @return The code.
   */
  @Override
  public int getCode() {
    return code_;
  }

  public static final int MSG_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile Object msg_ = "";
  /**
   * <pre>
   * 消息描述
   * </pre>
   *
   * <code>string msg = 2;</code>
   * @return The msg.
   */
  @Override
  public String getMsg() {
    Object ref = msg_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      msg_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 消息描述
   * </pre>
   *
   * <code>string msg = 2;</code>
   * @return The bytes for msg.
   */
  @Override
  public com.google.protobuf.ByteString
      getMsgBytes() {
    Object ref = msg_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      msg_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ADS_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<Ad> ads_;
  /**
   * <pre>
   * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
   * </pre>
   *
   * <code>repeated .Ad ads = 3;</code>
   */
  @Override
  public java.util.List<Ad> getAdsList() {
    return ads_;
  }
  /**
   * <pre>
   * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
   * </pre>
   *
   * <code>repeated .Ad ads = 3;</code>
   */
  @Override
  public java.util.List<? extends AdOrBuilder>
      getAdsOrBuilderList() {
    return ads_;
  }
  /**
   * <pre>
   * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
   * </pre>
   *
   * <code>repeated .Ad ads = 3;</code>
   */
  @Override
  public int getAdsCount() {
    return ads_.size();
  }
  /**
   * <pre>
   * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
   * </pre>
   *
   * <code>repeated .Ad ads = 3;</code>
   */
  @Override
  public Ad getAds(int index) {
    return ads_.get(index);
  }
  /**
   * <pre>
   * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
   * </pre>
   *
   * <code>repeated .Ad ads = 3;</code>
   */
  @Override
  public AdOrBuilder getAdsOrBuilder(
      int index) {
    return ads_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (code_ != 0) {
      output.writeInt32(1, code_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(msg_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, msg_);
    }
    for (int i = 0; i < ads_.size(); i++) {
      output.writeMessage(3, ads_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (code_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, code_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(msg_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, msg_);
    }
    for (int i = 0; i < ads_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, ads_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof AdResponse)) {
      return super.equals(obj);
    }
    AdResponse other = (AdResponse) obj;

    if (getCode()
        != other.getCode()) return false;
    if (!getMsg()
        .equals(other.getMsg())) return false;
    if (!getAdsList()
        .equals(other.getAdsList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode();
    hash = (37 * hash) + MSG_FIELD_NUMBER;
    hash = (53 * hash) + getMsg().hashCode();
    if (getAdsCount() > 0) {
      hash = (37 * hash) + ADS_FIELD_NUMBER;
      hash = (53 * hash) + getAdsList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static AdResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static AdResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static AdResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static AdResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static AdResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static AdResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static AdResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static AdResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static AdResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static AdResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static AdResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static AdResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(AdResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 响应协议
   * </pre>
   *
   * Protobuf type {@code AdResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:AdResponse)
      AdResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return XinDuoAdvReqResp.internal_static_AdResponse_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return XinDuoAdvReqResp.internal_static_AdResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              AdResponse.class, Builder.class);
    }

    // Construct using cn.taken.ad.logic.prossor.xinduov2.dto.AdResponse.newBuilder()
    private Builder() {

    }

    private Builder(
        BuilderParent parent) {
      super(parent);

    }
    @Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      code_ = 0;
      msg_ = "";
      if (adsBuilder_ == null) {
        ads_ = java.util.Collections.emptyList();
      } else {
        ads_ = null;
        adsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return XinDuoAdvReqResp.internal_static_AdResponse_descriptor;
    }

    @Override
    public AdResponse getDefaultInstanceForType() {
      return AdResponse.getDefaultInstance();
    }

    @Override
    public AdResponse build() {
      AdResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @Override
    public AdResponse buildPartial() {
      AdResponse result = new AdResponse(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(AdResponse result) {
      if (adsBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          ads_ = java.util.Collections.unmodifiableList(ads_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.ads_ = ads_;
      } else {
        result.ads_ = adsBuilder_.build();
      }
    }

    private void buildPartial0(AdResponse result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.code_ = code_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.msg_ = msg_;
      }
    }

    @Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof AdResponse) {
        return mergeFrom((AdResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(AdResponse other) {
      if (other == AdResponse.getDefaultInstance()) return this;
      if (other.getCode() != 0) {
        setCode(other.getCode());
      }
      if (!other.getMsg().isEmpty()) {
        msg_ = other.msg_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (adsBuilder_ == null) {
        if (!other.ads_.isEmpty()) {
          if (ads_.isEmpty()) {
            ads_ = other.ads_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureAdsIsMutable();
            ads_.addAll(other.ads_);
          }
          onChanged();
        }
      } else {
        if (!other.ads_.isEmpty()) {
          if (adsBuilder_.isEmpty()) {
            adsBuilder_.dispose();
            adsBuilder_ = null;
            ads_ = other.ads_;
            bitField0_ = (bitField0_ & ~0x00000004);
            adsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 getAdsFieldBuilder() : null;
          } else {
            adsBuilder_.addAllMessages(other.ads_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @Override
    public final boolean isInitialized() {
      return true;
    }

    @Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              code_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              msg_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              Ad m =
                  input.readMessage(
                      Ad.parser(),
                      extensionRegistry);
              if (adsBuilder_ == null) {
                ensureAdsIsMutable();
                ads_.add(m);
              } else {
                adsBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int code_ ;
    /**
     * <pre>
     * 状态码
     * </pre>
     *
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @Override
    public int getCode() {
      return code_;
    }
    /**
     * <pre>
     * 状态码
     * </pre>
     *
     * <code>int32 code = 1;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(int value) {

      code_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 状态码
     * </pre>
     *
     * <code>int32 code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      bitField0_ = (bitField0_ & ~0x00000001);
      code_ = 0;
      onChanged();
      return this;
    }

    private Object msg_ = "";
    /**
     * <pre>
     * 消息描述
     * </pre>
     *
     * <code>string msg = 2;</code>
     * @return The msg.
     */
    public String getMsg() {
      Object ref = msg_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        msg_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 消息描述
     * </pre>
     *
     * <code>string msg = 2;</code>
     * @return The bytes for msg.
     */
    public com.google.protobuf.ByteString
        getMsgBytes() {
      Object ref = msg_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 消息描述
     * </pre>
     *
     * <code>string msg = 2;</code>
     * @param value The msg to set.
     * @return This builder for chaining.
     */
    public Builder setMsg(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      msg_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 消息描述
     * </pre>
     *
     * <code>string msg = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMsg() {
      msg_ = getDefaultInstance().getMsg();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 消息描述
     * </pre>
     *
     * <code>string msg = 2;</code>
     * @param value The bytes for msg to set.
     * @return This builder for chaining.
     */
    public Builder setMsgBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      msg_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.util.List<Ad> ads_ =
      java.util.Collections.emptyList();
    private void ensureAdsIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        ads_ = new java.util.ArrayList<Ad>(ads_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        Ad, Ad.Builder, AdOrBuilder> adsBuilder_;

    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public java.util.List<Ad> getAdsList() {
      if (adsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(ads_);
      } else {
        return adsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public int getAdsCount() {
      if (adsBuilder_ == null) {
        return ads_.size();
      } else {
        return adsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Ad getAds(int index) {
      if (adsBuilder_ == null) {
        return ads_.get(index);
      } else {
        return adsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Builder setAds(
        int index, Ad value) {
      if (adsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAdsIsMutable();
        ads_.set(index, value);
        onChanged();
      } else {
        adsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Builder setAds(
        int index, Ad.Builder builderForValue) {
      if (adsBuilder_ == null) {
        ensureAdsIsMutable();
        ads_.set(index, builderForValue.build());
        onChanged();
      } else {
        adsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Builder addAds(Ad value) {
      if (adsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAdsIsMutable();
        ads_.add(value);
        onChanged();
      } else {
        adsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Builder addAds(
        int index, Ad value) {
      if (adsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAdsIsMutable();
        ads_.add(index, value);
        onChanged();
      } else {
        adsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Builder addAds(
        Ad.Builder builderForValue) {
      if (adsBuilder_ == null) {
        ensureAdsIsMutable();
        ads_.add(builderForValue.build());
        onChanged();
      } else {
        adsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Builder addAds(
        int index, Ad.Builder builderForValue) {
      if (adsBuilder_ == null) {
        ensureAdsIsMutable();
        ads_.add(index, builderForValue.build());
        onChanged();
      } else {
        adsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Builder addAllAds(
        Iterable<? extends Ad> values) {
      if (adsBuilder_ == null) {
        ensureAdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ads_);
        onChanged();
      } else {
        adsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Builder clearAds() {
      if (adsBuilder_ == null) {
        ads_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        adsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Builder removeAds(int index) {
      if (adsBuilder_ == null) {
        ensureAdsIsMutable();
        ads_.remove(index);
        onChanged();
      } else {
        adsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Ad.Builder getAdsBuilder(
        int index) {
      return getAdsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public AdOrBuilder getAdsOrBuilder(
        int index) {
      if (adsBuilder_ == null) {
        return ads_.get(index);  } else {
        return adsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public java.util.List<? extends AdOrBuilder>
         getAdsOrBuilderList() {
      if (adsBuilder_ != null) {
        return adsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(ads_);
      }
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Ad.Builder addAdsBuilder() {
      return getAdsFieldBuilder().addBuilder(
          Ad.getDefaultInstance());
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public Ad.Builder addAdsBuilder(
        int index) {
      return getAdsFieldBuilder().addBuilder(
          index, Ad.getDefaultInstance());
    }
    /**
     * <pre>
     * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
     * </pre>
     *
     * <code>repeated .Ad ads = 3;</code>
     */
    public java.util.List<Ad.Builder>
         getAdsBuilderList() {
      return getAdsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        Ad, Ad.Builder, AdOrBuilder>
        getAdsFieldBuilder() {
      if (adsBuilder_ == null) {
        adsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            Ad, Ad.Builder, AdOrBuilder>(
                ads_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        ads_ = null;
      }
      return adsBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:AdResponse)
  }

  // @@protoc_insertion_point(class_scope:AdResponse)
  private static final AdResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new AdResponse();
  }

  public static AdResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AdResponse>
      PARSER = new com.google.protobuf.AbstractParser<AdResponse>() {
    @Override
    public AdResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<AdResponse> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<AdResponse> getParserForType() {
    return PARSER;
  }

  @Override
  public AdResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

