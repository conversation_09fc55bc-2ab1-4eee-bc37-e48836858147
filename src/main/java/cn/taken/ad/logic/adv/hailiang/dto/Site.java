// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: halomobi_ads_entry.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.hailiang.dto;

/**
 * Protobuf type {@code dsp.Site}
 */
public final class Site extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:dsp.Site)
    SiteOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      Site.class.getName());
  }
  // Use Site.newBuilder() to construct.
  private Site(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private Site() {
    id_ = "";
    name_ = "";
    domain_ = "";
    cat_ = "";
    sectioncat_ = "";
    pagecat_ = "";
    page_ = "";
    ref_ = "";
    search_ = "";
    keywords_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return HaiLiangDto.internal_static_dsp_Site_descriptor;
  }

  @Override
  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return HaiLiangDto.internal_static_dsp_Site_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            Site.class, Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile Object id_ = "";
  /**
   * <pre>
   * Site ID on the exchange.
   * RECOMMENDED by the OpenRTB specification.
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @Override
  public String getId() {
    Object ref = id_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Site ID on the exchange.
   * RECOMMENDED by the OpenRTB specification.
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    Object ref = id_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile Object name_ = "";
  /**
   * <pre>
   * Site name (may be masked at publisher's request).
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  @Override
  public String getName() {
    Object ref = name_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Site name (may be masked at publisher's request).
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  @Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    Object ref = name_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DOMAIN_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile Object domain_ = "";
  /**
   * <pre>
   * Domain of the site, used for advertiser side blocking.
   * For example, "foo.com".
   * </pre>
   *
   * <code>string domain = 3;</code>
   * @return The domain.
   */
  @Override
  public String getDomain() {
    Object ref = domain_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      domain_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Domain of the site, used for advertiser side blocking.
   * For example, "foo.com".
   * </pre>
   *
   * <code>string domain = 3;</code>
   * @return The bytes for domain.
   */
  @Override
  public com.google.protobuf.ByteString
      getDomainBytes() {
    Object ref = domain_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      domain_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CAT_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile Object cat_ = "";
  /**
   * <pre>
   * Array of IAB content categories of the site.
   * See enum ContentCategory.
   * </pre>
   *
   * <code>string cat = 4;</code>
   * @return The cat.
   */
  @Override
  public String getCat() {
    Object ref = cat_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      cat_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Array of IAB content categories of the site.
   * See enum ContentCategory.
   * </pre>
   *
   * <code>string cat = 4;</code>
   * @return The bytes for cat.
   */
  @Override
  public com.google.protobuf.ByteString
      getCatBytes() {
    Object ref = cat_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      cat_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SECTIONCAT_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile Object sectioncat_ = "";
  /**
   * <pre>
   * Array of IAB content categories that describe the current section
   * of the site.
   * See enum ContentCategory.
   * </pre>
   *
   * <code>string sectioncat = 5;</code>
   * @return The sectioncat.
   */
  @Override
  public String getSectioncat() {
    Object ref = sectioncat_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      sectioncat_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Array of IAB content categories that describe the current section
   * of the site.
   * See enum ContentCategory.
   * </pre>
   *
   * <code>string sectioncat = 5;</code>
   * @return The bytes for sectioncat.
   */
  @Override
  public com.google.protobuf.ByteString
      getSectioncatBytes() {
    Object ref = sectioncat_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      sectioncat_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAGECAT_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile Object pagecat_ = "";
  /**
   * <pre>
   * Array of IAB content categories that describe the current page or view
   * of the site.
   * See enum ContentCategory.
   * </pre>
   *
   * <code>string pagecat = 6;</code>
   * @return The pagecat.
   */
  @Override
  public String getPagecat() {
    Object ref = pagecat_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      pagecat_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Array of IAB content categories that describe the current page or view
   * of the site.
   * See enum ContentCategory.
   * </pre>
   *
   * <code>string pagecat = 6;</code>
   * @return The bytes for pagecat.
   */
  @Override
  public com.google.protobuf.ByteString
      getPagecatBytes() {
    Object ref = pagecat_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      pagecat_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAGE_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile Object page_ = "";
  /**
   * <pre>
   * URL of the page where the impression will be shown.
   * </pre>
   *
   * <code>string page = 7;</code>
   * @return The page.
   */
  @Override
  public String getPage() {
    Object ref = page_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      page_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * URL of the page where the impression will be shown.
   * </pre>
   *
   * <code>string page = 7;</code>
   * @return The bytes for page.
   */
  @Override
  public com.google.protobuf.ByteString
      getPageBytes() {
    Object ref = page_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      page_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRIVACYPOLICY_FIELD_NUMBER = 8;
  private int privacypolicy_ = 0;
  /**
   * <pre>
   * Indicates if the site has a privacy policy, where 0 = no, 1 = yes.
   * </pre>
   *
   * <code>int32 privacypolicy = 8;</code>
   * @return The privacypolicy.
   */
  @Override
  public int getPrivacypolicy() {
    return privacypolicy_;
  }

  public static final int REF_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile Object ref_ = "";
  /**
   * <pre>
   * Referrer URL that caused navigation to the current page.
   * </pre>
   *
   * <code>string ref = 9;</code>
   * @return The ref.
   */
  @Override
  public String getRef() {
    Object ref = ref_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      ref_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Referrer URL that caused navigation to the current page.
   * </pre>
   *
   * <code>string ref = 9;</code>
   * @return The bytes for ref.
   */
  @Override
  public com.google.protobuf.ByteString
      getRefBytes() {
    Object ref = ref_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      ref_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SEARCH_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile Object search_ = "";
  /**
   * <pre>
   * Search string that caused navigation to the current page.
   * </pre>
   *
   * <code>string search = 10;</code>
   * @return The search.
   */
  @Override
  public String getSearch() {
    Object ref = search_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      search_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Search string that caused navigation to the current page.
   * </pre>
   *
   * <code>string search = 10;</code>
   * @return The bytes for search.
   */
  @Override
  public com.google.protobuf.ByteString
      getSearchBytes() {
    Object ref = search_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      search_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int KEYWORDS_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile Object keywords_ = "";
  /**
   * <pre>
   * Comma separated list of keywords about this site.
   * Note: OpenRTB 2.2 allowed an array-of-strings as alternate implementation
   * but this was fixed in 2.3+ where it's definitely a single string with CSV
   * content again. Compatibility with some OpenRTB 2.2 exchanges that adopted
   * the alternate representation may require custom handling of the JSON.
   * </pre>
   *
   * <code>string keywords = 11;</code>
   * @return The keywords.
   */
  @Override
  public String getKeywords() {
    Object ref = keywords_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      keywords_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Comma separated list of keywords about this site.
   * Note: OpenRTB 2.2 allowed an array-of-strings as alternate implementation
   * but this was fixed in 2.3+ where it's definitely a single string with CSV
   * content again. Compatibility with some OpenRTB 2.2 exchanges that adopted
   * the alternate representation may require custom handling of the JSON.
   * </pre>
   *
   * <code>string keywords = 11;</code>
   * @return The bytes for keywords.
   */
  @Override
  public com.google.protobuf.ByteString
      getKeywordsBytes() {
    Object ref = keywords_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      keywords_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MOBILE_FIELD_NUMBER = 12;
  private int mobile_ = 0;
  /**
   * <pre>
   * Indicates if the site has been programmed to optimize layout
   * when viewed on mobile devices, where 0 = no, 1 = yes.
   * </pre>
   *
   * <code>int32 mobile = 12;</code>
   * @return The mobile.
   */
  @Override
  public int getMobile() {
    return mobile_;
  }

  private byte memoizedIsInitialized = -1;
  @Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(name_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, name_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(domain_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, domain_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(cat_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, cat_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(sectioncat_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, sectioncat_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(pagecat_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, pagecat_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(page_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, page_);
    }
    if (privacypolicy_ != 0) {
      output.writeInt32(8, privacypolicy_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ref_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, ref_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(search_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 10, search_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(keywords_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 11, keywords_);
    }
    if (mobile_ != 0) {
      output.writeInt32(12, mobile_);
    }
    getUnknownFields().writeTo(output);
  }

  @Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(name_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, name_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(domain_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, domain_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(cat_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, cat_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(sectioncat_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, sectioncat_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(pagecat_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, pagecat_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(page_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, page_);
    }
    if (privacypolicy_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, privacypolicy_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ref_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, ref_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(search_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(10, search_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(keywords_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(11, keywords_);
    }
    if (mobile_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(12, mobile_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof Site)) {
      return super.equals(obj);
    }
    Site other = (Site) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getDomain()
        .equals(other.getDomain())) return false;
    if (!getCat()
        .equals(other.getCat())) return false;
    if (!getSectioncat()
        .equals(other.getSectioncat())) return false;
    if (!getPagecat()
        .equals(other.getPagecat())) return false;
    if (!getPage()
        .equals(other.getPage())) return false;
    if (getPrivacypolicy()
        != other.getPrivacypolicy()) return false;
    if (!getRef()
        .equals(other.getRef())) return false;
    if (!getSearch()
        .equals(other.getSearch())) return false;
    if (!getKeywords()
        .equals(other.getKeywords())) return false;
    if (getMobile()
        != other.getMobile()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + DOMAIN_FIELD_NUMBER;
    hash = (53 * hash) + getDomain().hashCode();
    hash = (37 * hash) + CAT_FIELD_NUMBER;
    hash = (53 * hash) + getCat().hashCode();
    hash = (37 * hash) + SECTIONCAT_FIELD_NUMBER;
    hash = (53 * hash) + getSectioncat().hashCode();
    hash = (37 * hash) + PAGECAT_FIELD_NUMBER;
    hash = (53 * hash) + getPagecat().hashCode();
    hash = (37 * hash) + PAGE_FIELD_NUMBER;
    hash = (53 * hash) + getPage().hashCode();
    hash = (37 * hash) + PRIVACYPOLICY_FIELD_NUMBER;
    hash = (53 * hash) + getPrivacypolicy();
    hash = (37 * hash) + REF_FIELD_NUMBER;
    hash = (53 * hash) + getRef().hashCode();
    hash = (37 * hash) + SEARCH_FIELD_NUMBER;
    hash = (53 * hash) + getSearch().hashCode();
    hash = (37 * hash) + KEYWORDS_FIELD_NUMBER;
    hash = (53 * hash) + getKeywords().hashCode();
    hash = (37 * hash) + MOBILE_FIELD_NUMBER;
    hash = (53 * hash) + getMobile();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static Site parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Site parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Site parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Site parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Site parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Site parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Site parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static Site parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static Site parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static Site parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static Site parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static Site parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(Site prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code dsp.Site}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:dsp.Site)
      SiteOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return HaiLiangDto.internal_static_dsp_Site_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return HaiLiangDto.internal_static_dsp_Site_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Site.class, Builder.class);
    }

    // Construct using cn.taken.ad.logic.prossor.hailiang.dto.Site.newBuilder()
    private Builder() {

    }

    private Builder(
        BuilderParent parent) {
      super(parent);

    }
    @Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      name_ = "";
      domain_ = "";
      cat_ = "";
      sectioncat_ = "";
      pagecat_ = "";
      page_ = "";
      privacypolicy_ = 0;
      ref_ = "";
      search_ = "";
      keywords_ = "";
      mobile_ = 0;
      return this;
    }

    @Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return HaiLiangDto.internal_static_dsp_Site_descriptor;
    }

    @Override
    public Site getDefaultInstanceForType() {
      return Site.getDefaultInstance();
    }

    @Override
    public Site build() {
      Site result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @Override
    public Site buildPartial() {
      Site result = new Site(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(Site result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.name_ = name_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.domain_ = domain_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.cat_ = cat_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.sectioncat_ = sectioncat_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.pagecat_ = pagecat_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.page_ = page_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.privacypolicy_ = privacypolicy_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.ref_ = ref_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.search_ = search_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.keywords_ = keywords_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.mobile_ = mobile_;
      }
    }

    @Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof Site) {
        return mergeFrom((Site)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(Site other) {
      if (other == Site.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getDomain().isEmpty()) {
        domain_ = other.domain_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getCat().isEmpty()) {
        cat_ = other.cat_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getSectioncat().isEmpty()) {
        sectioncat_ = other.sectioncat_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.getPagecat().isEmpty()) {
        pagecat_ = other.pagecat_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getPage().isEmpty()) {
        page_ = other.page_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.getPrivacypolicy() != 0) {
        setPrivacypolicy(other.getPrivacypolicy());
      }
      if (!other.getRef().isEmpty()) {
        ref_ = other.ref_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (!other.getSearch().isEmpty()) {
        search_ = other.search_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (!other.getKeywords().isEmpty()) {
        keywords_ = other.keywords_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.getMobile() != 0) {
        setMobile(other.getMobile());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @Override
    public final boolean isInitialized() {
      return true;
    }

    @Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              name_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              domain_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              cat_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              sectioncat_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              pagecat_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              page_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 64: {
              privacypolicy_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 74: {
              ref_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              search_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              keywords_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 96: {
              mobile_ = input.readInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private Object id_ = "";
    /**
     * <pre>
     * Site ID on the exchange.
     * RECOMMENDED by the OpenRTB specification.
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public String getId() {
      Object ref = id_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * Site ID on the exchange.
     * RECOMMENDED by the OpenRTB specification.
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Site ID on the exchange.
     * RECOMMENDED by the OpenRTB specification.
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Site ID on the exchange.
     * RECOMMENDED by the OpenRTB specification.
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Site ID on the exchange.
     * RECOMMENDED by the OpenRTB specification.
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private Object name_ = "";
    /**
     * <pre>
     * Site name (may be masked at publisher's request).
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    public String getName() {
      Object ref = name_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * Site name (may be masked at publisher's request).
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Site name (may be masked at publisher's request).
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Site name (may be masked at publisher's request).
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Site name (may be masked at publisher's request).
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      name_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private Object domain_ = "";
    /**
     * <pre>
     * Domain of the site, used for advertiser side blocking.
     * For example, "foo.com".
     * </pre>
     *
     * <code>string domain = 3;</code>
     * @return The domain.
     */
    public String getDomain() {
      Object ref = domain_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        domain_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * Domain of the site, used for advertiser side blocking.
     * For example, "foo.com".
     * </pre>
     *
     * <code>string domain = 3;</code>
     * @return The bytes for domain.
     */
    public com.google.protobuf.ByteString
        getDomainBytes() {
      Object ref = domain_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        domain_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Domain of the site, used for advertiser side blocking.
     * For example, "foo.com".
     * </pre>
     *
     * <code>string domain = 3;</code>
     * @param value The domain to set.
     * @return This builder for chaining.
     */
    public Builder setDomain(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      domain_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Domain of the site, used for advertiser side blocking.
     * For example, "foo.com".
     * </pre>
     *
     * <code>string domain = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearDomain() {
      domain_ = getDefaultInstance().getDomain();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Domain of the site, used for advertiser side blocking.
     * For example, "foo.com".
     * </pre>
     *
     * <code>string domain = 3;</code>
     * @param value The bytes for domain to set.
     * @return This builder for chaining.
     */
    public Builder setDomainBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      domain_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private Object cat_ = "";
    /**
     * <pre>
     * Array of IAB content categories of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string cat = 4;</code>
     * @return The cat.
     */
    public String getCat() {
      Object ref = cat_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        cat_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * Array of IAB content categories of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string cat = 4;</code>
     * @return The bytes for cat.
     */
    public com.google.protobuf.ByteString
        getCatBytes() {
      Object ref = cat_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        cat_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Array of IAB content categories of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string cat = 4;</code>
     * @param value The cat to set.
     * @return This builder for chaining.
     */
    public Builder setCat(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      cat_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Array of IAB content categories of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string cat = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCat() {
      cat_ = getDefaultInstance().getCat();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Array of IAB content categories of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string cat = 4;</code>
     * @param value The bytes for cat to set.
     * @return This builder for chaining.
     */
    public Builder setCatBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      cat_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private Object sectioncat_ = "";
    /**
     * <pre>
     * Array of IAB content categories that describe the current section
     * of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string sectioncat = 5;</code>
     * @return The sectioncat.
     */
    public String getSectioncat() {
      Object ref = sectioncat_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        sectioncat_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * Array of IAB content categories that describe the current section
     * of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string sectioncat = 5;</code>
     * @return The bytes for sectioncat.
     */
    public com.google.protobuf.ByteString
        getSectioncatBytes() {
      Object ref = sectioncat_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        sectioncat_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Array of IAB content categories that describe the current section
     * of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string sectioncat = 5;</code>
     * @param value The sectioncat to set.
     * @return This builder for chaining.
     */
    public Builder setSectioncat(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      sectioncat_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Array of IAB content categories that describe the current section
     * of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string sectioncat = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearSectioncat() {
      sectioncat_ = getDefaultInstance().getSectioncat();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Array of IAB content categories that describe the current section
     * of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string sectioncat = 5;</code>
     * @param value The bytes for sectioncat to set.
     * @return This builder for chaining.
     */
    public Builder setSectioncatBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      sectioncat_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private Object pagecat_ = "";
    /**
     * <pre>
     * Array of IAB content categories that describe the current page or view
     * of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string pagecat = 6;</code>
     * @return The pagecat.
     */
    public String getPagecat() {
      Object ref = pagecat_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        pagecat_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * Array of IAB content categories that describe the current page or view
     * of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string pagecat = 6;</code>
     * @return The bytes for pagecat.
     */
    public com.google.protobuf.ByteString
        getPagecatBytes() {
      Object ref = pagecat_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        pagecat_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Array of IAB content categories that describe the current page or view
     * of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string pagecat = 6;</code>
     * @param value The pagecat to set.
     * @return This builder for chaining.
     */
    public Builder setPagecat(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      pagecat_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Array of IAB content categories that describe the current page or view
     * of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string pagecat = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPagecat() {
      pagecat_ = getDefaultInstance().getPagecat();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Array of IAB content categories that describe the current page or view
     * of the site.
     * See enum ContentCategory.
     * </pre>
     *
     * <code>string pagecat = 6;</code>
     * @param value The bytes for pagecat to set.
     * @return This builder for chaining.
     */
    public Builder setPagecatBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      pagecat_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private Object page_ = "";
    /**
     * <pre>
     * URL of the page where the impression will be shown.
     * </pre>
     *
     * <code>string page = 7;</code>
     * @return The page.
     */
    public String getPage() {
      Object ref = page_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        page_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * URL of the page where the impression will be shown.
     * </pre>
     *
     * <code>string page = 7;</code>
     * @return The bytes for page.
     */
    public com.google.protobuf.ByteString
        getPageBytes() {
      Object ref = page_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        page_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * URL of the page where the impression will be shown.
     * </pre>
     *
     * <code>string page = 7;</code>
     * @param value The page to set.
     * @return This builder for chaining.
     */
    public Builder setPage(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      page_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * URL of the page where the impression will be shown.
     * </pre>
     *
     * <code>string page = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearPage() {
      page_ = getDefaultInstance().getPage();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * URL of the page where the impression will be shown.
     * </pre>
     *
     * <code>string page = 7;</code>
     * @param value The bytes for page to set.
     * @return This builder for chaining.
     */
    public Builder setPageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      page_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private int privacypolicy_ ;
    /**
     * <pre>
     * Indicates if the site has a privacy policy, where 0 = no, 1 = yes.
     * </pre>
     *
     * <code>int32 privacypolicy = 8;</code>
     * @return The privacypolicy.
     */
    @Override
    public int getPrivacypolicy() {
      return privacypolicy_;
    }
    /**
     * <pre>
     * Indicates if the site has a privacy policy, where 0 = no, 1 = yes.
     * </pre>
     *
     * <code>int32 privacypolicy = 8;</code>
     * @param value The privacypolicy to set.
     * @return This builder for chaining.
     */
    public Builder setPrivacypolicy(int value) {

      privacypolicy_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Indicates if the site has a privacy policy, where 0 = no, 1 = yes.
     * </pre>
     *
     * <code>int32 privacypolicy = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrivacypolicy() {
      bitField0_ = (bitField0_ & ~0x00000080);
      privacypolicy_ = 0;
      onChanged();
      return this;
    }

    private Object ref_ = "";
    /**
     * <pre>
     * Referrer URL that caused navigation to the current page.
     * </pre>
     *
     * <code>string ref = 9;</code>
     * @return The ref.
     */
    public String getRef() {
      Object ref = ref_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        ref_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * Referrer URL that caused navigation to the current page.
     * </pre>
     *
     * <code>string ref = 9;</code>
     * @return The bytes for ref.
     */
    public com.google.protobuf.ByteString
        getRefBytes() {
      Object ref = ref_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ref_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Referrer URL that caused navigation to the current page.
     * </pre>
     *
     * <code>string ref = 9;</code>
     * @param value The ref to set.
     * @return This builder for chaining.
     */
    public Builder setRef(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      ref_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Referrer URL that caused navigation to the current page.
     * </pre>
     *
     * <code>string ref = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearRef() {
      ref_ = getDefaultInstance().getRef();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Referrer URL that caused navigation to the current page.
     * </pre>
     *
     * <code>string ref = 9;</code>
     * @param value The bytes for ref to set.
     * @return This builder for chaining.
     */
    public Builder setRefBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ref_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private Object search_ = "";
    /**
     * <pre>
     * Search string that caused navigation to the current page.
     * </pre>
     *
     * <code>string search = 10;</code>
     * @return The search.
     */
    public String getSearch() {
      Object ref = search_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        search_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * Search string that caused navigation to the current page.
     * </pre>
     *
     * <code>string search = 10;</code>
     * @return The bytes for search.
     */
    public com.google.protobuf.ByteString
        getSearchBytes() {
      Object ref = search_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        search_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Search string that caused navigation to the current page.
     * </pre>
     *
     * <code>string search = 10;</code>
     * @param value The search to set.
     * @return This builder for chaining.
     */
    public Builder setSearch(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      search_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Search string that caused navigation to the current page.
     * </pre>
     *
     * <code>string search = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearSearch() {
      search_ = getDefaultInstance().getSearch();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Search string that caused navigation to the current page.
     * </pre>
     *
     * <code>string search = 10;</code>
     * @param value The bytes for search to set.
     * @return This builder for chaining.
     */
    public Builder setSearchBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      search_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private Object keywords_ = "";
    /**
     * <pre>
     * Comma separated list of keywords about this site.
     * Note: OpenRTB 2.2 allowed an array-of-strings as alternate implementation
     * but this was fixed in 2.3+ where it's definitely a single string with CSV
     * content again. Compatibility with some OpenRTB 2.2 exchanges that adopted
     * the alternate representation may require custom handling of the JSON.
     * </pre>
     *
     * <code>string keywords = 11;</code>
     * @return The keywords.
     */
    public String getKeywords() {
      Object ref = keywords_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        keywords_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * Comma separated list of keywords about this site.
     * Note: OpenRTB 2.2 allowed an array-of-strings as alternate implementation
     * but this was fixed in 2.3+ where it's definitely a single string with CSV
     * content again. Compatibility with some OpenRTB 2.2 exchanges that adopted
     * the alternate representation may require custom handling of the JSON.
     * </pre>
     *
     * <code>string keywords = 11;</code>
     * @return The bytes for keywords.
     */
    public com.google.protobuf.ByteString
        getKeywordsBytes() {
      Object ref = keywords_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        keywords_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Comma separated list of keywords about this site.
     * Note: OpenRTB 2.2 allowed an array-of-strings as alternate implementation
     * but this was fixed in 2.3+ where it's definitely a single string with CSV
     * content again. Compatibility with some OpenRTB 2.2 exchanges that adopted
     * the alternate representation may require custom handling of the JSON.
     * </pre>
     *
     * <code>string keywords = 11;</code>
     * @param value The keywords to set.
     * @return This builder for chaining.
     */
    public Builder setKeywords(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      keywords_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Comma separated list of keywords about this site.
     * Note: OpenRTB 2.2 allowed an array-of-strings as alternate implementation
     * but this was fixed in 2.3+ where it's definitely a single string with CSV
     * content again. Compatibility with some OpenRTB 2.2 exchanges that adopted
     * the alternate representation may require custom handling of the JSON.
     * </pre>
     *
     * <code>string keywords = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearKeywords() {
      keywords_ = getDefaultInstance().getKeywords();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Comma separated list of keywords about this site.
     * Note: OpenRTB 2.2 allowed an array-of-strings as alternate implementation
     * but this was fixed in 2.3+ where it's definitely a single string with CSV
     * content again. Compatibility with some OpenRTB 2.2 exchanges that adopted
     * the alternate representation may require custom handling of the JSON.
     * </pre>
     *
     * <code>string keywords = 11;</code>
     * @param value The bytes for keywords to set.
     * @return This builder for chaining.
     */
    public Builder setKeywordsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      keywords_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private int mobile_ ;
    /**
     * <pre>
     * Indicates if the site has been programmed to optimize layout
     * when viewed on mobile devices, where 0 = no, 1 = yes.
     * </pre>
     *
     * <code>int32 mobile = 12;</code>
     * @return The mobile.
     */
    @Override
    public int getMobile() {
      return mobile_;
    }
    /**
     * <pre>
     * Indicates if the site has been programmed to optimize layout
     * when viewed on mobile devices, where 0 = no, 1 = yes.
     * </pre>
     *
     * <code>int32 mobile = 12;</code>
     * @param value The mobile to set.
     * @return This builder for chaining.
     */
    public Builder setMobile(int value) {

      mobile_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Indicates if the site has been programmed to optimize layout
     * when viewed on mobile devices, where 0 = no, 1 = yes.
     * </pre>
     *
     * <code>int32 mobile = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearMobile() {
      bitField0_ = (bitField0_ & ~0x00000800);
      mobile_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:dsp.Site)
  }

  // @@protoc_insertion_point(class_scope:dsp.Site)
  private static final Site DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new Site();
  }

  public static Site getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Site>
      PARSER = new com.google.protobuf.AbstractParser<Site>() {
    @Override
    public Site parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Site> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<Site> getParserForType() {
    return PARSER;
  }

  @Override
  public Site getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

