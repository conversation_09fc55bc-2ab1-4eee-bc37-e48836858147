package cn.taken.ad.logic.adv.dishuyun.dto;

import java.io.Serializable;

public class DiShYunResponseRewardInfo implements Serializable {
    private static final long serialVersionUID = 2250312772309051309L;
    /**
     * 关闭/跳过按钮展示时间，单位：秒
     */
    private Integer skipShowTime;
    /**
     * 激励任务时长，单位：秒
     * 此字段为空时，取视频时长(Video.videoDuration)字段
     */
    private Integer rewardTime;
    /**
     * 点击跳过按钮时，是否打开广告落地页。默认为 0
     * 0 不需要 1 需要
     */
    private Integer showLandingPage;

    public Integer getSkipShowTime() {
        return skipShowTime;
    }

    public void setSkipShowTime(Integer skipShowTime) {
        this.skipShowTime = skipShowTime;
    }

    public Integer getRewardTime() {
        return rewardTime;
    }

    public void setRewardTime(Integer rewardTime) {
        this.rewardTime = rewardTime;
    }

    public Integer getShowLandingPage() {
        return showLandingPage;
    }

    public void setShowLandingPage(Integer showLandingPage) {
        this.showLandingPage = showLandingPage;
    }
}
