package cn.taken.ad.logic.adv.duoying.dto;

import java.util.List;

public class DuoYingResponseNative {
    /**
     * Title 对象，多赢返回的标题的详细信息
     */
    private Title title;
    /**
     * 描述
     */
    private String desc;
    /**
     * 文字按钮
     */
    private String buttonTxt;

    private List<Img> imgList;

    private Vid vid;

    public Title getTitle() {
        return title;
    }

    public void setTitle(Title title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getButtonTxt() {
        return buttonTxt;
    }

    public void setButtonTxt(String buttonTxt) {
        this.buttonTxt = buttonTxt;
    }

    public List<Img> getImgList() {
        return imgList;
    }

    public void setImgList(List<Img> imgList) {
        this.imgList = imgList;
    }

    public Vid getVid() {
        return vid;
    }

    public void setVid(Vid vid) {
        this.vid = vid;
    }

    public static class Title{
        private String text;

        public Title() {
        }

        public Title(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }
    }
    public static class Img{
        /**
         * 图片的 url
         */
        private String url;
        /**
         * 图片的宽
         */
        private Integer width;
        /**
         * 图片的高
         */
        private Integer height;
        /**
         * 图片的 md5 值,用于图片下载完整性校验
         */
        private String md5;
        /**
         * 图片类型
         * 1 主图; 2 icon 图; 3 button 图
         */
        private Integer type;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public Integer getWidth() {
            return width;
        }

        public void setWidth(Integer width) {
            this.width = width;
        }

        public Integer getHeight() {
            return height;
        }

        public void setHeight(Integer height) {
            this.height = height;
        }

        public String getMd5() {
            return md5;
        }

        public void setMd5(String md5) {
            this.md5 = md5;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }
    }

    public static class Vid{
        /**
         * 视频的 url
         */
        private String url;
        /**
         * 视频的宽
         */
        private Integer width;
        /**
         * 视频的高
         */
        private Integer height;
        /**
         * 视频封面图片地址
         */
        private String imgSrc;
        /**
         * 视频长度，秒
         */
        private Integer duration;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public Integer getWidth() {
            return width;
        }

        public void setWidth(Integer width) {
            this.width = width;
        }

        public Integer getHeight() {
            return height;
        }

        public void setHeight(Integer height) {
            this.height = height;
        }

        public String getImgSrc() {
            return imgSrc;
        }

        public void setImgSrc(String imgSrc) {
            this.imgSrc = imgSrc;
        }

        public Integer getDuration() {
            return duration;
        }

        public void setDuration(Integer duration) {
            this.duration = duration;
        }
    }
}
