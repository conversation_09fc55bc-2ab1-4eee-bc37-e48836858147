package cn.taken.ad.logic.adv.huanrui.dto;

import java.io.Serializable;

public class HuanRuiAdvRequestNet implements Serializable {

    private static final long serialVersionUID = -8227557992297017038L;
    /**
     * 客户端IPv4 地址，如无则传IPv6
     */
    private String ip;
    /**
     * 网络名称，wifi/2g/3g/4g/5g/other
     */
    private String network;
    private String mac;
    /**
     * 运营商0: 其它，1: 移动，2:联通，3: 电信
     */
    private String operator;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
