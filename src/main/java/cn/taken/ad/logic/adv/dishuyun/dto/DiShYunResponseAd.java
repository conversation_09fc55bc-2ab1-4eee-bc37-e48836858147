package cn.taken.ad.logic.adv.dishuyun.dto;

import java.io.Serializable;
import java.util.List;

public class DiShYunResponseAd implements Serializable {
    private static final long serialVersionUID = 312625038391447822L;

    /**
     * 广告 ID
     */
    private String adId;
    /**
     * 广告物料
     */
    private DiShYunResponseMaterialMeta creative;
    /**
     * 监测信息(注：目前仅视频类会有)
     */
    private List<DiShYunResponseTrack> tracks;
    /**
     * 竞价出价，单位分
     * 当前返回的都按一价结算
     */
    private Integer price;

    public String getAdId() {
        return adId;
    }

    public void setAdId(String adId) {
        this.adId = adId;
    }

    public DiShYunResponseMaterialMeta getCreative() {
        return creative;
    }

    public void setCreative(DiShYunResponseMaterialMeta creative) {
        this.creative = creative;
    }

    public List<DiShYunResponseTrack> getTracks() {
        return tracks;
    }

    public void setTracks(List<DiShYunResponseTrack> tracks) {
        this.tracks = tracks;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }
}
