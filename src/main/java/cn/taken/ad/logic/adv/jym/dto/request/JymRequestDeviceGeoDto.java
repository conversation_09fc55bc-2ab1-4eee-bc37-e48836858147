package cn.taken.ad.logic.adv.jym.dto.request;

import java.io.Serializable;

public class JymRequestDeviceGeoDto implements Serializable {

    private static final long serialVersionUID = 3884225616242639293L;
    /**
     * PS 获取的维度信息，不传可能影响广告填充
     */
    private Float lat;
    /**
     * GPS 获取的经度信息，不传可能影响广告填充
     */
    private Float lon;
    /**
     * 10 位时间戳 精确到 s
     */
    private Integer ts;

    public Float getLat() {
        return lat;
    }

    public void setLat(Float lat) {
        this.lat = lat;
    }

    public Float getLon() {
        return lon;
    }

    public void setLon(Float lon) {
        this.lon = lon;
    }

    public Integer getTs() {
        return ts;
    }

    public void setTs(Integer ts) {
        this.ts = ts;
    }
}
