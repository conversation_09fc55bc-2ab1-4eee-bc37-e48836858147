syntax = "proto3";

//package protocol;

//option go_package = "mostmob.com/proto/protocol";
option java_package = "cn.taken.ad.logic.adv.mostmob.dto";
option java_outer_classname = "MostMobAdvRequest";
option java_multiple_files = true;


// AdxRequest 广告请求对象
message AdxRequest {
  // RequestID 媒体自定义的请求ID，每次广告请求时生成的唯一ID(128位)，便于出现问题时，提供此ID排查原因
  optional string request_id = 1;  // json:"requestId" 必填

  // DeviceID DeviceId对象
  optional AdxDeviceId device_id = 2; // json:"deviceId" 必填

  // Network Network对象
  optional AdxNetwork network = 3; // json:"network" 必填

  // Device Device对象
  optional AdxDevice device = 4;   // json:"device" 必填

  // Slot Slot对象
  optional AdxSlot slot = 5;       // json:"slot" 必填

  // Deal Deal对象
  optional AdxDeal deal = 6;       // json:"deal" 选填

  // App App对象
  optional AdxApp app = 7;         // json:"app" 必填

  // User User对象
  optional AdxUser user = 8;       // json:"user" 选填

  // CaID Caid对象
  optional AdxCaid ca_id = 9;      // json:"caId" 选填

  // Geo Geo对象
  optional AdxGeo geo = 10;        // json:"geo"  选填

  // Ext Ext对象
  optional AdxExt ext = 11;        // json:"ext" 选填
}

// AdxDeal 广告交易信息对象
message AdxDeal {
  // Bidfloor 出价明文，单位：分(竞价模式存在)
  optional int32 bidfloor = 1;  // json:"bidfloor" 选填
}

// AdxCaid Caid对象
message AdxCaid {
  //中国广告协会互联网广告标识（IOS能获取的情况下必填，填写可提高填充）
  optional string caid = 1;         // json:"caid" 尽量填
  //caid 版本号（IOS能获取的情况下必填，填写可提高填充）
  optional string version = 2;      // json:"version" 尽量填
  //caid生成时间（IOS能获取的情况下必填）
  optional int64 generate_time = 3; // json:"generateTime" 选填
  // caid 供应商：0：热云1：信通院 2：阿里因子 AAID （IOS能获取的情况下必填）
  optional int32 vendor = 4; //  json:"vendor" 尽量填
}

// AdxApp 媒体APP信息对象
message AdxApp {
  optional uint32 app_id = 1;
  // name 应用名称
  optional string name = 2;              // json:"name" 选填
  // ver_code 应用版本号
  optional string ver_name = 3;          // json:"verName" 必填
  // pkg_name 应用包名
  optional string pkg_name = 4;          // json:"pkgName" 必填
  // app_store_version 应用商店版本号
  optional string app_store_version = 5;  // json:"appStoreVersion" 选填
}

// AdxUser 用户信息实体类
message AdxUser {
  //  性别
  optional int32 gender = 1;           // json:"gender" 选填
  // age 用户年龄
  optional int32 age = 2;              // json:"age" 选填
  // installed 安装应用列表
  repeated int32 installed = 4; // json:"installed" 能获取的情况下必填
}

// AdxExt 扩展信息对象
message AdxExt {
  // support_wechat 是否支持微信
  optional bool support_wechat = 1;  // json:"supportWechat" 选填
  //  paid 是否付费
  optional string paid = 2;          // json:"paid" 选填
}

// 操作系统类型
enum OSType {
  UnknownOS = 0; // 未知
  Android = 1; // android
  IOS = 2; // ios
  HarmonyOs = 3; // 鸿蒙
}

// 设备类型
enum DeviceType {
  UnknownDevice = 0; // 未知
  Phone = 1; // 手机
  Pad = 2; // 平板
}

// 屏幕方向
enum OrientationType{
  Vertical = 0; // 竖屏
  Horizontal = 1; // 横屏
  UnknownOrientation = 3; // 未知
}

// AdxDevice 设备信息实体类
message AdxDevice {
  // os_type 操作系统类型
  optional OSType os_type = 1;              // json:"osType" 必填
  // type 设备类型
  optional DeviceType type = 2;                 // json:"type" 必填
  // os_version 操作系统版本
  optional string os_version = 3;          // json:"osVersion" 必填
  // os_ui_version 操作系统ui版本
  optional string os_ui_version = 4;        // json:"osUiVersion" 选填
  // android_api_level 安卓api版本
  optional int32 android_api_level = 5;     // json:"androidApiLevel" 必填
  // sys_compiling_time 系统编译时间
  optional string sys_compiling_time = 6;   // json:"sysCompilingTime" 尽量填
  // sys_update_time 系统更新时间
  optional string sys_update_time = 7;      // json:"sysUpdateTime" 尽量填
  // sys_startup_time 系统启动时间
  optional string sys_startup_time = 8;     // json:"sysStartupTime" 尽量填
  // birth_mark IOS设备初始化标识
  optional string birth_mark = 9;           // json:"birthMark" 尽量填
  // boot_mark 系统启动标识
  optional string boot_mark = 10;           // json:"bootMark" 尽量填
  // update_mark 系统更新标识
  optional string update_mark = 11;         // json:"updateMark" 尽量填
  // rom_version 系统rom版本
  optional string rom_version = 12;         // json:"romVersion" 尽量填
  // device_name 设备名称
  optional string device_name = 13;         // json:"deviceName" 尽量填
  // device_name_md5 设备名称md5
  optional string device_name_md5 = 14;     // json:"deviceNameMd5" 尽量填
  // cpu_num cpu数量
  optional int32 cpu_num = 15;              // json:"cpuNum" 选填
  // sys_disk_size 系统磁盘大小
  optional int64 sys_disk_size = 16;        // json:"sysDiskSize" 尽量填
  // sys_memory_size 系统内存大小
  optional int64 sys_memory_size = 17;      // json:"sysMemorySize" 尽量填
  // model 设备型号
  optional string model = 18;               // json:"protocol" 选填
  // hardware_model 设备硬件型号
  optional string hardware_model = 19;      // json:"hardwareModel" 尽量填
  // language 设备系统语言
  optional string language = 20;            // json:"language" 尽量填
  // time_zone 设备系统时区
  optional string time_zone = 21;           // json:"timeZone" 尽量填
  // hms_version hms版本
  optional string hms_version = 22;         // json:"hmsVersion" 选填
  // harmony_os_version 鸿蒙系统内核版本
  optional string harmony_os_version = 23;   // json:"harmonyOsVersion" 选填
  // hag_version 华为AppGallery应用市场版本
  optional string hag_version = 24;         // json:"hagVersion" 选填
  // support_deeplink 是否支持deeplink
  optional int32 support_deeplink = 25;     // json:"supportDeeplink" 尽量填
  // support_universal 是否支持universal
  optional int32 support_universal = 26;    // json:"supportUniversal" 尽量填
  // make 设备制造商
  optional string make = 27;                // json:"make" 必填
  // brand 设备品牌
  optional string brand = 28;               // json:"brand" 必填
  // imsi 设备IMSI
  optional string imsi = 29;                // json:"imsi" 选填
  //width 屏幕宽度
  optional int32 width = 30;                // json:"width" 必填
  // height 屏幕高度
  optional int32 height = 31;               // json:"height" 必填
  // density 屏幕密度
  optional double density = 32;             // json:"density" 尽量填
  // dpi 屏幕dpi
  optional int32 dpi = 33;                  // json:"dpi" 尽量填
  // ppi 屏幕ppi
  optional int32 ppi = 34;                  // json:"ppi" 尽量填
  // orientation 屏幕方向
  optional OrientationType orientation = 35;          // json:"orientation" 尽量填
  // screen_size 屏幕尺寸
  optional string screen_size = 36;         // json:"screenSize" 尽量填
  // serialno 设备序列号
  optional string serialno = 37;            // json:"serialno" 选填
}

// AdxCarrier 运营商信息实体类
enum CarrierType {
  UnknownCarrier = 0;//未知
  ChinaMobile = 1;//中国移动
  ChinaUnicom = 2;//中国联通
  ChinaTelecom = 3;//中国电信
}

// 网络类型
enum NetworkType {
  UnknownNetwork = 0; //未知
  Wifi = 1; //wifi
  Mobile_2G = 2; //移动2G
  Mobile_3G = 3; //移动3G
  Mobile_4G = 4; //移动4G
  Mobile_5G = 5; //移动5G
}

// AdxNetwork 网络信息实体类
message AdxNetwork {
  // user_agent 设备浏览器内核的User-Agent
  optional string user_agent = 1;    // json:"userAgent" 必填
  //ip 用户设备所处公网真实ipv4地址
  optional string ip = 2;            // json:"ip"  必填 和ipv6二选一
  //ipv6 用户设备所处公网真实ipv6地址
  optional string ipv6 = 3;          // json:"ipv6" 必填 和ip二选一
  //mac 用户设备mac地址
  optional string mac = 4;           // json:"mac" 选填
  //mac_md5 用户设备mac地址MD5小写值
  optional string mac_md5 = 5;       // json:"macMd5" 选填
  //mac_sha1 用户设备mac地址SHA1值
  optional string mac_sha1 = 6;      // json:"macSha1" 选填
  //ssid 用户设备所处wifi的ssid
  optional string ssid = 7;          // json:"ssid" 选填
  //bssid 用户设备所处wifi的bssid
  optional string bssid = 8;         // json:"bssid" 选填
  //carrier 网络运营商
  optional CarrierType carrier = 9;        // json:"carrier" 必填
  //network_type 网络类型
  optional NetworkType network_type = 10;  // json:"networkType" 必填
  //mcc 移动国家代码
  optional string mcc = 11;          // json:"mcc" 尽量填
  //mnc 移动网络号码
  optional string mnc = 12;          // json:"mnc" 尽量填
  //country 网络国家
  optional string country = 13;      // json:"country" 必填
}

// AdxDeviceId 设备标识信息实体类
message AdxDeviceId {
  // imei Android移动设备imei号
  optional string imei = 1;           // json:"imei" Android能获取的情况下必填
  //imei_md5 Android移动设备imei号MD5小写值
  optional string imei_md5 = 2;       // json:"imeiMd5" Android能获取的情况下必填
  // oaid Android移动设备oaid号
  optional string oaid = 3;           // json:"oaid" Android能获取的情况下必填
  //oaid_md5 Android移动设备oaid号MD5小写值
  optional string oaid_md5 = 4;      // json:"oaidMd5" Android能获取的情况下必填
  //android_id Android移动设备android_id号
  optional string android_id = 5;     // json:"androidId" Android能获取的情况下必填
  //android_id_md5 Android移动设备android_id号MD5小写值
  optional string android_id_md5 = 6; // json:"androidIdMd5" Android能获取的情况下必填
  //Android移动设备 android ID Sha1值
  optional string android_id_sha1 = 7;// json:"androidIdSha1" Android能获取的情况下必填
  // idfa IOS移动设备idfa号
  optional string idfa = 8;          // json:"idfa" iOS能获取的情况下必填
  //idfa_md5 IOS移动设备idfa号MD5小写值
  optional string idfa_md5 = 9;      // json:"idfaMd5" iOS能获取的情况下必填
  //idfv IOS移动设备idfv号
  optional string idfv = 10;         // json:"idfv" iOS能获取的情况下必填
  //open_udid IOS 移动设备OpenUDID
  optional string open_udid = 11;     // json:"openUdid" iOS能获取的情况下必填
  //aaid 阿里设备标识
  optional string aaid = 12; // json:"aaid" 能获取的情况下必填
}

// AdxGeo 地理信息实体类
message AdxGeo {
  // coordinate_type 坐标类型
  optional int32 coordinate_type = 1;  // json:"coordinateType" 尽量填
  // latitude 纬度
  optional double latitude = 2;        // json:"latitude" 尽量填
  // longitude 经度
  optional double longitude = 3;       // json:"longitude" 尽量填
  // timestamp 时间戳
  optional int64 timestamp = 4;        // json:"timestamp" 尽量填
}


enum AdType{
  UnknownAdType = 0; //未知
  Splash = 1; // 开屏
  Interstitial = 2; // 插屏
  Native = 3; //原生
  RewardedVideo = 4; // 激励视频
  Banner = 5; //横幅
}

// AdxSlot 广告位对象
message AdxSlot {
  // ad_slot_id 广告位id
  optional uint32 ad_slot_id = 1;  // json:"adSlotId" 必填
  // type 广告位类型
  optional AdType type = 2;        // json:"type" 必填
  // width 广告位宽度
  optional int32 width = 3;       // json:"width" 必填
  // height 广告位高度
  optional int32 height = 4;      // json:"height" 必填
}

//广告响应信息
message AdxResponse{
  // code 响应码
  int32 code = 1;
  // message 响应信息
  string message = 2;
  // ad_list 广告列表
  repeated AdxAdInfo ad_list = 3;
}

// 素材类型
enum MaterialType {
  UNKNOWN = 0; //未知素材
  SINGLE_IMAGE = 1; //单小图素材
  MULTI_IMAGE = 2;//多小图素材
  Horizontal_BIG_IMAGE = 3;//大图素材
  Vertica_BIG_IMAGE = 4;//大图素材
  Horizontal_VIDEO = 5; //横屏视频素材
  Vertical_VIDEO = 6;//竖屏视频素材
}

// 交互类型
enum InteractionType {
  UNKNOWN_NONE = 0; //无交互
  H5 = 1; //H5
  DEEPLINK = 2; //直接跳转
  UNIVERSAL_LINK = 3; //iOS拉取
  WX_MINI = 4; //微信小程序
}

// AdxAdInfo 广告信息类
message AdxAdInfo {
  //请求id 和请求携带的值一致
  optional string request_id = 1;                // json:"requestId"
  //广告类型
  optional AdType ad_type = 2;                    // json:"adType"
  //素材类型
  optional MaterialType material_type = 3;       // json:"materialType"
  //交互类型
  optional InteractionType interaction_type = 4;           // json:"interactionType"
  //标题
  optional string title = 5;                     // json:"title"
  //描述
  optional string desc = 6;                      // json:"desc"
  //素材图标
  repeated string ad_icons = 7;         // json:"adIcons"
  //素材图片
  optional string deeplink = 8;                  // json:"deeplink"
  //iOS落地页url
  optional string universal_link = 9;            // json:"universalLink"
  //落地页url
  optional string landing_page_url = 10;         // json:"landingPageUrl"
  //下载地址
  optional string download_url = 11;             // json:"downloadUrl"
  //价格(千次/分)
  optional double bid_price = 13;                 // json:"bidPrice"
  //应用信息，下载类时有值
  optional AdxAppInfo app = 14;                  // json:"app"
  //视频信息，视频类素材时有值
  optional AdxVideoInfo video = 15;              // json:"video"
  //图片信息，图片类素材时有值，单图时只有一个值，多图多个。
  repeated AdxImageInfo images = 16;    // json:"images"
  //小程序信息，交互类型为微信小程序时有值
  optional AdxMiniProgramInfo mini_program = 17; // json:"miniProgram"
  //广告上报信息
  optional AdxTrackInfo track = 18;              // json:"track"
  //竞胜上报地址，注意需要替换价格宏
  repeated string win_notice_urls = 19; // json:"winNoticeUrls"
  //竞败上报地址，注意需要替换价格宏
  repeated string loss_notice_urls = 20;// json:"lossNoticeUrls"
}

// AdxMiniProgramInfo 微信小程序信息类，交互类型为微信小程序时有值
message AdxMiniProgramInfo {
  //小程序id
  optional string mini_program_id = 1;    // json:"miniProgramId"
  //小程序路径
  optional string mini_program_path = 2;  // json:"miniProgramPath"
}

// AdxVideoInfo 视频信息类，素材类型为视频时有值
message AdxVideoInfo {
  //视频地址
  optional string url = 1;                // json:"url"
  //视频封面地址
  optional string cover_url = 2;          // json:"coverUrl"
  //视频时长
  optional int32 duration = 3;            // json:"duration"
  //视频大小
  optional int32 size = 5;                // json:"size"
  //视频宽度
  optional int32 width = 6;               // json:"width"
  //视频高度
  optional int32 height = 7;              // json:"height"
}

// AdxTrackInfo 广告上报信息
message AdxTrackInfo {
  //展示上报链接，可能有多个，需要替换时间宏
  repeated string show_urls = 1;                 // json:"showUrls"
  //点击上报链接，可能有多个，需要替换时间宏
  repeated string click_urls = 2;                // json:"clickUrls"
  //渲染失败上报链接，可能有多个，需要替换时间和失败原因宏
  repeated string render_fail_urls = 3;          // json:"renderFailUrls"
  //下载开始上报链接，可能有多个，需要替换时间宏
  repeated string start_download_urls = 8;        // json:"startDownloadUrls"
  //下载完成上报链接，可能有多个，需要替换时间宏
  repeated string finish_download_urls = 9;       // json:"finishDownloadUrls"
  //下载暂停上报链接，可能有多个，需要替换时间宏
  repeated string pause_download_urls = 10;       // json:"pauseDownloadUrls"
  //下载继续上报链接，可能有多个，需要替换时间宏
  repeated string continue_download_urls = 11;   // json:"continueDownloadUrls"
  //删除下载上报链接，可能有多个，需要替换时间宏
  repeated string delete_download_urls = 12;      // json:"deleteDownloadUrls"
  //安装开始上报链接，可能有多个，需要替换时间宏
  repeated string start_install_urls = 13;       // json:"startInstallUrls"
  //安装完成上报链接，可能有多个，需要替换时间宏
  repeated string finish_install_urls = 14;      // json:"finishInstallUrls"
  //deeplink调用成功上报链接，可能有多个，需要替换时间宏
  repeated string deeplink_success_urls = 17;    // json:"deeplinkSuccessUrls"
  //deeplink调用失败上报链接，可能有多个，需要替换时间宏
  repeated string deeplink_failure_urls = 18;    // json:"deeplinkFailureUrls"
  //视频开始上报链接，可能有多个，需要替换时间宏
  repeated string video_start_urls = 22;         // json:"videoStartUrls"
  //视频自动开始上报链接，可能有多个，需要替换时间宏
  repeated string video_auto_start_urls = 23;    // json:"videoAutoStartUrls"
  //视频播放完成上报链接，可能有多个，需要替换时间宏
  repeated string video_complete_urls = 24;      // json:"videoCompleteUrls"
  //视频播放失败上报链接，可能有多个，需要替换时间宏
  repeated string video_fail_urls = 25;          // json:"videoFailUrls"
  //视频关闭上报链接，可能有多个，需要替换时间宏
  repeated string video_close_urls = 26;     // json:"videoCloseUrls"
  //视频暂停上报链接，可能有多个，需要替换时间宏
  repeated string video_pause_urls = 27; //  json:"videoPauseUrls"
  //视频恢复上报链接，可能有多个，需要替换时间宏
  repeated string video_resume_urls = 28;// json:"videoResumeUrls"
  //视频重新播放上报链接，可能有多个，需要替换时间宏
  repeated string video_replay_urls = 29;// json:"videoReplayUrls"
  //视频播放中间上报链接，可能有多个，需要替换时间宏
  repeated string video_play_mid_urls = 31;//  json:"videoPlayMidUrls"
  //视频播放第三上报链接，可能有多个，需要替换时间宏
  repeated string video_play_third_urls = 32;// json:"videoPlayThirdUrls"
  //点击区域上报链接，可能有多个，需要替换时间宏
  optional string click_area_report_urls = 33;// json:"clickAreaReportUrls"
}

// AdxImageInfo 图片信息类
message AdxImageInfo {
  //图片链接
  optional string url = 1;    // json:"url"
  //图片宽度
  optional int32 width = 2;   // json:"width"
  //图片高度
  optional int32 height = 3;  // json:"height"
}

// AdxAppInfo 应用信息类，下载类素材会有这个信息
message AdxAppInfo {
  //应用名称
  optional string name = 1;                   // json:"name"
  //应用版本
  optional string version = 2;                 // json:"version"
  //应用包名
  optional string pkg_name = 3;               // json:"pkgName"
  //应用下载地址
  optional string icon_url = 5;               // json:"iconUrl"
  //应用大小
  optional int64 size = 6;                    // json:"size"
  //应用评分
  optional string corporate = 7;              // json:"corporate"
  //应用介绍
  optional string introduction_info = 8;      // json:"introductionInfo"
  //应用介绍链接
  optional string introduction_info_url = 9;  // json:"introductionInfoUrl"
  //应用隐私政策链接
  optional string privacy_policy_url = 10;    // json:"privacyPolicyUrl"
  //应用权限信息
  optional string permission_info = 11;       // json:"permissionInfo"
  //应用权限链接
  optional string permission_url = 12;        // json:"permissionUrl"
  //备案信息
  optional string record_number = 13;         // json:"recordNumber"
}