package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI设备信息
 */
public class MadBoxiRequestDevice implements Serializable {
    private static final long serialVersionUID = -7064608428216363189L;

    /**
     * 操作系统 1：android；2：ios；3：ott
     */
    private String os;

    /**
     * 操作系统版本
     */
    private String os_version;

    /**
     * 浏览器 user-agent
     */
    private String ua;

    /**
     * 手机终端的 ip
     */
    private String ip;

    /**
     * 手机硬件制造商/设备品牌
     */
    private String make;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 设备屏幕宽度
     */
    private Integer width;

    /**
     * 设备屏幕高度
     */
    private Integer height;

    /**
     * Android 必填。手机的 IMEI
     */
    private String imei;

    /**
     * imei 的 md5 值,32 位小写
     */
    private String imeimd5;

    /**
     * Android 必填。因 android Q 版本无法获取 imei
     */
    private String oaid;

    /**
     * oaid 的 md5 值,32 位小写
     */
    private String oaid_md5;

    /**
     * Android 系统的 AndroidID
     */
    private String androidId;

    /**
     * Android 系统的 AndroidID md5 值
     */
    private String androidid_md5;

    /**
     * 设备 WiFi 网卡 MAC 地址
     */
    private String mac;

    /**
     * mac 的 md5 值, 32 位小写
     */
    private String macmd5;

    /**
     * iOS 必填。
     */
    private String idfa;

    /**
     * idfa 的 md5 值,32 位小写
     */
    private String idfa_md5;

    /**
     * iOS 必填。iOS 设备唯一标志码，idfa 关闭时使用
     */
    private String openudid;

    /**
     * iOS 需要中广协 CAID。被废弃，优先使用 caids。
     */
    private String caid;

    /**
     * iOS 需要 caid 对应的版本号。被废弃，优先使用 caids。
     */
    private String caidVer;

    /**
     * 联网方式 0: 其它 1: WIFI，2: 2G，3: 3G，4: 4G
     */
    private Integer connectiontype;

    /**
     * 运营商 0:其它，1:移动，2:联通，3:电信
     */
    private Integer carrier;

    /**
     * 屏幕像素密度，示例：400
     */
    private Integer ppi;

    /**
     * 屏幕物理像素密度，示例：2.5
     */
    private Float pxratio;

    /**
     * 无线网SSID 名称，如获取不到可传空
     */
    private String ssid;

    /**
     * 手机 ROM 版本，如获取不到可传空
     */
    private String rom_version;

    /**
     * 华为 hms core 版本号(华为必填)
     */
    private String hms;

    /**
     * 应用商店版本号
     */
    private String ag;

    /**
     * 系统启动标识
     */
    private String boot_mark;

    /**
     * 系统更新标识
     */
    private String update_mark;

    /**
     * 阿里设备标识
     */
    private String aaid_ios;

    /**
     * 拼多多设备标识
     */
    private String paid;

    /**
     * Android Advertising ID
     */
    private String aaid;

    /**
     * 设备 machine，caid 必传参数
     */
    private String hardware_machine;

    /**
     * 设备启动时间，caid 必传参数
     */
    private String startup_time;

    /**
     * 系统版本更新时间，caid 必传参数
     */
    private String mb_time;

    /**
     * 国家，caid 必传参数
     */
    private String country_code;

    /**
     * 语言，caid 必传参数
     */
    private String language;

    /**
     * 运营商名称 ，caid 必传参数
     */
    private String carrier_name;

    /**
     * 内存空间，字节，caid 必传参数
     */
    private Integer mem_total;

    /**
     * 磁盘总空间，字节，caid 必传参数
     */
    private Integer disk_total;

    /**
     * 时区，caid 必传参数
     */
    private String local_tz_name;

    /**
     * 设备 model，caid 必传参数
     */
    private String hardware_model;

    /**
     * 系统版本，caid 必传参数
     */
    private String ios_os_version;

    /**
     * 设备名称（小写 MD5) ，caid 必传参数
     */
    private String phone_name;

    /**
     * 广告授权情况
     */
    private String auth_status;

    /**
     * cpu 数目
     */
    private String cpu_num;

    /**
     * app 包名列表（多个以逗号分割例如："com.xxx1,com.xxx2"）
     */
    private String app_list;

    /**
     * 位置信息
     */
    private MadBoxiRequestGeo geo;

    /**
     * 设备初始化时间，birth_time、device_filetime 传其一，优先传 device_filetime。
     */
    private String birth_time;

    /**
     * 设备初始化时间，单位秒保留 9 位小数。格式:"1649783466.444164583"
     */
    private String device_filetime;

    /**
     * 设备系统启动时间,精度毫秒
     */
    private String boot_time_millisec;

    /**
     * 设备系统更新时间, 精度纳秒，格式为：1621329481::433642666, :前部分为秒，:后部分为纳秒
     */
    private String update_time_nanosec;

    /**
     * caid 数组，iOS 需要
     */
    private List<MadBoxiRequestCaid> caids;

    // Getters and Setters
    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getOs_version() {
        return os_version;
    }

    public void setOs_version(String os_version) {
        this.os_version = os_version;
    }

    public String getOaid_md5() {
        return oaid_md5;
    }

    public void setOaid_md5(String oaid_md5) {
        this.oaid_md5 = oaid_md5;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMake() {
        return make;
    }

    public void setMake(String make) {
        this.make = make;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getImeimd5() {
        return imeimd5;
    }

    public void setImeimd5(String imeimd5) {
        this.imeimd5 = imeimd5;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getAndroidid_md5() {
        return androidid_md5;
    }

    public void setAndroidid_md5(String androidid_md5) {
        this.androidid_md5 = androidid_md5;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getMacmd5() {
        return macmd5;
    }

    public void setMacmd5(String macmd5) {
        this.macmd5 = macmd5;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getIdfa_md5() {
        return idfa_md5;
    }

    public void setIdfa_md5(String idfa_md5) {
        this.idfa_md5 = idfa_md5;
    }

    public String getOpenudid() {
        return openudid;
    }

    public void setOpenudid(String openudid) {
        this.openudid = openudid;
    }

    public String getCaid() {
        return caid;
    }

    public void setCaid(String caid) {
        this.caid = caid;
    }

    public String getCaidVer() {
        return caidVer;
    }

    public void setCaidVer(String caidVer) {
        this.caidVer = caidVer;
    }

    public Integer getConnectiontype() {
        return connectiontype;
    }

    public void setConnectiontype(Integer connectiontype) {
        this.connectiontype = connectiontype;
    }

    public Integer getCarrier() {
        return carrier;
    }

    public void setCarrier(Integer carrier) {
        this.carrier = carrier;
    }

    public Integer getPpi() {
        return ppi;
    }

    public void setPpi(Integer ppi) {
        this.ppi = ppi;
    }

    public Float getPxratio() {
        return pxratio;
    }

    public void setPxratio(Float pxratio) {
        this.pxratio = pxratio;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }

    public String getRom_version() {
        return rom_version;
    }

    public void setRom_version(String rom_version) {
        this.rom_version = rom_version;
    }

    public String getHms() {
        return hms;
    }

    public void setHms(String hms) {
        this.hms = hms;
    }

    public String getAg() {
        return ag;
    }

    public void setAg(String ag) {
        this.ag = ag;
    }

    public String getBoot_mark() {
        return boot_mark;
    }

    public void setBoot_mark(String boot_mark) {
        this.boot_mark = boot_mark;
    }

    public String getUpdate_mark() {
        return update_mark;
    }

    public void setUpdate_mark(String update_mark) {
        this.update_mark = update_mark;
    }

    public String getAaid_ios() {
        return aaid_ios;
    }

    public void setAaid_ios(String aaid_ios) {
        this.aaid_ios = aaid_ios;
    }

    public String getPaid() {
        return paid;
    }

    public void setPaid(String paid) {
        this.paid = paid;
    }

    public String getAaid() {
        return aaid;
    }

    public void setAaid(String aaid) {
        this.aaid = aaid;
    }

    public String getHardware_machine() {
        return hardware_machine;
    }

    public void setHardware_machine(String hardware_machine) {
        this.hardware_machine = hardware_machine;
    }

    public String getStartup_time() {
        return startup_time;
    }

    public void setStartup_time(String startup_time) {
        this.startup_time = startup_time;
    }

    public String getMb_time() {
        return mb_time;
    }

    public void setMb_time(String mb_time) {
        this.mb_time = mb_time;
    }

    public String getCountry_code() {
        return country_code;
    }

    public void setCountry_code(String country_code) {
        this.country_code = country_code;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCarrier_name() {
        return carrier_name;
    }

    public void setCarrier_name(String carrier_name) {
        this.carrier_name = carrier_name;
    }

    public Integer getMem_total() {
        return mem_total;
    }

    public void setMem_total(Integer mem_total) {
        this.mem_total = mem_total;
    }

    public Integer getDisk_total() {
        return disk_total;
    }

    public void setDisk_total(Integer disk_total) {
        this.disk_total = disk_total;
    }

    public String getLocal_tz_name() {
        return local_tz_name;
    }

    public void setLocal_tz_name(String local_tz_name) {
        this.local_tz_name = local_tz_name;
    }

    public String getHardware_model() {
        return hardware_model;
    }

    public void setHardware_model(String hardware_model) {
        this.hardware_model = hardware_model;
    }

    public String getIos_os_version() {
        return ios_os_version;
    }

    public void setIos_os_version(String ios_os_version) {
        this.ios_os_version = ios_os_version;
    }

    public String getPhone_name() {
        return phone_name;
    }

    public void setPhone_name(String phone_name) {
        this.phone_name = phone_name;
    }

    public String getAuth_status() {
        return auth_status;
    }

    public void setAuth_status(String auth_status) {
        this.auth_status = auth_status;
    }

    public String getCpu_num() {
        return cpu_num;
    }

    public void setCpu_num(String cpu_num) {
        this.cpu_num = cpu_num;
    }

    public String getApp_list() {
        return app_list;
    }

    public void setApp_list(String app_list) {
        this.app_list = app_list;
    }

    public MadBoxiRequestGeo getGeo() {
        return geo;
    }

    public void setGeo(MadBoxiRequestGeo geo) {
        this.geo = geo;
    }

    public String getBirth_time() {
        return birth_time;
    }

    public void setBirth_time(String birth_time) {
        this.birth_time = birth_time;
    }

    public String getDevice_filetime() {
        return device_filetime;
    }

    public void setDevice_filetime(String device_filetime) {
        this.device_filetime = device_filetime;
    }

    public String getBoot_time_millisec() {
        return boot_time_millisec;
    }

    public void setBoot_time_millisec(String boot_time_millisec) {
        this.boot_time_millisec = boot_time_millisec;
    }

    public String getUpdate_time_nanosec() {
        return update_time_nanosec;
    }

    public void setUpdate_time_nanosec(String update_time_nanosec) {
        this.update_time_nanosec = update_time_nanosec;
    }

    public List<MadBoxiRequestCaid> getCaids() {
        return caids;
    }

    public void setCaids(List<MadBoxiRequestCaid> caids) {
        this.caids = caids;
    }
}
