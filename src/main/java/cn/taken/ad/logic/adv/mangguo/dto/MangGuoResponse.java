package cn.taken.ad.logic.adv.mangguo.dto;

import java.io.Serializable;
import java.util.List;

public class MangGuoResponse implements Serializable {

    private static final long serialVersionUID = 3886029745575319701L;
    /**
     * 请求id，唯一标识一次请求
     */
    private String id;
    /**
     * 响应状态码
     */
    private Integer error_code;

    private List<MangGuoResponseAds> ads;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getError_code() {
        return error_code;
    }

    public void setError_code(Integer error_code) {
        this.error_code = error_code;
    }

    public List<MangGuoResponseAds> getAds() {
        return ads;
    }

    public void setAds(List<MangGuoResponseAds> ads) {
        this.ads = ads;
    }
}
