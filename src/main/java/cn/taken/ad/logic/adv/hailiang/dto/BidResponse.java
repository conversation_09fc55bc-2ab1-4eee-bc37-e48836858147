// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: halomobi_ads_entry.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.hailiang.dto;

/**
 * Protobuf type {@code dsp.BidResponse}
 */
public final class BidResponse extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:dsp.BidResponse)
    BidResponseOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      BidResponse.class.getName());
  }
  // Use BidResponse.newBuilder() to construct.
  private BidResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private BidResponse() {
    id_ = "";
    seatbid_ = java.util.Collections.emptyList();
    bidid_ = "";
    msg_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return HaiLiangDto.internal_static_dsp_BidResponse_descriptor;
  }

  @Override
  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return HaiLiangDto.internal_static_dsp_BidResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            BidResponse.class, Builder.class);
  }

  public interface SeatBidOrBuilder extends
      // @@protoc_insertion_point(interface_extends:dsp.BidResponse.SeatBid)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
     */
    java.util.List<SeatBid.Bid>
        getBidList();
    /**
     * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
     */
    SeatBid.Bid getBid(int index);
    /**
     * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
     */
    int getBidCount();
    /**
     * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
     */
    java.util.List<? extends SeatBid.BidOrBuilder>
        getBidOrBuilderList();
    /**
     * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
     */
    SeatBid.BidOrBuilder getBidOrBuilder(
        int index);

    /**
     * <code>string seat = 2;</code>
     * @return The seat.
     */
    String getSeat();
    /**
     * <code>string seat = 2;</code>
     * @return The bytes for seat.
     */
    com.google.protobuf.ByteString
        getSeatBytes();
  }
  /**
   * Protobuf type {@code dsp.BidResponse.SeatBid}
   */
  public static final class SeatBid extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:dsp.BidResponse.SeatBid)
      SeatBidOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        SeatBid.class.getName());
    }
    // Use SeatBid.newBuilder() to construct.
    private SeatBid(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SeatBid() {
      bid_ = java.util.Collections.emptyList();
      seat_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              SeatBid.class, Builder.class);
    }

    public interface BidOrBuilder extends
        // @@protoc_insertion_point(interface_extends:dsp.BidResponse.SeatBid.Bid)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>string id = 1;</code>
       * @return The id.
       */
      String getId();
      /**
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      com.google.protobuf.ByteString
          getIdBytes();

      /**
       * <code>string impid = 2;</code>
       * @return The impid.
       */
      String getImpid();
      /**
       * <code>string impid = 2;</code>
       * @return The bytes for impid.
       */
      com.google.protobuf.ByteString
          getImpidBytes();

      /**
       * <code>int32 bidtype = 3;</code>
       * @return The bidtype.
       */
      int getBidtype();

      /**
       * <code>string crid = 4;</code>
       * @return The crid.
       */
      String getCrid();
      /**
       * <code>string crid = 4;</code>
       * @return The bytes for crid.
       */
      com.google.protobuf.ByteString
          getCridBytes();

      /**
       * <code>repeated string dp_tracks = 6;</code>
       * @return A list containing the dpTracks.
       */
      java.util.List<String>
          getDpTracksList();
      /**
       * <code>repeated string dp_tracks = 6;</code>
       * @return The count of dpTracks.
       */
      int getDpTracksCount();
      /**
       * <code>repeated string dp_tracks = 6;</code>
       * @param index The index of the element to return.
       * @return The dpTracks at the given index.
       */
      String getDpTracks(int index);
      /**
       * <code>repeated string dp_tracks = 6;</code>
       * @param index The index of the value to return.
       * @return The bytes of the dpTracks at the given index.
       */
      com.google.protobuf.ByteString
          getDpTracksBytes(int index);

      /**
       * <code>repeated string imp_tracks = 7;</code>
       * @return A list containing the impTracks.
       */
      java.util.List<String>
          getImpTracksList();
      /**
       * <code>repeated string imp_tracks = 7;</code>
       * @return The count of impTracks.
       */
      int getImpTracksCount();
      /**
       * <code>repeated string imp_tracks = 7;</code>
       * @param index The index of the element to return.
       * @return The impTracks at the given index.
       */
      String getImpTracks(int index);
      /**
       * <code>repeated string imp_tracks = 7;</code>
       * @param index The index of the value to return.
       * @return The bytes of the impTracks at the given index.
       */
      com.google.protobuf.ByteString
          getImpTracksBytes(int index);

      /**
       * <code>repeated string click_tracks = 8;</code>
       * @return A list containing the clickTracks.
       */
      java.util.List<String>
          getClickTracksList();
      /**
       * <code>repeated string click_tracks = 8;</code>
       * @return The count of clickTracks.
       */
      int getClickTracksCount();
      /**
       * <code>repeated string click_tracks = 8;</code>
       * @param index The index of the element to return.
       * @return The clickTracks at the given index.
       */
      String getClickTracks(int index);
      /**
       * <code>repeated string click_tracks = 8;</code>
       * @param index The index of the value to return.
       * @return The bytes of the clickTracks at the given index.
       */
      com.google.protobuf.ByteString
          getClickTracksBytes(int index);

      /**
       * <code>repeated string down_start_tracks = 9;</code>
       * @return A list containing the downStartTracks.
       */
      java.util.List<String>
          getDownStartTracksList();
      /**
       * <code>repeated string down_start_tracks = 9;</code>
       * @return The count of downStartTracks.
       */
      int getDownStartTracksCount();
      /**
       * <code>repeated string down_start_tracks = 9;</code>
       * @param index The index of the element to return.
       * @return The downStartTracks at the given index.
       */
      String getDownStartTracks(int index);
      /**
       * <code>repeated string down_start_tracks = 9;</code>
       * @param index The index of the value to return.
       * @return The bytes of the downStartTracks at the given index.
       */
      com.google.protobuf.ByteString
          getDownStartTracksBytes(int index);

      /**
       * <code>repeated string down_complete_tracks = 10;</code>
       * @return A list containing the downCompleteTracks.
       */
      java.util.List<String>
          getDownCompleteTracksList();
      /**
       * <code>repeated string down_complete_tracks = 10;</code>
       * @return The count of downCompleteTracks.
       */
      int getDownCompleteTracksCount();
      /**
       * <code>repeated string down_complete_tracks = 10;</code>
       * @param index The index of the element to return.
       * @return The downCompleteTracks at the given index.
       */
      String getDownCompleteTracks(int index);
      /**
       * <code>repeated string down_complete_tracks = 10;</code>
       * @param index The index of the value to return.
       * @return The bytes of the downCompleteTracks at the given index.
       */
      com.google.protobuf.ByteString
          getDownCompleteTracksBytes(int index);

      /**
       * <code>repeated string install_start_tracks = 12;</code>
       * @return A list containing the installStartTracks.
       */
      java.util.List<String>
          getInstallStartTracksList();
      /**
       * <code>repeated string install_start_tracks = 12;</code>
       * @return The count of installStartTracks.
       */
      int getInstallStartTracksCount();
      /**
       * <code>repeated string install_start_tracks = 12;</code>
       * @param index The index of the element to return.
       * @return The installStartTracks at the given index.
       */
      String getInstallStartTracks(int index);
      /**
       * <code>repeated string install_start_tracks = 12;</code>
       * @param index The index of the value to return.
       * @return The bytes of the installStartTracks at the given index.
       */
      com.google.protobuf.ByteString
          getInstallStartTracksBytes(int index);

      /**
       * <code>repeated string install_complete_tracks = 13;</code>
       * @return A list containing the installCompleteTracks.
       */
      java.util.List<String>
          getInstallCompleteTracksList();
      /**
       * <code>repeated string install_complete_tracks = 13;</code>
       * @return The count of installCompleteTracks.
       */
      int getInstallCompleteTracksCount();
      /**
       * <code>repeated string install_complete_tracks = 13;</code>
       * @param index The index of the element to return.
       * @return The installCompleteTracks at the given index.
       */
      String getInstallCompleteTracks(int index);
      /**
       * <code>repeated string install_complete_tracks = 13;</code>
       * @param index The index of the value to return.
       * @return The bytes of the installCompleteTracks at the given index.
       */
      com.google.protobuf.ByteString
          getInstallCompleteTracksBytes(int index);

      /**
       * <code>string appname = 14;</code>
       * @return The appname.
       */
      String getAppname();
      /**
       * <code>string appname = 14;</code>
       * @return The bytes for appname.
       */
      com.google.protobuf.ByteString
          getAppnameBytes();

      /**
       * <code>string bundle = 15;</code>
       * @return The bundle.
       */
      String getBundle();
      /**
       * <code>string bundle = 15;</code>
       * @return The bytes for bundle.
       */
      com.google.protobuf.ByteString
          getBundleBytes();

      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
       * @return Whether the image field is set.
       */
      boolean hasImage();
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
       * @return The image.
       */
      Bid.ResImage getImage();
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
       */
      Bid.ResImageOrBuilder getImageOrBuilder();

      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
       */
      java.util.List<Bid.ResImage>
          getImagesList();
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
       */
      Bid.ResImage getImages(int index);
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
       */
      int getImagesCount();
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
       */
      java.util.List<? extends Bid.ResImageOrBuilder>
          getImagesOrBuilderList();
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
       */
      Bid.ResImageOrBuilder getImagesOrBuilder(
          int index);

      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
       * @return Whether the logo field is set.
       */
      boolean hasLogo();
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
       * @return The logo.
       */
      Bid.ResImage getLogo();
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
       */
      Bid.ResImageOrBuilder getLogoOrBuilder();

      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
       * @return Whether the appicon field is set.
       */
      boolean hasAppicon();
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
       * @return The appicon.
       */
      Bid.ResImage getAppicon();
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
       */
      Bid.ResImageOrBuilder getAppiconOrBuilder();

      /**
       * <code>string title = 20;</code>
       * @return The title.
       */
      String getTitle();
      /**
       * <code>string title = 20;</code>
       * @return The bytes for title.
       */
      com.google.protobuf.ByteString
          getTitleBytes();

      /**
       * <code>string des = 21;</code>
       * @return The des.
       */
      String getDes();
      /**
       * <code>string des = 21;</code>
       * @return The bytes for des.
       */
      com.google.protobuf.ByteString
          getDesBytes();

      /**
       * <code>bool is_download = 23;</code>
       * @return The isDownload.
       */
      boolean getIsDownload();

      /**
       * <code>bool is_deep = 24;</code>
       * @return The isDeep.
       */
      boolean getIsDeep();

      /**
       * <code>string file_url = 25;</code>
       * @return The fileUrl.
       */
      String getFileUrl();
      /**
       * <code>string file_url = 25;</code>
       * @return The bytes for fileUrl.
       */
      com.google.protobuf.ByteString
          getFileUrlBytes();

      /**
       * <code>string landing_url = 26;</code>
       * @return The landingUrl.
       */
      String getLandingUrl();
      /**
       * <code>string landing_url = 26;</code>
       * @return The bytes for landingUrl.
       */
      com.google.protobuf.ByteString
          getLandingUrlBytes();

      /**
       * <code>string deeplink = 27;</code>
       * @return The deeplink.
       */
      String getDeeplink();
      /**
       * <code>string deeplink = 27;</code>
       * @return The bytes for deeplink.
       */
      com.google.protobuf.ByteString
          getDeeplinkBytes();

      /**
       * <code>int64 expirationTime = 28;</code>
       * @return The expirationTime.
       */
      long getExpirationTime();

      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
       * @return Whether the video field is set.
       */
      boolean hasVideo();
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
       * @return The video.
       */
      Bid.ResVideo getVideo();
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
       */
      Bid.ResVideoOrBuilder getVideoOrBuilder();

      /**
       * <code>repeated string video_start_tracks = 30;</code>
       * @return A list containing the videoStartTracks.
       */
      java.util.List<String>
          getVideoStartTracksList();
      /**
       * <code>repeated string video_start_tracks = 30;</code>
       * @return The count of videoStartTracks.
       */
      int getVideoStartTracksCount();
      /**
       * <code>repeated string video_start_tracks = 30;</code>
       * @param index The index of the element to return.
       * @return The videoStartTracks at the given index.
       */
      String getVideoStartTracks(int index);
      /**
       * <code>repeated string video_start_tracks = 30;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoStartTracks at the given index.
       */
      com.google.protobuf.ByteString
          getVideoStartTracksBytes(int index);

      /**
       * <code>repeated string video_complete_tracks = 31;</code>
       * @return A list containing the videoCompleteTracks.
       */
      java.util.List<String>
          getVideoCompleteTracksList();
      /**
       * <code>repeated string video_complete_tracks = 31;</code>
       * @return The count of videoCompleteTracks.
       */
      int getVideoCompleteTracksCount();
      /**
       * <code>repeated string video_complete_tracks = 31;</code>
       * @param index The index of the element to return.
       * @return The videoCompleteTracks at the given index.
       */
      String getVideoCompleteTracks(int index);
      /**
       * <code>repeated string video_complete_tracks = 31;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoCompleteTracks at the given index.
       */
      com.google.protobuf.ByteString
          getVideoCompleteTracksBytes(int index);

      /**
       * <code>repeated string ad_close_tracks = 32;</code>
       * @return A list containing the adCloseTracks.
       */
      java.util.List<String>
          getAdCloseTracksList();
      /**
       * <code>repeated string ad_close_tracks = 32;</code>
       * @return The count of adCloseTracks.
       */
      int getAdCloseTracksCount();
      /**
       * <code>repeated string ad_close_tracks = 32;</code>
       * @param index The index of the element to return.
       * @return The adCloseTracks at the given index.
       */
      String getAdCloseTracks(int index);
      /**
       * <code>repeated string ad_close_tracks = 32;</code>
       * @param index The index of the value to return.
       * @return The bytes of the adCloseTracks at the given index.
       */
      com.google.protobuf.ByteString
          getAdCloseTracksBytes(int index);

      /**
       * <code>int32 macro = 33;</code>
       * @return The macro.
       */
      int getMacro();

      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
       */
      java.util.List<Bid.Feedbacks>
          getFeedbacksList();
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
       */
      Bid.Feedbacks getFeedbacks(int index);
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
       */
      int getFeedbacksCount();
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
       */
      java.util.List<? extends Bid.FeedbacksOrBuilder>
          getFeedbacksOrBuilderList();
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
       */
      Bid.FeedbacksOrBuilder getFeedbacksOrBuilder(
          int index);

      /**
       * <code>repeated string video_play_first_quartile_tracks = 35;</code>
       * @return A list containing the videoPlayFirstQuartileTracks.
       */
      java.util.List<String>
          getVideoPlayFirstQuartileTracksList();
      /**
       * <code>repeated string video_play_first_quartile_tracks = 35;</code>
       * @return The count of videoPlayFirstQuartileTracks.
       */
      int getVideoPlayFirstQuartileTracksCount();
      /**
       * <code>repeated string video_play_first_quartile_tracks = 35;</code>
       * @param index The index of the element to return.
       * @return The videoPlayFirstQuartileTracks at the given index.
       */
      String getVideoPlayFirstQuartileTracks(int index);
      /**
       * <code>repeated string video_play_first_quartile_tracks = 35;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoPlayFirstQuartileTracks at the given index.
       */
      com.google.protobuf.ByteString
          getVideoPlayFirstQuartileTracksBytes(int index);

      /**
       * <code>repeated string video_play_midpoint_tracks = 36;</code>
       * @return A list containing the videoPlayMidpointTracks.
       */
      java.util.List<String>
          getVideoPlayMidpointTracksList();
      /**
       * <code>repeated string video_play_midpoint_tracks = 36;</code>
       * @return The count of videoPlayMidpointTracks.
       */
      int getVideoPlayMidpointTracksCount();
      /**
       * <code>repeated string video_play_midpoint_tracks = 36;</code>
       * @param index The index of the element to return.
       * @return The videoPlayMidpointTracks at the given index.
       */
      String getVideoPlayMidpointTracks(int index);
      /**
       * <code>repeated string video_play_midpoint_tracks = 36;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoPlayMidpointTracks at the given index.
       */
      com.google.protobuf.ByteString
          getVideoPlayMidpointTracksBytes(int index);

      /**
       * <code>repeated string video_play_third_quartile_tracks = 37;</code>
       * @return A list containing the videoPlayThirdQuartileTracks.
       */
      java.util.List<String>
          getVideoPlayThirdQuartileTracksList();
      /**
       * <code>repeated string video_play_third_quartile_tracks = 37;</code>
       * @return The count of videoPlayThirdQuartileTracks.
       */
      int getVideoPlayThirdQuartileTracksCount();
      /**
       * <code>repeated string video_play_third_quartile_tracks = 37;</code>
       * @param index The index of the element to return.
       * @return The videoPlayThirdQuartileTracks at the given index.
       */
      String getVideoPlayThirdQuartileTracks(int index);
      /**
       * <code>repeated string video_play_third_quartile_tracks = 37;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoPlayThirdQuartileTracks at the given index.
       */
      com.google.protobuf.ByteString
          getVideoPlayThirdQuartileTracksBytes(int index);

      /**
       * <code>repeated string video_skip_tracks = 38;</code>
       * @return A list containing the videoSkipTracks.
       */
      java.util.List<String>
          getVideoSkipTracksList();
      /**
       * <code>repeated string video_skip_tracks = 38;</code>
       * @return The count of videoSkipTracks.
       */
      int getVideoSkipTracksCount();
      /**
       * <code>repeated string video_skip_tracks = 38;</code>
       * @param index The index of the element to return.
       * @return The videoSkipTracks at the given index.
       */
      String getVideoSkipTracks(int index);
      /**
       * <code>repeated string video_skip_tracks = 38;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoSkipTracks at the given index.
       */
      com.google.protobuf.ByteString
          getVideoSkipTracksBytes(int index);

      /**
       * <code>repeated string video_click_tracks = 39;</code>
       * @return A list containing the videoClickTracks.
       */
      java.util.List<String>
          getVideoClickTracksList();
      /**
       * <code>repeated string video_click_tracks = 39;</code>
       * @return The count of videoClickTracks.
       */
      int getVideoClickTracksCount();
      /**
       * <code>repeated string video_click_tracks = 39;</code>
       * @param index The index of the element to return.
       * @return The videoClickTracks at the given index.
       */
      String getVideoClickTracks(int index);
      /**
       * <code>repeated string video_click_tracks = 39;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoClickTracks at the given index.
       */
      com.google.protobuf.ByteString
          getVideoClickTracksBytes(int index);

      /**
       * <code>repeated string video_mute_tracks = 40;</code>
       * @return A list containing the videoMuteTracks.
       */
      java.util.List<String>
          getVideoMuteTracksList();
      /**
       * <code>repeated string video_mute_tracks = 40;</code>
       * @return The count of videoMuteTracks.
       */
      int getVideoMuteTracksCount();
      /**
       * <code>repeated string video_mute_tracks = 40;</code>
       * @param index The index of the element to return.
       * @return The videoMuteTracks at the given index.
       */
      String getVideoMuteTracks(int index);
      /**
       * <code>repeated string video_mute_tracks = 40;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoMuteTracks at the given index.
       */
      com.google.protobuf.ByteString
          getVideoMuteTracksBytes(int index);

      /**
       * <code>repeated string video_unmute_tracks = 41;</code>
       * @return A list containing the videoUnmuteTracks.
       */
      java.util.List<String>
          getVideoUnmuteTracksList();
      /**
       * <code>repeated string video_unmute_tracks = 41;</code>
       * @return The count of videoUnmuteTracks.
       */
      int getVideoUnmuteTracksCount();
      /**
       * <code>repeated string video_unmute_tracks = 41;</code>
       * @param index The index of the element to return.
       * @return The videoUnmuteTracks at the given index.
       */
      String getVideoUnmuteTracks(int index);
      /**
       * <code>repeated string video_unmute_tracks = 41;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoUnmuteTracks at the given index.
       */
      com.google.protobuf.ByteString
          getVideoUnmuteTracksBytes(int index);

      /**
       * <code>string source = 42;</code>
       * @return The source.
       */
      String getSource();
      /**
       * <code>string source = 42;</code>
       * @return The bytes for source.
       */
      com.google.protobuf.ByteString
          getSourceBytes();

      /**
       * <code>int32 price = 43;</code>
       * @return The price.
       */
      int getPrice();

      /**
       * <code>string nurl = 44;</code>
       * @return The nurl.
       */
      String getNurl();
      /**
       * <code>string nurl = 44;</code>
       * @return The bytes for nurl.
       */
      com.google.protobuf.ByteString
          getNurlBytes();

      /**
       * <code>string lurl = 45;</code>
       * @return The lurl.
       */
      String getLurl();
      /**
       * <code>string lurl = 45;</code>
       * @return The bytes for lurl.
       */
      com.google.protobuf.ByteString
          getLurlBytes();

      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
       * @return Whether the ext field is set.
       */
      boolean hasExt();
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
       * @return The ext.
       */
      Bid.Ext getExt();
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
       */
      Bid.ExtOrBuilder getExtOrBuilder();

      /**
       * <code>string universal_link = 47;</code>
       * @return The universalLink.
       */
      String getUniversalLink();
      /**
       * <code>string universal_link = 47;</code>
       * @return The bytes for universalLink.
       */
      com.google.protobuf.ByteString
          getUniversalLinkBytes();

      /**
       * <code>repeated string dp_fail_tracks = 48;</code>
       * @return A list containing the dpFailTracks.
       */
      java.util.List<String>
          getDpFailTracksList();
      /**
       * <code>repeated string dp_fail_tracks = 48;</code>
       * @return The count of dpFailTracks.
       */
      int getDpFailTracksCount();
      /**
       * <code>repeated string dp_fail_tracks = 48;</code>
       * @param index The index of the element to return.
       * @return The dpFailTracks at the given index.
       */
      String getDpFailTracks(int index);
      /**
       * <code>repeated string dp_fail_tracks = 48;</code>
       * @param index The index of the value to return.
       * @return The bytes of the dpFailTracks at the given index.
       */
      com.google.protobuf.ByteString
          getDpFailTracksBytes(int index);
    }
    /**
     * Protobuf type {@code dsp.BidResponse.SeatBid.Bid}
     */
    public static final class Bid extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:dsp.BidResponse.SeatBid.Bid)
        BidOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 28,
          /* patch= */ 3,
          /* suffix= */ "",
          Bid.class.getName());
      }
      // Use Bid.newBuilder() to construct.
      private Bid(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private Bid() {
        id_ = "";
        impid_ = "";
        crid_ = "";
        dpTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        impTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        clickTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        downStartTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        downCompleteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        installStartTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        installCompleteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        appname_ = "";
        bundle_ = "";
        images_ = java.util.Collections.emptyList();
        title_ = "";
        des_ = "";
        fileUrl_ = "";
        landingUrl_ = "";
        deeplink_ = "";
        videoStartTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        videoCompleteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        adCloseTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        feedbacks_ = java.util.Collections.emptyList();
        videoPlayFirstQuartileTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        videoPlayMidpointTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        videoPlayThirdQuartileTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        videoSkipTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        videoClickTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        videoMuteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        videoUnmuteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        source_ = "";
        nurl_ = "";
        lurl_ = "";
        universalLink_ = "";
        dpFailTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Bid.class, Builder.class);
      }

      public interface FeedbacksOrBuilder extends
          // @@protoc_insertion_point(interface_extends:dsp.BidResponse.SeatBid.Bid.Feedbacks)
          com.google.protobuf.MessageOrBuilder {

        /**
         * <code>string action = 1;</code>
         * @return The action.
         */
        String getAction();
        /**
         * <code>string action = 1;</code>
         * @return The bytes for action.
         */
        com.google.protobuf.ByteString
            getActionBytes();

        /**
         * <code>repeated string urls = 2;</code>
         * @return A list containing the urls.
         */
        java.util.List<String>
            getUrlsList();
        /**
         * <code>repeated string urls = 2;</code>
         * @return The count of urls.
         */
        int getUrlsCount();
        /**
         * <code>repeated string urls = 2;</code>
         * @param index The index of the element to return.
         * @return The urls at the given index.
         */
        String getUrls(int index);
        /**
         * <code>repeated string urls = 2;</code>
         * @param index The index of the value to return.
         * @return The bytes of the urls at the given index.
         */
        com.google.protobuf.ByteString
            getUrlsBytes(int index);
      }
      /**
       * Protobuf type {@code dsp.BidResponse.SeatBid.Bid.Feedbacks}
       */
      public static final class Feedbacks extends
          com.google.protobuf.GeneratedMessage implements
          // @@protoc_insertion_point(message_implements:dsp.BidResponse.SeatBid.Bid.Feedbacks)
          FeedbacksOrBuilder {
      private static final long serialVersionUID = 0L;
        static {
          com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
            com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
            /* major= */ 4,
            /* minor= */ 28,
            /* patch= */ 3,
            /* suffix= */ "",
            Feedbacks.class.getName());
        }
        // Use Feedbacks.newBuilder() to construct.
        private Feedbacks(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
          super(builder);
        }
        private Feedbacks() {
          action_ = "";
          urls_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_Feedbacks_descriptor;
        }

        @Override
        protected FieldAccessorTable
            internalGetFieldAccessorTable() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_Feedbacks_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  Feedbacks.class, Builder.class);
        }

        public static final int ACTION_FIELD_NUMBER = 1;
        @SuppressWarnings("serial")
        private volatile Object action_ = "";
        /**
         * <code>string action = 1;</code>
         * @return The action.
         */
        @Override
        public String getAction() {
          Object ref = action_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            action_ = s;
            return s;
          }
        }
        /**
         * <code>string action = 1;</code>
         * @return The bytes for action.
         */
        @Override
        public com.google.protobuf.ByteString
            getActionBytes() {
          Object ref = action_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            action_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int URLS_FIELD_NUMBER = 2;
        @SuppressWarnings("serial")
        private com.google.protobuf.LazyStringArrayList urls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        /**
         * <code>repeated string urls = 2;</code>
         * @return A list containing the urls.
         */
        public com.google.protobuf.ProtocolStringList
            getUrlsList() {
          return urls_;
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @return The count of urls.
         */
        public int getUrlsCount() {
          return urls_.size();
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @param index The index of the element to return.
         * @return The urls at the given index.
         */
        public String getUrls(int index) {
          return urls_.get(index);
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @param index The index of the value to return.
         * @return The bytes of the urls at the given index.
         */
        public com.google.protobuf.ByteString
            getUrlsBytes(int index) {
          return urls_.getByteString(index);
        }

        private byte memoizedIsInitialized = -1;
        @Override
        public final boolean isInitialized() {
          byte isInitialized = memoizedIsInitialized;
          if (isInitialized == 1) return true;
          if (isInitialized == 0) return false;

          memoizedIsInitialized = 1;
          return true;
        }

        @Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                            throws java.io.IOException {
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(action_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 1, action_);
          }
          for (int i = 0; i < urls_.size(); i++) {
            com.google.protobuf.GeneratedMessage.writeString(output, 2, urls_.getRaw(i));
          }
          getUnknownFields().writeTo(output);
        }

        @Override
        public int getSerializedSize() {
          int size = memoizedSize;
          if (size != -1) return size;

          size = 0;
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(action_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(1, action_);
          }
          {
            int dataSize = 0;
            for (int i = 0; i < urls_.size(); i++) {
              dataSize += computeStringSizeNoTag(urls_.getRaw(i));
            }
            size += dataSize;
            size += 1 * getUrlsList().size();
          }
          size += getUnknownFields().getSerializedSize();
          memoizedSize = size;
          return size;
        }

        @Override
        public boolean equals(final Object obj) {
          if (obj == this) {
           return true;
          }
          if (!(obj instanceof Feedbacks)) {
            return super.equals(obj);
          }
          Feedbacks other = (Feedbacks) obj;

          if (!getAction()
              .equals(other.getAction())) return false;
          if (!getUrlsList()
              .equals(other.getUrlsList())) return false;
          if (!getUnknownFields().equals(other.getUnknownFields())) return false;
          return true;
        }

        @Override
        public int hashCode() {
          if (memoizedHashCode != 0) {
            return memoizedHashCode;
          }
          int hash = 41;
          hash = (19 * hash) + getDescriptor().hashCode();
          hash = (37 * hash) + ACTION_FIELD_NUMBER;
          hash = (53 * hash) + getAction().hashCode();
          if (getUrlsCount() > 0) {
            hash = (37 * hash) + URLS_FIELD_NUMBER;
            hash = (53 * hash) + getUrlsList().hashCode();
          }
          hash = (29 * hash) + getUnknownFields().hashCode();
          memoizedHashCode = hash;
          return hash;
        }

        public static Feedbacks parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static Feedbacks parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Feedbacks parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static Feedbacks parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Feedbacks parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static Feedbacks parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Feedbacks parseFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static Feedbacks parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static Feedbacks parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input);
        }

        public static Feedbacks parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static Feedbacks parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static Feedbacks parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @Override
        public Builder newBuilderForType() { return newBuilder(); }
        public static Builder newBuilder() {
          return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(Feedbacks prototype) {
          return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        @Override
        public Builder toBuilder() {
          return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
        }

        @Override
        protected Builder newBuilderForType(
            BuilderParent parent) {
          Builder builder = new Builder(parent);
          return builder;
        }
        /**
         * Protobuf type {@code dsp.BidResponse.SeatBid.Bid.Feedbacks}
         */
        public static final class Builder extends
            com.google.protobuf.GeneratedMessage.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:dsp.BidResponse.SeatBid.Bid.Feedbacks)
            FeedbacksOrBuilder {
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_Feedbacks_descriptor;
          }

          @Override
          protected FieldAccessorTable
              internalGetFieldAccessorTable() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_Feedbacks_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    Feedbacks.class, Builder.class);
          }

          // Construct using cn.taken.ad.logic.prossor.hailiang.dto.BidResponse.SeatBid.Bid.Feedbacks.newBuilder()
          private Builder() {

          }

          private Builder(
              BuilderParent parent) {
            super(parent);

          }
          @Override
          public Builder clear() {
            super.clear();
            bitField0_ = 0;
            action_ = "";
            urls_ =
                com.google.protobuf.LazyStringArrayList.emptyList();
            return this;
          }

          @Override
          public com.google.protobuf.Descriptors.Descriptor
              getDescriptorForType() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_Feedbacks_descriptor;
          }

          @Override
          public Feedbacks getDefaultInstanceForType() {
            return Feedbacks.getDefaultInstance();
          }

          @Override
          public Feedbacks build() {
            Feedbacks result = buildPartial();
            if (!result.isInitialized()) {
              throw newUninitializedMessageException(result);
            }
            return result;
          }

          @Override
          public Feedbacks buildPartial() {
            Feedbacks result = new Feedbacks(this);
            if (bitField0_ != 0) { buildPartial0(result); }
            onBuilt();
            return result;
          }

          private void buildPartial0(Feedbacks result) {
            int from_bitField0_ = bitField0_;
            if (((from_bitField0_ & 0x00000001) != 0)) {
              result.action_ = action_;
            }
            if (((from_bitField0_ & 0x00000002) != 0)) {
              urls_.makeImmutable();
              result.urls_ = urls_;
            }
          }

          @Override
          public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof Feedbacks) {
              return mergeFrom((Feedbacks)other);
            } else {
              super.mergeFrom(other);
              return this;
            }
          }

          public Builder mergeFrom(Feedbacks other) {
            if (other == Feedbacks.getDefaultInstance()) return this;
            if (!other.getAction().isEmpty()) {
              action_ = other.action_;
              bitField0_ |= 0x00000001;
              onChanged();
            }
            if (!other.urls_.isEmpty()) {
              if (urls_.isEmpty()) {
                urls_ = other.urls_;
                bitField0_ |= 0x00000002;
              } else {
                ensureUrlsIsMutable();
                urls_.addAll(other.urls_);
              }
              onChanged();
            }
            this.mergeUnknownFields(other.getUnknownFields());
            onChanged();
            return this;
          }

          @Override
          public final boolean isInitialized() {
            return true;
          }

          @Override
          public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            if (extensionRegistry == null) {
              throw new NullPointerException();
            }
            try {
              boolean done = false;
              while (!done) {
                int tag = input.readTag();
                switch (tag) {
                  case 0:
                    done = true;
                    break;
                  case 10: {
                    action_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000001;
                    break;
                  } // case 10
                  case 18: {
                    String s = input.readStringRequireUtf8();
                    ensureUrlsIsMutable();
                    urls_.add(s);
                    break;
                  } // case 18
                  default: {
                    if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                      done = true; // was an endgroup tag
                    }
                    break;
                  } // default:
                } // switch (tag)
              } // while (!done)
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.unwrapIOException();
            } finally {
              onChanged();
            } // finally
            return this;
          }
          private int bitField0_;

          private Object action_ = "";
          /**
           * <code>string action = 1;</code>
           * @return The action.
           */
          public String getAction() {
            Object ref = action_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              action_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string action = 1;</code>
           * @return The bytes for action.
           */
          public com.google.protobuf.ByteString
              getActionBytes() {
            Object ref = action_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              action_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string action = 1;</code>
           * @param value The action to set.
           * @return This builder for chaining.
           */
          public Builder setAction(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            action_ = value;
            bitField0_ |= 0x00000001;
            onChanged();
            return this;
          }
          /**
           * <code>string action = 1;</code>
           * @return This builder for chaining.
           */
          public Builder clearAction() {
            action_ = getDefaultInstance().getAction();
            bitField0_ = (bitField0_ & ~0x00000001);
            onChanged();
            return this;
          }
          /**
           * <code>string action = 1;</code>
           * @param value The bytes for action to set.
           * @return This builder for chaining.
           */
          public Builder setActionBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            action_ = value;
            bitField0_ |= 0x00000001;
            onChanged();
            return this;
          }

          private com.google.protobuf.LazyStringArrayList urls_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          private void ensureUrlsIsMutable() {
            if (!urls_.isModifiable()) {
              urls_ = new com.google.protobuf.LazyStringArrayList(urls_);
            }
            bitField0_ |= 0x00000002;
          }
          /**
           * <code>repeated string urls = 2;</code>
           * @return A list containing the urls.
           */
          public com.google.protobuf.ProtocolStringList
              getUrlsList() {
            urls_.makeImmutable();
            return urls_;
          }
          /**
           * <code>repeated string urls = 2;</code>
           * @return The count of urls.
           */
          public int getUrlsCount() {
            return urls_.size();
          }
          /**
           * <code>repeated string urls = 2;</code>
           * @param index The index of the element to return.
           * @return The urls at the given index.
           */
          public String getUrls(int index) {
            return urls_.get(index);
          }
          /**
           * <code>repeated string urls = 2;</code>
           * @param index The index of the value to return.
           * @return The bytes of the urls at the given index.
           */
          public com.google.protobuf.ByteString
              getUrlsBytes(int index) {
            return urls_.getByteString(index);
          }
          /**
           * <code>repeated string urls = 2;</code>
           * @param index The index to set the value at.
           * @param value The urls to set.
           * @return This builder for chaining.
           */
          public Builder setUrls(
              int index, String value) {
            if (value == null) { throw new NullPointerException(); }
            ensureUrlsIsMutable();
            urls_.set(index, value);
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string urls = 2;</code>
           * @param value The urls to add.
           * @return This builder for chaining.
           */
          public Builder addUrls(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            ensureUrlsIsMutable();
            urls_.add(value);
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string urls = 2;</code>
           * @param values The urls to add.
           * @return This builder for chaining.
           */
          public Builder addAllUrls(
              Iterable<String> values) {
            ensureUrlsIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, urls_);
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string urls = 2;</code>
           * @return This builder for chaining.
           */
          public Builder clearUrls() {
            urls_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
            bitField0_ = (bitField0_ & ~0x00000002);;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string urls = 2;</code>
           * @param value The bytes of the urls to add.
           * @return This builder for chaining.
           */
          public Builder addUrlsBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            ensureUrlsIsMutable();
            urls_.add(value);
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }

          // @@protoc_insertion_point(builder_scope:dsp.BidResponse.SeatBid.Bid.Feedbacks)
        }

        // @@protoc_insertion_point(class_scope:dsp.BidResponse.SeatBid.Bid.Feedbacks)
        private static final Feedbacks DEFAULT_INSTANCE;
        static {
          DEFAULT_INSTANCE = new Feedbacks();
        }

        public static Feedbacks getDefaultInstance() {
          return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<Feedbacks>
            PARSER = new com.google.protobuf.AbstractParser<Feedbacks>() {
          @Override
          public Feedbacks parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            Builder builder = newBuilder();
            try {
              builder.mergeFrom(input, extensionRegistry);
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.setUnfinishedMessage(builder.buildPartial());
            } catch (com.google.protobuf.UninitializedMessageException e) {
              throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
            } catch (java.io.IOException e) {
              throw new com.google.protobuf.InvalidProtocolBufferException(e)
                  .setUnfinishedMessage(builder.buildPartial());
            }
            return builder.buildPartial();
          }
        };

        public static com.google.protobuf.Parser<Feedbacks> parser() {
          return PARSER;
        }

        @Override
        public com.google.protobuf.Parser<Feedbacks> getParserForType() {
          return PARSER;
        }

        @Override
        public Feedbacks getDefaultInstanceForType() {
          return DEFAULT_INSTANCE;
        }

      }

      public interface ResImageOrBuilder extends
          // @@protoc_insertion_point(interface_extends:dsp.BidResponse.SeatBid.Bid.ResImage)
          com.google.protobuf.MessageOrBuilder {

        /**
         * <code>string url = 1;</code>
         * @return The url.
         */
        String getUrl();
        /**
         * <code>string url = 1;</code>
         * @return The bytes for url.
         */
        com.google.protobuf.ByteString
            getUrlBytes();

        /**
         * <code>int64 w = 2;</code>
         * @return The w.
         */
        long getW();

        /**
         * <code>int64 h = 3;</code>
         * @return The h.
         */
        long getH();

        /**
         * <code>string type = 4;</code>
         * @return The type.
         */
        String getType();
        /**
         * <code>string type = 4;</code>
         * @return The bytes for type.
         */
        com.google.protobuf.ByteString
            getTypeBytes();

        /**
         * <code>int64 kb = 5;</code>
         * @return The kb.
         */
        long getKb();
      }
      /**
       * Protobuf type {@code dsp.BidResponse.SeatBid.Bid.ResImage}
       */
      public static final class ResImage extends
          com.google.protobuf.GeneratedMessage implements
          // @@protoc_insertion_point(message_implements:dsp.BidResponse.SeatBid.Bid.ResImage)
          ResImageOrBuilder {
      private static final long serialVersionUID = 0L;
        static {
          com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
            com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
            /* major= */ 4,
            /* minor= */ 28,
            /* patch= */ 3,
            /* suffix= */ "",
            ResImage.class.getName());
        }
        // Use ResImage.newBuilder() to construct.
        private ResImage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
          super(builder);
        }
        private ResImage() {
          url_ = "";
          type_ = "";
        }

        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_ResImage_descriptor;
        }

        @Override
        protected FieldAccessorTable
            internalGetFieldAccessorTable() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_ResImage_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  ResImage.class, Builder.class);
        }

        public static final int URL_FIELD_NUMBER = 1;
        @SuppressWarnings("serial")
        private volatile Object url_ = "";
        /**
         * <code>string url = 1;</code>
         * @return The url.
         */
        @Override
        public String getUrl() {
          Object ref = url_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            url_ = s;
            return s;
          }
        }
        /**
         * <code>string url = 1;</code>
         * @return The bytes for url.
         */
        @Override
        public com.google.protobuf.ByteString
            getUrlBytes() {
          Object ref = url_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            url_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int W_FIELD_NUMBER = 2;
        private long w_ = 0L;
        /**
         * <code>int64 w = 2;</code>
         * @return The w.
         */
        @Override
        public long getW() {
          return w_;
        }

        public static final int H_FIELD_NUMBER = 3;
        private long h_ = 0L;
        /**
         * <code>int64 h = 3;</code>
         * @return The h.
         */
        @Override
        public long getH() {
          return h_;
        }

        public static final int TYPE_FIELD_NUMBER = 4;
        @SuppressWarnings("serial")
        private volatile Object type_ = "";
        /**
         * <code>string type = 4;</code>
         * @return The type.
         */
        @Override
        public String getType() {
          Object ref = type_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            type_ = s;
            return s;
          }
        }
        /**
         * <code>string type = 4;</code>
         * @return The bytes for type.
         */
        @Override
        public com.google.protobuf.ByteString
            getTypeBytes() {
          Object ref = type_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            type_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int KB_FIELD_NUMBER = 5;
        private long kb_ = 0L;
        /**
         * <code>int64 kb = 5;</code>
         * @return The kb.
         */
        @Override
        public long getKb() {
          return kb_;
        }

        private byte memoizedIsInitialized = -1;
        @Override
        public final boolean isInitialized() {
          byte isInitialized = memoizedIsInitialized;
          if (isInitialized == 1) return true;
          if (isInitialized == 0) return false;

          memoizedIsInitialized = 1;
          return true;
        }

        @Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                            throws java.io.IOException {
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 1, url_);
          }
          if (w_ != 0L) {
            output.writeInt64(2, w_);
          }
          if (h_ != 0L) {
            output.writeInt64(3, h_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(type_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 4, type_);
          }
          if (kb_ != 0L) {
            output.writeInt64(5, kb_);
          }
          getUnknownFields().writeTo(output);
        }

        @Override
        public int getSerializedSize() {
          int size = memoizedSize;
          if (size != -1) return size;

          size = 0;
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(1, url_);
          }
          if (w_ != 0L) {
            size += com.google.protobuf.CodedOutputStream
              .computeInt64Size(2, w_);
          }
          if (h_ != 0L) {
            size += com.google.protobuf.CodedOutputStream
              .computeInt64Size(3, h_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(type_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(4, type_);
          }
          if (kb_ != 0L) {
            size += com.google.protobuf.CodedOutputStream
              .computeInt64Size(5, kb_);
          }
          size += getUnknownFields().getSerializedSize();
          memoizedSize = size;
          return size;
        }

        @Override
        public boolean equals(final Object obj) {
          if (obj == this) {
           return true;
          }
          if (!(obj instanceof ResImage)) {
            return super.equals(obj);
          }
          ResImage other = (ResImage) obj;

          if (!getUrl()
              .equals(other.getUrl())) return false;
          if (getW()
              != other.getW()) return false;
          if (getH()
              != other.getH()) return false;
          if (!getType()
              .equals(other.getType())) return false;
          if (getKb()
              != other.getKb()) return false;
          if (!getUnknownFields().equals(other.getUnknownFields())) return false;
          return true;
        }

        @Override
        public int hashCode() {
          if (memoizedHashCode != 0) {
            return memoizedHashCode;
          }
          int hash = 41;
          hash = (19 * hash) + getDescriptor().hashCode();
          hash = (37 * hash) + URL_FIELD_NUMBER;
          hash = (53 * hash) + getUrl().hashCode();
          hash = (37 * hash) + W_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getW());
          hash = (37 * hash) + H_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getH());
          hash = (37 * hash) + TYPE_FIELD_NUMBER;
          hash = (53 * hash) + getType().hashCode();
          hash = (37 * hash) + KB_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getKb());
          hash = (29 * hash) + getUnknownFields().hashCode();
          memoizedHashCode = hash;
          return hash;
        }

        public static ResImage parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static ResImage parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static ResImage parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static ResImage parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static ResImage parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static ResImage parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static ResImage parseFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static ResImage parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static ResImage parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input);
        }

        public static ResImage parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static ResImage parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static ResImage parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @Override
        public Builder newBuilderForType() { return newBuilder(); }
        public static Builder newBuilder() {
          return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(ResImage prototype) {
          return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        @Override
        public Builder toBuilder() {
          return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
        }

        @Override
        protected Builder newBuilderForType(
            BuilderParent parent) {
          Builder builder = new Builder(parent);
          return builder;
        }
        /**
         * Protobuf type {@code dsp.BidResponse.SeatBid.Bid.ResImage}
         */
        public static final class Builder extends
            com.google.protobuf.GeneratedMessage.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:dsp.BidResponse.SeatBid.Bid.ResImage)
            ResImageOrBuilder {
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_ResImage_descriptor;
          }

          @Override
          protected FieldAccessorTable
              internalGetFieldAccessorTable() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_ResImage_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    ResImage.class, Builder.class);
          }

          // Construct using cn.taken.ad.logic.prossor.hailiang.dto.BidResponse.SeatBid.Bid.ResImage.newBuilder()
          private Builder() {

          }

          private Builder(
              BuilderParent parent) {
            super(parent);

          }
          @Override
          public Builder clear() {
            super.clear();
            bitField0_ = 0;
            url_ = "";
            w_ = 0L;
            h_ = 0L;
            type_ = "";
            kb_ = 0L;
            return this;
          }

          @Override
          public com.google.protobuf.Descriptors.Descriptor
              getDescriptorForType() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_ResImage_descriptor;
          }

          @Override
          public ResImage getDefaultInstanceForType() {
            return ResImage.getDefaultInstance();
          }

          @Override
          public ResImage build() {
            ResImage result = buildPartial();
            if (!result.isInitialized()) {
              throw newUninitializedMessageException(result);
            }
            return result;
          }

          @Override
          public ResImage buildPartial() {
            ResImage result = new ResImage(this);
            if (bitField0_ != 0) { buildPartial0(result); }
            onBuilt();
            return result;
          }

          private void buildPartial0(ResImage result) {
            int from_bitField0_ = bitField0_;
            if (((from_bitField0_ & 0x00000001) != 0)) {
              result.url_ = url_;
            }
            if (((from_bitField0_ & 0x00000002) != 0)) {
              result.w_ = w_;
            }
            if (((from_bitField0_ & 0x00000004) != 0)) {
              result.h_ = h_;
            }
            if (((from_bitField0_ & 0x00000008) != 0)) {
              result.type_ = type_;
            }
            if (((from_bitField0_ & 0x00000010) != 0)) {
              result.kb_ = kb_;
            }
          }

          @Override
          public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof ResImage) {
              return mergeFrom((ResImage)other);
            } else {
              super.mergeFrom(other);
              return this;
            }
          }

          public Builder mergeFrom(ResImage other) {
            if (other == ResImage.getDefaultInstance()) return this;
            if (!other.getUrl().isEmpty()) {
              url_ = other.url_;
              bitField0_ |= 0x00000001;
              onChanged();
            }
            if (other.getW() != 0L) {
              setW(other.getW());
            }
            if (other.getH() != 0L) {
              setH(other.getH());
            }
            if (!other.getType().isEmpty()) {
              type_ = other.type_;
              bitField0_ |= 0x00000008;
              onChanged();
            }
            if (other.getKb() != 0L) {
              setKb(other.getKb());
            }
            this.mergeUnknownFields(other.getUnknownFields());
            onChanged();
            return this;
          }

          @Override
          public final boolean isInitialized() {
            return true;
          }

          @Override
          public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            if (extensionRegistry == null) {
              throw new NullPointerException();
            }
            try {
              boolean done = false;
              while (!done) {
                int tag = input.readTag();
                switch (tag) {
                  case 0:
                    done = true;
                    break;
                  case 10: {
                    url_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000001;
                    break;
                  } // case 10
                  case 16: {
                    w_ = input.readInt64();
                    bitField0_ |= 0x00000002;
                    break;
                  } // case 16
                  case 24: {
                    h_ = input.readInt64();
                    bitField0_ |= 0x00000004;
                    break;
                  } // case 24
                  case 34: {
                    type_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000008;
                    break;
                  } // case 34
                  case 40: {
                    kb_ = input.readInt64();
                    bitField0_ |= 0x00000010;
                    break;
                  } // case 40
                  default: {
                    if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                      done = true; // was an endgroup tag
                    }
                    break;
                  } // default:
                } // switch (tag)
              } // while (!done)
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.unwrapIOException();
            } finally {
              onChanged();
            } // finally
            return this;
          }
          private int bitField0_;

          private Object url_ = "";
          /**
           * <code>string url = 1;</code>
           * @return The url.
           */
          public String getUrl() {
            Object ref = url_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              url_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string url = 1;</code>
           * @return The bytes for url.
           */
          public com.google.protobuf.ByteString
              getUrlBytes() {
            Object ref = url_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              url_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string url = 1;</code>
           * @param value The url to set.
           * @return This builder for chaining.
           */
          public Builder setUrl(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            url_ = value;
            bitField0_ |= 0x00000001;
            onChanged();
            return this;
          }
          /**
           * <code>string url = 1;</code>
           * @return This builder for chaining.
           */
          public Builder clearUrl() {
            url_ = getDefaultInstance().getUrl();
            bitField0_ = (bitField0_ & ~0x00000001);
            onChanged();
            return this;
          }
          /**
           * <code>string url = 1;</code>
           * @param value The bytes for url to set.
           * @return This builder for chaining.
           */
          public Builder setUrlBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            url_ = value;
            bitField0_ |= 0x00000001;
            onChanged();
            return this;
          }

          private long w_ ;
          /**
           * <code>int64 w = 2;</code>
           * @return The w.
           */
          @Override
          public long getW() {
            return w_;
          }
          /**
           * <code>int64 w = 2;</code>
           * @param value The w to set.
           * @return This builder for chaining.
           */
          public Builder setW(long value) {

            w_ = value;
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }
          /**
           * <code>int64 w = 2;</code>
           * @return This builder for chaining.
           */
          public Builder clearW() {
            bitField0_ = (bitField0_ & ~0x00000002);
            w_ = 0L;
            onChanged();
            return this;
          }

          private long h_ ;
          /**
           * <code>int64 h = 3;</code>
           * @return The h.
           */
          @Override
          public long getH() {
            return h_;
          }
          /**
           * <code>int64 h = 3;</code>
           * @param value The h to set.
           * @return This builder for chaining.
           */
          public Builder setH(long value) {

            h_ = value;
            bitField0_ |= 0x00000004;
            onChanged();
            return this;
          }
          /**
           * <code>int64 h = 3;</code>
           * @return This builder for chaining.
           */
          public Builder clearH() {
            bitField0_ = (bitField0_ & ~0x00000004);
            h_ = 0L;
            onChanged();
            return this;
          }

          private Object type_ = "";
          /**
           * <code>string type = 4;</code>
           * @return The type.
           */
          public String getType() {
            Object ref = type_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              type_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string type = 4;</code>
           * @return The bytes for type.
           */
          public com.google.protobuf.ByteString
              getTypeBytes() {
            Object ref = type_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              type_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string type = 4;</code>
           * @param value The type to set.
           * @return This builder for chaining.
           */
          public Builder setType(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            type_ = value;
            bitField0_ |= 0x00000008;
            onChanged();
            return this;
          }
          /**
           * <code>string type = 4;</code>
           * @return This builder for chaining.
           */
          public Builder clearType() {
            type_ = getDefaultInstance().getType();
            bitField0_ = (bitField0_ & ~0x00000008);
            onChanged();
            return this;
          }
          /**
           * <code>string type = 4;</code>
           * @param value The bytes for type to set.
           * @return This builder for chaining.
           */
          public Builder setTypeBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            type_ = value;
            bitField0_ |= 0x00000008;
            onChanged();
            return this;
          }

          private long kb_ ;
          /**
           * <code>int64 kb = 5;</code>
           * @return The kb.
           */
          @Override
          public long getKb() {
            return kb_;
          }
          /**
           * <code>int64 kb = 5;</code>
           * @param value The kb to set.
           * @return This builder for chaining.
           */
          public Builder setKb(long value) {

            kb_ = value;
            bitField0_ |= 0x00000010;
            onChanged();
            return this;
          }
          /**
           * <code>int64 kb = 5;</code>
           * @return This builder for chaining.
           */
          public Builder clearKb() {
            bitField0_ = (bitField0_ & ~0x00000010);
            kb_ = 0L;
            onChanged();
            return this;
          }

          // @@protoc_insertion_point(builder_scope:dsp.BidResponse.SeatBid.Bid.ResImage)
        }

        // @@protoc_insertion_point(class_scope:dsp.BidResponse.SeatBid.Bid.ResImage)
        private static final ResImage DEFAULT_INSTANCE;
        static {
          DEFAULT_INSTANCE = new ResImage();
        }

        public static ResImage getDefaultInstance() {
          return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ResImage>
            PARSER = new com.google.protobuf.AbstractParser<ResImage>() {
          @Override
          public ResImage parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            Builder builder = newBuilder();
            try {
              builder.mergeFrom(input, extensionRegistry);
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.setUnfinishedMessage(builder.buildPartial());
            } catch (com.google.protobuf.UninitializedMessageException e) {
              throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
            } catch (java.io.IOException e) {
              throw new com.google.protobuf.InvalidProtocolBufferException(e)
                  .setUnfinishedMessage(builder.buildPartial());
            }
            return builder.buildPartial();
          }
        };

        public static com.google.protobuf.Parser<ResImage> parser() {
          return PARSER;
        }

        @Override
        public com.google.protobuf.Parser<ResImage> getParserForType() {
          return PARSER;
        }

        @Override
        public ResImage getDefaultInstanceForType() {
          return DEFAULT_INSTANCE;
        }

      }

      public interface ResVideoOrBuilder extends
          // @@protoc_insertion_point(interface_extends:dsp.BidResponse.SeatBid.Bid.ResVideo)
          com.google.protobuf.MessageOrBuilder {

        /**
         * <code>string url = 1;</code>
         * @return The url.
         */
        String getUrl();
        /**
         * <code>string url = 1;</code>
         * @return The bytes for url.
         */
        com.google.protobuf.ByteString
            getUrlBytes();

        /**
         * <code>int64 w = 2;</code>
         * @return The w.
         */
        long getW();

        /**
         * <code>int64 h = 3;</code>
         * @return The h.
         */
        long getH();

        /**
         * <code>string type = 4;</code>
         * @return The type.
         */
        String getType();
        /**
         * <code>string type = 4;</code>
         * @return The bytes for type.
         */
        com.google.protobuf.ByteString
            getTypeBytes();

        /**
         * <code>int64 kb = 5;</code>
         * @return The kb.
         */
        long getKb();

        /**
         * <code>int64 duration = 6;</code>
         * @return The duration.
         */
        long getDuration();
      }
      /**
       * Protobuf type {@code dsp.BidResponse.SeatBid.Bid.ResVideo}
       */
      public static final class ResVideo extends
          com.google.protobuf.GeneratedMessage implements
          // @@protoc_insertion_point(message_implements:dsp.BidResponse.SeatBid.Bid.ResVideo)
          ResVideoOrBuilder {
      private static final long serialVersionUID = 0L;
        static {
          com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
            com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
            /* major= */ 4,
            /* minor= */ 28,
            /* patch= */ 3,
            /* suffix= */ "",
            ResVideo.class.getName());
        }
        // Use ResVideo.newBuilder() to construct.
        private ResVideo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
          super(builder);
        }
        private ResVideo() {
          url_ = "";
          type_ = "";
        }

        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_ResVideo_descriptor;
        }

        @Override
        protected FieldAccessorTable
            internalGetFieldAccessorTable() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_ResVideo_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  ResVideo.class, Builder.class);
        }

        public static final int URL_FIELD_NUMBER = 1;
        @SuppressWarnings("serial")
        private volatile Object url_ = "";
        /**
         * <code>string url = 1;</code>
         * @return The url.
         */
        @Override
        public String getUrl() {
          Object ref = url_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            url_ = s;
            return s;
          }
        }
        /**
         * <code>string url = 1;</code>
         * @return The bytes for url.
         */
        @Override
        public com.google.protobuf.ByteString
            getUrlBytes() {
          Object ref = url_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            url_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int W_FIELD_NUMBER = 2;
        private long w_ = 0L;
        /**
         * <code>int64 w = 2;</code>
         * @return The w.
         */
        @Override
        public long getW() {
          return w_;
        }

        public static final int H_FIELD_NUMBER = 3;
        private long h_ = 0L;
        /**
         * <code>int64 h = 3;</code>
         * @return The h.
         */
        @Override
        public long getH() {
          return h_;
        }

        public static final int TYPE_FIELD_NUMBER = 4;
        @SuppressWarnings("serial")
        private volatile Object type_ = "";
        /**
         * <code>string type = 4;</code>
         * @return The type.
         */
        @Override
        public String getType() {
          Object ref = type_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            type_ = s;
            return s;
          }
        }
        /**
         * <code>string type = 4;</code>
         * @return The bytes for type.
         */
        @Override
        public com.google.protobuf.ByteString
            getTypeBytes() {
          Object ref = type_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            type_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int KB_FIELD_NUMBER = 5;
        private long kb_ = 0L;
        /**
         * <code>int64 kb = 5;</code>
         * @return The kb.
         */
        @Override
        public long getKb() {
          return kb_;
        }

        public static final int DURATION_FIELD_NUMBER = 6;
        private long duration_ = 0L;
        /**
         * <code>int64 duration = 6;</code>
         * @return The duration.
         */
        @Override
        public long getDuration() {
          return duration_;
        }

        private byte memoizedIsInitialized = -1;
        @Override
        public final boolean isInitialized() {
          byte isInitialized = memoizedIsInitialized;
          if (isInitialized == 1) return true;
          if (isInitialized == 0) return false;

          memoizedIsInitialized = 1;
          return true;
        }

        @Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                            throws java.io.IOException {
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 1, url_);
          }
          if (w_ != 0L) {
            output.writeInt64(2, w_);
          }
          if (h_ != 0L) {
            output.writeInt64(3, h_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(type_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 4, type_);
          }
          if (kb_ != 0L) {
            output.writeInt64(5, kb_);
          }
          if (duration_ != 0L) {
            output.writeInt64(6, duration_);
          }
          getUnknownFields().writeTo(output);
        }

        @Override
        public int getSerializedSize() {
          int size = memoizedSize;
          if (size != -1) return size;

          size = 0;
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(1, url_);
          }
          if (w_ != 0L) {
            size += com.google.protobuf.CodedOutputStream
              .computeInt64Size(2, w_);
          }
          if (h_ != 0L) {
            size += com.google.protobuf.CodedOutputStream
              .computeInt64Size(3, h_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(type_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(4, type_);
          }
          if (kb_ != 0L) {
            size += com.google.protobuf.CodedOutputStream
              .computeInt64Size(5, kb_);
          }
          if (duration_ != 0L) {
            size += com.google.protobuf.CodedOutputStream
              .computeInt64Size(6, duration_);
          }
          size += getUnknownFields().getSerializedSize();
          memoizedSize = size;
          return size;
        }

        @Override
        public boolean equals(final Object obj) {
          if (obj == this) {
           return true;
          }
          if (!(obj instanceof ResVideo)) {
            return super.equals(obj);
          }
          ResVideo other = (ResVideo) obj;

          if (!getUrl()
              .equals(other.getUrl())) return false;
          if (getW()
              != other.getW()) return false;
          if (getH()
              != other.getH()) return false;
          if (!getType()
              .equals(other.getType())) return false;
          if (getKb()
              != other.getKb()) return false;
          if (getDuration()
              != other.getDuration()) return false;
          if (!getUnknownFields().equals(other.getUnknownFields())) return false;
          return true;
        }

        @Override
        public int hashCode() {
          if (memoizedHashCode != 0) {
            return memoizedHashCode;
          }
          int hash = 41;
          hash = (19 * hash) + getDescriptor().hashCode();
          hash = (37 * hash) + URL_FIELD_NUMBER;
          hash = (53 * hash) + getUrl().hashCode();
          hash = (37 * hash) + W_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getW());
          hash = (37 * hash) + H_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getH());
          hash = (37 * hash) + TYPE_FIELD_NUMBER;
          hash = (53 * hash) + getType().hashCode();
          hash = (37 * hash) + KB_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getKb());
          hash = (37 * hash) + DURATION_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getDuration());
          hash = (29 * hash) + getUnknownFields().hashCode();
          memoizedHashCode = hash;
          return hash;
        }

        public static ResVideo parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static ResVideo parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static ResVideo parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static ResVideo parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static ResVideo parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static ResVideo parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static ResVideo parseFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static ResVideo parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static ResVideo parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input);
        }

        public static ResVideo parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static ResVideo parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static ResVideo parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @Override
        public Builder newBuilderForType() { return newBuilder(); }
        public static Builder newBuilder() {
          return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(ResVideo prototype) {
          return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        @Override
        public Builder toBuilder() {
          return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
        }

        @Override
        protected Builder newBuilderForType(
            BuilderParent parent) {
          Builder builder = new Builder(parent);
          return builder;
        }
        /**
         * Protobuf type {@code dsp.BidResponse.SeatBid.Bid.ResVideo}
         */
        public static final class Builder extends
            com.google.protobuf.GeneratedMessage.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:dsp.BidResponse.SeatBid.Bid.ResVideo)
            ResVideoOrBuilder {
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_ResVideo_descriptor;
          }

          @Override
          protected FieldAccessorTable
              internalGetFieldAccessorTable() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_ResVideo_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    ResVideo.class, Builder.class);
          }

          // Construct using cn.taken.ad.logic.prossor.hailiang.dto.BidResponse.SeatBid.Bid.ResVideo.newBuilder()
          private Builder() {

          }

          private Builder(
              BuilderParent parent) {
            super(parent);

          }
          @Override
          public Builder clear() {
            super.clear();
            bitField0_ = 0;
            url_ = "";
            w_ = 0L;
            h_ = 0L;
            type_ = "";
            kb_ = 0L;
            duration_ = 0L;
            return this;
          }

          @Override
          public com.google.protobuf.Descriptors.Descriptor
              getDescriptorForType() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_ResVideo_descriptor;
          }

          @Override
          public ResVideo getDefaultInstanceForType() {
            return ResVideo.getDefaultInstance();
          }

          @Override
          public ResVideo build() {
            ResVideo result = buildPartial();
            if (!result.isInitialized()) {
              throw newUninitializedMessageException(result);
            }
            return result;
          }

          @Override
          public ResVideo buildPartial() {
            ResVideo result = new ResVideo(this);
            if (bitField0_ != 0) { buildPartial0(result); }
            onBuilt();
            return result;
          }

          private void buildPartial0(ResVideo result) {
            int from_bitField0_ = bitField0_;
            if (((from_bitField0_ & 0x00000001) != 0)) {
              result.url_ = url_;
            }
            if (((from_bitField0_ & 0x00000002) != 0)) {
              result.w_ = w_;
            }
            if (((from_bitField0_ & 0x00000004) != 0)) {
              result.h_ = h_;
            }
            if (((from_bitField0_ & 0x00000008) != 0)) {
              result.type_ = type_;
            }
            if (((from_bitField0_ & 0x00000010) != 0)) {
              result.kb_ = kb_;
            }
            if (((from_bitField0_ & 0x00000020) != 0)) {
              result.duration_ = duration_;
            }
          }

          @Override
          public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof ResVideo) {
              return mergeFrom((ResVideo)other);
            } else {
              super.mergeFrom(other);
              return this;
            }
          }

          public Builder mergeFrom(ResVideo other) {
            if (other == ResVideo.getDefaultInstance()) return this;
            if (!other.getUrl().isEmpty()) {
              url_ = other.url_;
              bitField0_ |= 0x00000001;
              onChanged();
            }
            if (other.getW() != 0L) {
              setW(other.getW());
            }
            if (other.getH() != 0L) {
              setH(other.getH());
            }
            if (!other.getType().isEmpty()) {
              type_ = other.type_;
              bitField0_ |= 0x00000008;
              onChanged();
            }
            if (other.getKb() != 0L) {
              setKb(other.getKb());
            }
            if (other.getDuration() != 0L) {
              setDuration(other.getDuration());
            }
            this.mergeUnknownFields(other.getUnknownFields());
            onChanged();
            return this;
          }

          @Override
          public final boolean isInitialized() {
            return true;
          }

          @Override
          public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            if (extensionRegistry == null) {
              throw new NullPointerException();
            }
            try {
              boolean done = false;
              while (!done) {
                int tag = input.readTag();
                switch (tag) {
                  case 0:
                    done = true;
                    break;
                  case 10: {
                    url_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000001;
                    break;
                  } // case 10
                  case 16: {
                    w_ = input.readInt64();
                    bitField0_ |= 0x00000002;
                    break;
                  } // case 16
                  case 24: {
                    h_ = input.readInt64();
                    bitField0_ |= 0x00000004;
                    break;
                  } // case 24
                  case 34: {
                    type_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000008;
                    break;
                  } // case 34
                  case 40: {
                    kb_ = input.readInt64();
                    bitField0_ |= 0x00000010;
                    break;
                  } // case 40
                  case 48: {
                    duration_ = input.readInt64();
                    bitField0_ |= 0x00000020;
                    break;
                  } // case 48
                  default: {
                    if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                      done = true; // was an endgroup tag
                    }
                    break;
                  } // default:
                } // switch (tag)
              } // while (!done)
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.unwrapIOException();
            } finally {
              onChanged();
            } // finally
            return this;
          }
          private int bitField0_;

          private Object url_ = "";
          /**
           * <code>string url = 1;</code>
           * @return The url.
           */
          public String getUrl() {
            Object ref = url_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              url_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string url = 1;</code>
           * @return The bytes for url.
           */
          public com.google.protobuf.ByteString
              getUrlBytes() {
            Object ref = url_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              url_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string url = 1;</code>
           * @param value The url to set.
           * @return This builder for chaining.
           */
          public Builder setUrl(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            url_ = value;
            bitField0_ |= 0x00000001;
            onChanged();
            return this;
          }
          /**
           * <code>string url = 1;</code>
           * @return This builder for chaining.
           */
          public Builder clearUrl() {
            url_ = getDefaultInstance().getUrl();
            bitField0_ = (bitField0_ & ~0x00000001);
            onChanged();
            return this;
          }
          /**
           * <code>string url = 1;</code>
           * @param value The bytes for url to set.
           * @return This builder for chaining.
           */
          public Builder setUrlBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            url_ = value;
            bitField0_ |= 0x00000001;
            onChanged();
            return this;
          }

          private long w_ ;
          /**
           * <code>int64 w = 2;</code>
           * @return The w.
           */
          @Override
          public long getW() {
            return w_;
          }
          /**
           * <code>int64 w = 2;</code>
           * @param value The w to set.
           * @return This builder for chaining.
           */
          public Builder setW(long value) {

            w_ = value;
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }
          /**
           * <code>int64 w = 2;</code>
           * @return This builder for chaining.
           */
          public Builder clearW() {
            bitField0_ = (bitField0_ & ~0x00000002);
            w_ = 0L;
            onChanged();
            return this;
          }

          private long h_ ;
          /**
           * <code>int64 h = 3;</code>
           * @return The h.
           */
          @Override
          public long getH() {
            return h_;
          }
          /**
           * <code>int64 h = 3;</code>
           * @param value The h to set.
           * @return This builder for chaining.
           */
          public Builder setH(long value) {

            h_ = value;
            bitField0_ |= 0x00000004;
            onChanged();
            return this;
          }
          /**
           * <code>int64 h = 3;</code>
           * @return This builder for chaining.
           */
          public Builder clearH() {
            bitField0_ = (bitField0_ & ~0x00000004);
            h_ = 0L;
            onChanged();
            return this;
          }

          private Object type_ = "";
          /**
           * <code>string type = 4;</code>
           * @return The type.
           */
          public String getType() {
            Object ref = type_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              type_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string type = 4;</code>
           * @return The bytes for type.
           */
          public com.google.protobuf.ByteString
              getTypeBytes() {
            Object ref = type_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              type_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string type = 4;</code>
           * @param value The type to set.
           * @return This builder for chaining.
           */
          public Builder setType(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            type_ = value;
            bitField0_ |= 0x00000008;
            onChanged();
            return this;
          }
          /**
           * <code>string type = 4;</code>
           * @return This builder for chaining.
           */
          public Builder clearType() {
            type_ = getDefaultInstance().getType();
            bitField0_ = (bitField0_ & ~0x00000008);
            onChanged();
            return this;
          }
          /**
           * <code>string type = 4;</code>
           * @param value The bytes for type to set.
           * @return This builder for chaining.
           */
          public Builder setTypeBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            type_ = value;
            bitField0_ |= 0x00000008;
            onChanged();
            return this;
          }

          private long kb_ ;
          /**
           * <code>int64 kb = 5;</code>
           * @return The kb.
           */
          @Override
          public long getKb() {
            return kb_;
          }
          /**
           * <code>int64 kb = 5;</code>
           * @param value The kb to set.
           * @return This builder for chaining.
           */
          public Builder setKb(long value) {

            kb_ = value;
            bitField0_ |= 0x00000010;
            onChanged();
            return this;
          }
          /**
           * <code>int64 kb = 5;</code>
           * @return This builder for chaining.
           */
          public Builder clearKb() {
            bitField0_ = (bitField0_ & ~0x00000010);
            kb_ = 0L;
            onChanged();
            return this;
          }

          private long duration_ ;
          /**
           * <code>int64 duration = 6;</code>
           * @return The duration.
           */
          @Override
          public long getDuration() {
            return duration_;
          }
          /**
           * <code>int64 duration = 6;</code>
           * @param value The duration to set.
           * @return This builder for chaining.
           */
          public Builder setDuration(long value) {

            duration_ = value;
            bitField0_ |= 0x00000020;
            onChanged();
            return this;
          }
          /**
           * <code>int64 duration = 6;</code>
           * @return This builder for chaining.
           */
          public Builder clearDuration() {
            bitField0_ = (bitField0_ & ~0x00000020);
            duration_ = 0L;
            onChanged();
            return this;
          }

          // @@protoc_insertion_point(builder_scope:dsp.BidResponse.SeatBid.Bid.ResVideo)
        }

        // @@protoc_insertion_point(class_scope:dsp.BidResponse.SeatBid.Bid.ResVideo)
        private static final ResVideo DEFAULT_INSTANCE;
        static {
          DEFAULT_INSTANCE = new ResVideo();
        }

        public static ResVideo getDefaultInstance() {
          return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ResVideo>
            PARSER = new com.google.protobuf.AbstractParser<ResVideo>() {
          @Override
          public ResVideo parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            Builder builder = newBuilder();
            try {
              builder.mergeFrom(input, extensionRegistry);
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.setUnfinishedMessage(builder.buildPartial());
            } catch (com.google.protobuf.UninitializedMessageException e) {
              throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
            } catch (java.io.IOException e) {
              throw new com.google.protobuf.InvalidProtocolBufferException(e)
                  .setUnfinishedMessage(builder.buildPartial());
            }
            return builder.buildPartial();
          }
        };

        public static com.google.protobuf.Parser<ResVideo> parser() {
          return PARSER;
        }

        @Override
        public com.google.protobuf.Parser<ResVideo> getParserForType() {
          return PARSER;
        }

        @Override
        public ResVideo getDefaultInstanceForType() {
          return DEFAULT_INSTANCE;
        }

      }

      public interface ExtOrBuilder extends
          // @@protoc_insertion_point(interface_extends:dsp.BidResponse.SeatBid.Bid.Ext)
          com.google.protobuf.MessageOrBuilder {

        /**
         * <code>string app_permission_url = 1;</code>
         * @return The appPermissionUrl.
         */
        String getAppPermissionUrl();
        /**
         * <code>string app_permission_url = 1;</code>
         * @return The bytes for appPermissionUrl.
         */
        com.google.protobuf.ByteString
            getAppPermissionUrlBytes();

        /**
         * <code>int64 app_version_code = 2;</code>
         * @return The appVersionCode.
         */
        long getAppVersionCode();

        /**
         * <code>string app_version_name = 3;</code>
         * @return The appVersionName.
         */
        String getAppVersionName();
        /**
         * <code>string app_version_name = 3;</code>
         * @return The bytes for appVersionName.
         */
        com.google.protobuf.ByteString
            getAppVersionNameBytes();

        /**
         * <code>repeated string app_snapshots = 4;</code>
         * @return A list containing the appSnapshots.
         */
        java.util.List<String>
            getAppSnapshotsList();
        /**
         * <code>repeated string app_snapshots = 4;</code>
         * @return The count of appSnapshots.
         */
        int getAppSnapshotsCount();
        /**
         * <code>repeated string app_snapshots = 4;</code>
         * @param index The index of the element to return.
         * @return The appSnapshots at the given index.
         */
        String getAppSnapshots(int index);
        /**
         * <code>repeated string app_snapshots = 4;</code>
         * @param index The index of the value to return.
         * @return The bytes of the appSnapshots at the given index.
         */
        com.google.protobuf.ByteString
            getAppSnapshotsBytes(int index);

        /**
         * <code>string app_author = 5;</code>
         * @return The appAuthor.
         */
        String getAppAuthor();
        /**
         * <code>string app_author = 5;</code>
         * @return The bytes for appAuthor.
         */
        com.google.protobuf.ByteString
            getAppAuthorBytes();

        /**
         * <code>string app_privacy_url = 6;</code>
         * @return The appPrivacyUrl.
         */
        String getAppPrivacyUrl();
        /**
         * <code>string app_privacy_url = 6;</code>
         * @return The bytes for appPrivacyUrl.
         */
        com.google.protobuf.ByteString
            getAppPrivacyUrlBytes();

        /**
         * <code>int64 file_size = 7;</code>
         * @return The fileSize.
         */
        long getFileSize();

        /**
         * <code>string file_md5 = 8;</code>
         * @return The fileMd5.
         */
        String getFileMd5();
        /**
         * <code>string file_md5 = 8;</code>
         * @return The bytes for fileMd5.
         */
        com.google.protobuf.ByteString
            getFileMd5Bytes();

        /**
         * <code>string app_info_url = 9;</code>
         * @return The appInfoUrl.
         */
        String getAppInfoUrl();
        /**
         * <code>string app_info_url = 9;</code>
         * @return The bytes for appInfoUrl.
         */
        com.google.protobuf.ByteString
            getAppInfoUrlBytes();
      }
      /**
       * Protobuf type {@code dsp.BidResponse.SeatBid.Bid.Ext}
       */
      public static final class Ext extends
          com.google.protobuf.GeneratedMessage implements
          // @@protoc_insertion_point(message_implements:dsp.BidResponse.SeatBid.Bid.Ext)
          ExtOrBuilder {
      private static final long serialVersionUID = 0L;
        static {
          com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
            com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
            /* major= */ 4,
            /* minor= */ 28,
            /* patch= */ 3,
            /* suffix= */ "",
            Ext.class.getName());
        }
        // Use Ext.newBuilder() to construct.
        private Ext(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
          super(builder);
        }
        private Ext() {
          appPermissionUrl_ = "";
          appVersionName_ = "";
          appSnapshots_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          appAuthor_ = "";
          appPrivacyUrl_ = "";
          fileMd5_ = "";
          appInfoUrl_ = "";
        }

        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_Ext_descriptor;
        }

        @Override
        protected FieldAccessorTable
            internalGetFieldAccessorTable() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_Ext_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  Ext.class, Builder.class);
        }

        public static final int APP_PERMISSION_URL_FIELD_NUMBER = 1;
        @SuppressWarnings("serial")
        private volatile Object appPermissionUrl_ = "";
        /**
         * <code>string app_permission_url = 1;</code>
         * @return The appPermissionUrl.
         */
        @Override
        public String getAppPermissionUrl() {
          Object ref = appPermissionUrl_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            appPermissionUrl_ = s;
            return s;
          }
        }
        /**
         * <code>string app_permission_url = 1;</code>
         * @return The bytes for appPermissionUrl.
         */
        @Override
        public com.google.protobuf.ByteString
            getAppPermissionUrlBytes() {
          Object ref = appPermissionUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            appPermissionUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int APP_VERSION_CODE_FIELD_NUMBER = 2;
        private long appVersionCode_ = 0L;
        /**
         * <code>int64 app_version_code = 2;</code>
         * @return The appVersionCode.
         */
        @Override
        public long getAppVersionCode() {
          return appVersionCode_;
        }

        public static final int APP_VERSION_NAME_FIELD_NUMBER = 3;
        @SuppressWarnings("serial")
        private volatile Object appVersionName_ = "";
        /**
         * <code>string app_version_name = 3;</code>
         * @return The appVersionName.
         */
        @Override
        public String getAppVersionName() {
          Object ref = appVersionName_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            appVersionName_ = s;
            return s;
          }
        }
        /**
         * <code>string app_version_name = 3;</code>
         * @return The bytes for appVersionName.
         */
        @Override
        public com.google.protobuf.ByteString
            getAppVersionNameBytes() {
          Object ref = appVersionName_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            appVersionName_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int APP_SNAPSHOTS_FIELD_NUMBER = 4;
        @SuppressWarnings("serial")
        private com.google.protobuf.LazyStringArrayList appSnapshots_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        /**
         * <code>repeated string app_snapshots = 4;</code>
         * @return A list containing the appSnapshots.
         */
        public com.google.protobuf.ProtocolStringList
            getAppSnapshotsList() {
          return appSnapshots_;
        }
        /**
         * <code>repeated string app_snapshots = 4;</code>
         * @return The count of appSnapshots.
         */
        public int getAppSnapshotsCount() {
          return appSnapshots_.size();
        }
        /**
         * <code>repeated string app_snapshots = 4;</code>
         * @param index The index of the element to return.
         * @return The appSnapshots at the given index.
         */
        public String getAppSnapshots(int index) {
          return appSnapshots_.get(index);
        }
        /**
         * <code>repeated string app_snapshots = 4;</code>
         * @param index The index of the value to return.
         * @return The bytes of the appSnapshots at the given index.
         */
        public com.google.protobuf.ByteString
            getAppSnapshotsBytes(int index) {
          return appSnapshots_.getByteString(index);
        }

        public static final int APP_AUTHOR_FIELD_NUMBER = 5;
        @SuppressWarnings("serial")
        private volatile Object appAuthor_ = "";
        /**
         * <code>string app_author = 5;</code>
         * @return The appAuthor.
         */
        @Override
        public String getAppAuthor() {
          Object ref = appAuthor_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            appAuthor_ = s;
            return s;
          }
        }
        /**
         * <code>string app_author = 5;</code>
         * @return The bytes for appAuthor.
         */
        @Override
        public com.google.protobuf.ByteString
            getAppAuthorBytes() {
          Object ref = appAuthor_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            appAuthor_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int APP_PRIVACY_URL_FIELD_NUMBER = 6;
        @SuppressWarnings("serial")
        private volatile Object appPrivacyUrl_ = "";
        /**
         * <code>string app_privacy_url = 6;</code>
         * @return The appPrivacyUrl.
         */
        @Override
        public String getAppPrivacyUrl() {
          Object ref = appPrivacyUrl_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            appPrivacyUrl_ = s;
            return s;
          }
        }
        /**
         * <code>string app_privacy_url = 6;</code>
         * @return The bytes for appPrivacyUrl.
         */
        @Override
        public com.google.protobuf.ByteString
            getAppPrivacyUrlBytes() {
          Object ref = appPrivacyUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            appPrivacyUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int FILE_SIZE_FIELD_NUMBER = 7;
        private long fileSize_ = 0L;
        /**
         * <code>int64 file_size = 7;</code>
         * @return The fileSize.
         */
        @Override
        public long getFileSize() {
          return fileSize_;
        }

        public static final int FILE_MD5_FIELD_NUMBER = 8;
        @SuppressWarnings("serial")
        private volatile Object fileMd5_ = "";
        /**
         * <code>string file_md5 = 8;</code>
         * @return The fileMd5.
         */
        @Override
        public String getFileMd5() {
          Object ref = fileMd5_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            fileMd5_ = s;
            return s;
          }
        }
        /**
         * <code>string file_md5 = 8;</code>
         * @return The bytes for fileMd5.
         */
        @Override
        public com.google.protobuf.ByteString
            getFileMd5Bytes() {
          Object ref = fileMd5_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            fileMd5_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int APP_INFO_URL_FIELD_NUMBER = 9;
        @SuppressWarnings("serial")
        private volatile Object appInfoUrl_ = "";
        /**
         * <code>string app_info_url = 9;</code>
         * @return The appInfoUrl.
         */
        @Override
        public String getAppInfoUrl() {
          Object ref = appInfoUrl_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            appInfoUrl_ = s;
            return s;
          }
        }
        /**
         * <code>string app_info_url = 9;</code>
         * @return The bytes for appInfoUrl.
         */
        @Override
        public com.google.protobuf.ByteString
            getAppInfoUrlBytes() {
          Object ref = appInfoUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            appInfoUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        private byte memoizedIsInitialized = -1;
        @Override
        public final boolean isInitialized() {
          byte isInitialized = memoizedIsInitialized;
          if (isInitialized == 1) return true;
          if (isInitialized == 0) return false;

          memoizedIsInitialized = 1;
          return true;
        }

        @Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                            throws java.io.IOException {
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appPermissionUrl_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 1, appPermissionUrl_);
          }
          if (appVersionCode_ != 0L) {
            output.writeInt64(2, appVersionCode_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appVersionName_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 3, appVersionName_);
          }
          for (int i = 0; i < appSnapshots_.size(); i++) {
            com.google.protobuf.GeneratedMessage.writeString(output, 4, appSnapshots_.getRaw(i));
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appAuthor_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 5, appAuthor_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appPrivacyUrl_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 6, appPrivacyUrl_);
          }
          if (fileSize_ != 0L) {
            output.writeInt64(7, fileSize_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(fileMd5_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 8, fileMd5_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appInfoUrl_)) {
            com.google.protobuf.GeneratedMessage.writeString(output, 9, appInfoUrl_);
          }
          getUnknownFields().writeTo(output);
        }

        @Override
        public int getSerializedSize() {
          int size = memoizedSize;
          if (size != -1) return size;

          size = 0;
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appPermissionUrl_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(1, appPermissionUrl_);
          }
          if (appVersionCode_ != 0L) {
            size += com.google.protobuf.CodedOutputStream
              .computeInt64Size(2, appVersionCode_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appVersionName_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(3, appVersionName_);
          }
          {
            int dataSize = 0;
            for (int i = 0; i < appSnapshots_.size(); i++) {
              dataSize += computeStringSizeNoTag(appSnapshots_.getRaw(i));
            }
            size += dataSize;
            size += 1 * getAppSnapshotsList().size();
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appAuthor_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(5, appAuthor_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appPrivacyUrl_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(6, appPrivacyUrl_);
          }
          if (fileSize_ != 0L) {
            size += com.google.protobuf.CodedOutputStream
              .computeInt64Size(7, fileSize_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(fileMd5_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(8, fileMd5_);
          }
          if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appInfoUrl_)) {
            size += com.google.protobuf.GeneratedMessage.computeStringSize(9, appInfoUrl_);
          }
          size += getUnknownFields().getSerializedSize();
          memoizedSize = size;
          return size;
        }

        @Override
        public boolean equals(final Object obj) {
          if (obj == this) {
           return true;
          }
          if (!(obj instanceof Ext)) {
            return super.equals(obj);
          }
          Ext other = (Ext) obj;

          if (!getAppPermissionUrl()
              .equals(other.getAppPermissionUrl())) return false;
          if (getAppVersionCode()
              != other.getAppVersionCode()) return false;
          if (!getAppVersionName()
              .equals(other.getAppVersionName())) return false;
          if (!getAppSnapshotsList()
              .equals(other.getAppSnapshotsList())) return false;
          if (!getAppAuthor()
              .equals(other.getAppAuthor())) return false;
          if (!getAppPrivacyUrl()
              .equals(other.getAppPrivacyUrl())) return false;
          if (getFileSize()
              != other.getFileSize()) return false;
          if (!getFileMd5()
              .equals(other.getFileMd5())) return false;
          if (!getAppInfoUrl()
              .equals(other.getAppInfoUrl())) return false;
          if (!getUnknownFields().equals(other.getUnknownFields())) return false;
          return true;
        }

        @Override
        public int hashCode() {
          if (memoizedHashCode != 0) {
            return memoizedHashCode;
          }
          int hash = 41;
          hash = (19 * hash) + getDescriptor().hashCode();
          hash = (37 * hash) + APP_PERMISSION_URL_FIELD_NUMBER;
          hash = (53 * hash) + getAppPermissionUrl().hashCode();
          hash = (37 * hash) + APP_VERSION_CODE_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getAppVersionCode());
          hash = (37 * hash) + APP_VERSION_NAME_FIELD_NUMBER;
          hash = (53 * hash) + getAppVersionName().hashCode();
          if (getAppSnapshotsCount() > 0) {
            hash = (37 * hash) + APP_SNAPSHOTS_FIELD_NUMBER;
            hash = (53 * hash) + getAppSnapshotsList().hashCode();
          }
          hash = (37 * hash) + APP_AUTHOR_FIELD_NUMBER;
          hash = (53 * hash) + getAppAuthor().hashCode();
          hash = (37 * hash) + APP_PRIVACY_URL_FIELD_NUMBER;
          hash = (53 * hash) + getAppPrivacyUrl().hashCode();
          hash = (37 * hash) + FILE_SIZE_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getFileSize());
          hash = (37 * hash) + FILE_MD5_FIELD_NUMBER;
          hash = (53 * hash) + getFileMd5().hashCode();
          hash = (37 * hash) + APP_INFO_URL_FIELD_NUMBER;
          hash = (53 * hash) + getAppInfoUrl().hashCode();
          hash = (29 * hash) + getUnknownFields().hashCode();
          memoizedHashCode = hash;
          return hash;
        }

        public static Ext parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static Ext parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Ext parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static Ext parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Ext parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static Ext parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Ext parseFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static Ext parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static Ext parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input);
        }

        public static Ext parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static Ext parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input);
        }
        public static Ext parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessage
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @Override
        public Builder newBuilderForType() { return newBuilder(); }
        public static Builder newBuilder() {
          return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(Ext prototype) {
          return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        @Override
        public Builder toBuilder() {
          return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
        }

        @Override
        protected Builder newBuilderForType(
            BuilderParent parent) {
          Builder builder = new Builder(parent);
          return builder;
        }
        /**
         * Protobuf type {@code dsp.BidResponse.SeatBid.Bid.Ext}
         */
        public static final class Builder extends
            com.google.protobuf.GeneratedMessage.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:dsp.BidResponse.SeatBid.Bid.Ext)
            ExtOrBuilder {
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_Ext_descriptor;
          }

          @Override
          protected FieldAccessorTable
              internalGetFieldAccessorTable() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_Ext_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    Ext.class, Builder.class);
          }

          // Construct using cn.taken.ad.logic.prossor.hailiang.dto.BidResponse.SeatBid.Bid.Ext.newBuilder()
          private Builder() {

          }

          private Builder(
              BuilderParent parent) {
            super(parent);

          }
          @Override
          public Builder clear() {
            super.clear();
            bitField0_ = 0;
            appPermissionUrl_ = "";
            appVersionCode_ = 0L;
            appVersionName_ = "";
            appSnapshots_ =
                com.google.protobuf.LazyStringArrayList.emptyList();
            appAuthor_ = "";
            appPrivacyUrl_ = "";
            fileSize_ = 0L;
            fileMd5_ = "";
            appInfoUrl_ = "";
            return this;
          }

          @Override
          public com.google.protobuf.Descriptors.Descriptor
              getDescriptorForType() {
            return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_Ext_descriptor;
          }

          @Override
          public Ext getDefaultInstanceForType() {
            return Ext.getDefaultInstance();
          }

          @Override
          public Ext build() {
            Ext result = buildPartial();
            if (!result.isInitialized()) {
              throw newUninitializedMessageException(result);
            }
            return result;
          }

          @Override
          public Ext buildPartial() {
            Ext result = new Ext(this);
            if (bitField0_ != 0) { buildPartial0(result); }
            onBuilt();
            return result;
          }

          private void buildPartial0(Ext result) {
            int from_bitField0_ = bitField0_;
            if (((from_bitField0_ & 0x00000001) != 0)) {
              result.appPermissionUrl_ = appPermissionUrl_;
            }
            if (((from_bitField0_ & 0x00000002) != 0)) {
              result.appVersionCode_ = appVersionCode_;
            }
            if (((from_bitField0_ & 0x00000004) != 0)) {
              result.appVersionName_ = appVersionName_;
            }
            if (((from_bitField0_ & 0x00000008) != 0)) {
              appSnapshots_.makeImmutable();
              result.appSnapshots_ = appSnapshots_;
            }
            if (((from_bitField0_ & 0x00000010) != 0)) {
              result.appAuthor_ = appAuthor_;
            }
            if (((from_bitField0_ & 0x00000020) != 0)) {
              result.appPrivacyUrl_ = appPrivacyUrl_;
            }
            if (((from_bitField0_ & 0x00000040) != 0)) {
              result.fileSize_ = fileSize_;
            }
            if (((from_bitField0_ & 0x00000080) != 0)) {
              result.fileMd5_ = fileMd5_;
            }
            if (((from_bitField0_ & 0x00000100) != 0)) {
              result.appInfoUrl_ = appInfoUrl_;
            }
          }

          @Override
          public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof Ext) {
              return mergeFrom((Ext)other);
            } else {
              super.mergeFrom(other);
              return this;
            }
          }

          public Builder mergeFrom(Ext other) {
            if (other == Ext.getDefaultInstance()) return this;
            if (!other.getAppPermissionUrl().isEmpty()) {
              appPermissionUrl_ = other.appPermissionUrl_;
              bitField0_ |= 0x00000001;
              onChanged();
            }
            if (other.getAppVersionCode() != 0L) {
              setAppVersionCode(other.getAppVersionCode());
            }
            if (!other.getAppVersionName().isEmpty()) {
              appVersionName_ = other.appVersionName_;
              bitField0_ |= 0x00000004;
              onChanged();
            }
            if (!other.appSnapshots_.isEmpty()) {
              if (appSnapshots_.isEmpty()) {
                appSnapshots_ = other.appSnapshots_;
                bitField0_ |= 0x00000008;
              } else {
                ensureAppSnapshotsIsMutable();
                appSnapshots_.addAll(other.appSnapshots_);
              }
              onChanged();
            }
            if (!other.getAppAuthor().isEmpty()) {
              appAuthor_ = other.appAuthor_;
              bitField0_ |= 0x00000010;
              onChanged();
            }
            if (!other.getAppPrivacyUrl().isEmpty()) {
              appPrivacyUrl_ = other.appPrivacyUrl_;
              bitField0_ |= 0x00000020;
              onChanged();
            }
            if (other.getFileSize() != 0L) {
              setFileSize(other.getFileSize());
            }
            if (!other.getFileMd5().isEmpty()) {
              fileMd5_ = other.fileMd5_;
              bitField0_ |= 0x00000080;
              onChanged();
            }
            if (!other.getAppInfoUrl().isEmpty()) {
              appInfoUrl_ = other.appInfoUrl_;
              bitField0_ |= 0x00000100;
              onChanged();
            }
            this.mergeUnknownFields(other.getUnknownFields());
            onChanged();
            return this;
          }

          @Override
          public final boolean isInitialized() {
            return true;
          }

          @Override
          public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            if (extensionRegistry == null) {
              throw new NullPointerException();
            }
            try {
              boolean done = false;
              while (!done) {
                int tag = input.readTag();
                switch (tag) {
                  case 0:
                    done = true;
                    break;
                  case 10: {
                    appPermissionUrl_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000001;
                    break;
                  } // case 10
                  case 16: {
                    appVersionCode_ = input.readInt64();
                    bitField0_ |= 0x00000002;
                    break;
                  } // case 16
                  case 26: {
                    appVersionName_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000004;
                    break;
                  } // case 26
                  case 34: {
                    String s = input.readStringRequireUtf8();
                    ensureAppSnapshotsIsMutable();
                    appSnapshots_.add(s);
                    break;
                  } // case 34
                  case 42: {
                    appAuthor_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000010;
                    break;
                  } // case 42
                  case 50: {
                    appPrivacyUrl_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000020;
                    break;
                  } // case 50
                  case 56: {
                    fileSize_ = input.readInt64();
                    bitField0_ |= 0x00000040;
                    break;
                  } // case 56
                  case 66: {
                    fileMd5_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000080;
                    break;
                  } // case 66
                  case 74: {
                    appInfoUrl_ = input.readStringRequireUtf8();
                    bitField0_ |= 0x00000100;
                    break;
                  } // case 74
                  default: {
                    if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                      done = true; // was an endgroup tag
                    }
                    break;
                  } // default:
                } // switch (tag)
              } // while (!done)
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.unwrapIOException();
            } finally {
              onChanged();
            } // finally
            return this;
          }
          private int bitField0_;

          private Object appPermissionUrl_ = "";
          /**
           * <code>string app_permission_url = 1;</code>
           * @return The appPermissionUrl.
           */
          public String getAppPermissionUrl() {
            Object ref = appPermissionUrl_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              appPermissionUrl_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string app_permission_url = 1;</code>
           * @return The bytes for appPermissionUrl.
           */
          public com.google.protobuf.ByteString
              getAppPermissionUrlBytes() {
            Object ref = appPermissionUrl_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              appPermissionUrl_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string app_permission_url = 1;</code>
           * @param value The appPermissionUrl to set.
           * @return This builder for chaining.
           */
          public Builder setAppPermissionUrl(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            appPermissionUrl_ = value;
            bitField0_ |= 0x00000001;
            onChanged();
            return this;
          }
          /**
           * <code>string app_permission_url = 1;</code>
           * @return This builder for chaining.
           */
          public Builder clearAppPermissionUrl() {
            appPermissionUrl_ = getDefaultInstance().getAppPermissionUrl();
            bitField0_ = (bitField0_ & ~0x00000001);
            onChanged();
            return this;
          }
          /**
           * <code>string app_permission_url = 1;</code>
           * @param value The bytes for appPermissionUrl to set.
           * @return This builder for chaining.
           */
          public Builder setAppPermissionUrlBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            appPermissionUrl_ = value;
            bitField0_ |= 0x00000001;
            onChanged();
            return this;
          }

          private long appVersionCode_ ;
          /**
           * <code>int64 app_version_code = 2;</code>
           * @return The appVersionCode.
           */
          @Override
          public long getAppVersionCode() {
            return appVersionCode_;
          }
          /**
           * <code>int64 app_version_code = 2;</code>
           * @param value The appVersionCode to set.
           * @return This builder for chaining.
           */
          public Builder setAppVersionCode(long value) {

            appVersionCode_ = value;
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }
          /**
           * <code>int64 app_version_code = 2;</code>
           * @return This builder for chaining.
           */
          public Builder clearAppVersionCode() {
            bitField0_ = (bitField0_ & ~0x00000002);
            appVersionCode_ = 0L;
            onChanged();
            return this;
          }

          private Object appVersionName_ = "";
          /**
           * <code>string app_version_name = 3;</code>
           * @return The appVersionName.
           */
          public String getAppVersionName() {
            Object ref = appVersionName_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              appVersionName_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string app_version_name = 3;</code>
           * @return The bytes for appVersionName.
           */
          public com.google.protobuf.ByteString
              getAppVersionNameBytes() {
            Object ref = appVersionName_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              appVersionName_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string app_version_name = 3;</code>
           * @param value The appVersionName to set.
           * @return This builder for chaining.
           */
          public Builder setAppVersionName(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            appVersionName_ = value;
            bitField0_ |= 0x00000004;
            onChanged();
            return this;
          }
          /**
           * <code>string app_version_name = 3;</code>
           * @return This builder for chaining.
           */
          public Builder clearAppVersionName() {
            appVersionName_ = getDefaultInstance().getAppVersionName();
            bitField0_ = (bitField0_ & ~0x00000004);
            onChanged();
            return this;
          }
          /**
           * <code>string app_version_name = 3;</code>
           * @param value The bytes for appVersionName to set.
           * @return This builder for chaining.
           */
          public Builder setAppVersionNameBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            appVersionName_ = value;
            bitField0_ |= 0x00000004;
            onChanged();
            return this;
          }

          private com.google.protobuf.LazyStringArrayList appSnapshots_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          private void ensureAppSnapshotsIsMutable() {
            if (!appSnapshots_.isModifiable()) {
              appSnapshots_ = new com.google.protobuf.LazyStringArrayList(appSnapshots_);
            }
            bitField0_ |= 0x00000008;
          }
          /**
           * <code>repeated string app_snapshots = 4;</code>
           * @return A list containing the appSnapshots.
           */
          public com.google.protobuf.ProtocolStringList
              getAppSnapshotsList() {
            appSnapshots_.makeImmutable();
            return appSnapshots_;
          }
          /**
           * <code>repeated string app_snapshots = 4;</code>
           * @return The count of appSnapshots.
           */
          public int getAppSnapshotsCount() {
            return appSnapshots_.size();
          }
          /**
           * <code>repeated string app_snapshots = 4;</code>
           * @param index The index of the element to return.
           * @return The appSnapshots at the given index.
           */
          public String getAppSnapshots(int index) {
            return appSnapshots_.get(index);
          }
          /**
           * <code>repeated string app_snapshots = 4;</code>
           * @param index The index of the value to return.
           * @return The bytes of the appSnapshots at the given index.
           */
          public com.google.protobuf.ByteString
              getAppSnapshotsBytes(int index) {
            return appSnapshots_.getByteString(index);
          }
          /**
           * <code>repeated string app_snapshots = 4;</code>
           * @param index The index to set the value at.
           * @param value The appSnapshots to set.
           * @return This builder for chaining.
           */
          public Builder setAppSnapshots(
              int index, String value) {
            if (value == null) { throw new NullPointerException(); }
            ensureAppSnapshotsIsMutable();
            appSnapshots_.set(index, value);
            bitField0_ |= 0x00000008;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string app_snapshots = 4;</code>
           * @param value The appSnapshots to add.
           * @return This builder for chaining.
           */
          public Builder addAppSnapshots(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            ensureAppSnapshotsIsMutable();
            appSnapshots_.add(value);
            bitField0_ |= 0x00000008;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string app_snapshots = 4;</code>
           * @param values The appSnapshots to add.
           * @return This builder for chaining.
           */
          public Builder addAllAppSnapshots(
              Iterable<String> values) {
            ensureAppSnapshotsIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, appSnapshots_);
            bitField0_ |= 0x00000008;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string app_snapshots = 4;</code>
           * @return This builder for chaining.
           */
          public Builder clearAppSnapshots() {
            appSnapshots_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
            bitField0_ = (bitField0_ & ~0x00000008);;
            onChanged();
            return this;
          }
          /**
           * <code>repeated string app_snapshots = 4;</code>
           * @param value The bytes of the appSnapshots to add.
           * @return This builder for chaining.
           */
          public Builder addAppSnapshotsBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            ensureAppSnapshotsIsMutable();
            appSnapshots_.add(value);
            bitField0_ |= 0x00000008;
            onChanged();
            return this;
          }

          private Object appAuthor_ = "";
          /**
           * <code>string app_author = 5;</code>
           * @return The appAuthor.
           */
          public String getAppAuthor() {
            Object ref = appAuthor_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              appAuthor_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string app_author = 5;</code>
           * @return The bytes for appAuthor.
           */
          public com.google.protobuf.ByteString
              getAppAuthorBytes() {
            Object ref = appAuthor_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              appAuthor_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string app_author = 5;</code>
           * @param value The appAuthor to set.
           * @return This builder for chaining.
           */
          public Builder setAppAuthor(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            appAuthor_ = value;
            bitField0_ |= 0x00000010;
            onChanged();
            return this;
          }
          /**
           * <code>string app_author = 5;</code>
           * @return This builder for chaining.
           */
          public Builder clearAppAuthor() {
            appAuthor_ = getDefaultInstance().getAppAuthor();
            bitField0_ = (bitField0_ & ~0x00000010);
            onChanged();
            return this;
          }
          /**
           * <code>string app_author = 5;</code>
           * @param value The bytes for appAuthor to set.
           * @return This builder for chaining.
           */
          public Builder setAppAuthorBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            appAuthor_ = value;
            bitField0_ |= 0x00000010;
            onChanged();
            return this;
          }

          private Object appPrivacyUrl_ = "";
          /**
           * <code>string app_privacy_url = 6;</code>
           * @return The appPrivacyUrl.
           */
          public String getAppPrivacyUrl() {
            Object ref = appPrivacyUrl_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              appPrivacyUrl_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string app_privacy_url = 6;</code>
           * @return The bytes for appPrivacyUrl.
           */
          public com.google.protobuf.ByteString
              getAppPrivacyUrlBytes() {
            Object ref = appPrivacyUrl_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              appPrivacyUrl_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string app_privacy_url = 6;</code>
           * @param value The appPrivacyUrl to set.
           * @return This builder for chaining.
           */
          public Builder setAppPrivacyUrl(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            appPrivacyUrl_ = value;
            bitField0_ |= 0x00000020;
            onChanged();
            return this;
          }
          /**
           * <code>string app_privacy_url = 6;</code>
           * @return This builder for chaining.
           */
          public Builder clearAppPrivacyUrl() {
            appPrivacyUrl_ = getDefaultInstance().getAppPrivacyUrl();
            bitField0_ = (bitField0_ & ~0x00000020);
            onChanged();
            return this;
          }
          /**
           * <code>string app_privacy_url = 6;</code>
           * @param value The bytes for appPrivacyUrl to set.
           * @return This builder for chaining.
           */
          public Builder setAppPrivacyUrlBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            appPrivacyUrl_ = value;
            bitField0_ |= 0x00000020;
            onChanged();
            return this;
          }

          private long fileSize_ ;
          /**
           * <code>int64 file_size = 7;</code>
           * @return The fileSize.
           */
          @Override
          public long getFileSize() {
            return fileSize_;
          }
          /**
           * <code>int64 file_size = 7;</code>
           * @param value The fileSize to set.
           * @return This builder for chaining.
           */
          public Builder setFileSize(long value) {

            fileSize_ = value;
            bitField0_ |= 0x00000040;
            onChanged();
            return this;
          }
          /**
           * <code>int64 file_size = 7;</code>
           * @return This builder for chaining.
           */
          public Builder clearFileSize() {
            bitField0_ = (bitField0_ & ~0x00000040);
            fileSize_ = 0L;
            onChanged();
            return this;
          }

          private Object fileMd5_ = "";
          /**
           * <code>string file_md5 = 8;</code>
           * @return The fileMd5.
           */
          public String getFileMd5() {
            Object ref = fileMd5_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              fileMd5_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string file_md5 = 8;</code>
           * @return The bytes for fileMd5.
           */
          public com.google.protobuf.ByteString
              getFileMd5Bytes() {
            Object ref = fileMd5_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              fileMd5_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string file_md5 = 8;</code>
           * @param value The fileMd5 to set.
           * @return This builder for chaining.
           */
          public Builder setFileMd5(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            fileMd5_ = value;
            bitField0_ |= 0x00000080;
            onChanged();
            return this;
          }
          /**
           * <code>string file_md5 = 8;</code>
           * @return This builder for chaining.
           */
          public Builder clearFileMd5() {
            fileMd5_ = getDefaultInstance().getFileMd5();
            bitField0_ = (bitField0_ & ~0x00000080);
            onChanged();
            return this;
          }
          /**
           * <code>string file_md5 = 8;</code>
           * @param value The bytes for fileMd5 to set.
           * @return This builder for chaining.
           */
          public Builder setFileMd5Bytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            fileMd5_ = value;
            bitField0_ |= 0x00000080;
            onChanged();
            return this;
          }

          private Object appInfoUrl_ = "";
          /**
           * <code>string app_info_url = 9;</code>
           * @return The appInfoUrl.
           */
          public String getAppInfoUrl() {
            Object ref = appInfoUrl_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              appInfoUrl_ = s;
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <code>string app_info_url = 9;</code>
           * @return The bytes for appInfoUrl.
           */
          public com.google.protobuf.ByteString
              getAppInfoUrlBytes() {
            Object ref = appInfoUrl_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              appInfoUrl_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string app_info_url = 9;</code>
           * @param value The appInfoUrl to set.
           * @return This builder for chaining.
           */
          public Builder setAppInfoUrl(
              String value) {
            if (value == null) { throw new NullPointerException(); }
            appInfoUrl_ = value;
            bitField0_ |= 0x00000100;
            onChanged();
            return this;
          }
          /**
           * <code>string app_info_url = 9;</code>
           * @return This builder for chaining.
           */
          public Builder clearAppInfoUrl() {
            appInfoUrl_ = getDefaultInstance().getAppInfoUrl();
            bitField0_ = (bitField0_ & ~0x00000100);
            onChanged();
            return this;
          }
          /**
           * <code>string app_info_url = 9;</code>
           * @param value The bytes for appInfoUrl to set.
           * @return This builder for chaining.
           */
          public Builder setAppInfoUrlBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) { throw new NullPointerException(); }
            checkByteStringIsUtf8(value);
            appInfoUrl_ = value;
            bitField0_ |= 0x00000100;
            onChanged();
            return this;
          }

          // @@protoc_insertion_point(builder_scope:dsp.BidResponse.SeatBid.Bid.Ext)
        }

        // @@protoc_insertion_point(class_scope:dsp.BidResponse.SeatBid.Bid.Ext)
        private static final Ext DEFAULT_INSTANCE;
        static {
          DEFAULT_INSTANCE = new Ext();
        }

        public static Ext getDefaultInstance() {
          return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<Ext>
            PARSER = new com.google.protobuf.AbstractParser<Ext>() {
          @Override
          public Ext parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            Builder builder = newBuilder();
            try {
              builder.mergeFrom(input, extensionRegistry);
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.setUnfinishedMessage(builder.buildPartial());
            } catch (com.google.protobuf.UninitializedMessageException e) {
              throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
            } catch (java.io.IOException e) {
              throw new com.google.protobuf.InvalidProtocolBufferException(e)
                  .setUnfinishedMessage(builder.buildPartial());
            }
            return builder.buildPartial();
          }
        };

        public static com.google.protobuf.Parser<Ext> parser() {
          return PARSER;
        }

        @Override
        public com.google.protobuf.Parser<Ext> getParserForType() {
          return PARSER;
        }

        @Override
        public Ext getDefaultInstanceForType() {
          return DEFAULT_INSTANCE;
        }

      }

      private int bitField0_;
      public static final int ID_FIELD_NUMBER = 1;
      @SuppressWarnings("serial")
      private volatile Object id_ = "";
      /**
       * <code>string id = 1;</code>
       * @return The id.
       */
      @Override
      public String getId() {
        Object ref = id_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          id_ = s;
          return s;
        }
      }
      /**
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      @Override
      public com.google.protobuf.ByteString
          getIdBytes() {
        Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int IMPID_FIELD_NUMBER = 2;
      @SuppressWarnings("serial")
      private volatile Object impid_ = "";
      /**
       * <code>string impid = 2;</code>
       * @return The impid.
       */
      @Override
      public String getImpid() {
        Object ref = impid_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          impid_ = s;
          return s;
        }
      }
      /**
       * <code>string impid = 2;</code>
       * @return The bytes for impid.
       */
      @Override
      public com.google.protobuf.ByteString
          getImpidBytes() {
        Object ref = impid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          impid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int BIDTYPE_FIELD_NUMBER = 3;
      private int bidtype_ = 0;
      /**
       * <code>int32 bidtype = 3;</code>
       * @return The bidtype.
       */
      @Override
      public int getBidtype() {
        return bidtype_;
      }

      public static final int CRID_FIELD_NUMBER = 4;
      @SuppressWarnings("serial")
      private volatile Object crid_ = "";
      /**
       * <code>string crid = 4;</code>
       * @return The crid.
       */
      @Override
      public String getCrid() {
        Object ref = crid_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          crid_ = s;
          return s;
        }
      }
      /**
       * <code>string crid = 4;</code>
       * @return The bytes for crid.
       */
      @Override
      public com.google.protobuf.ByteString
          getCridBytes() {
        Object ref = crid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          crid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DP_TRACKS_FIELD_NUMBER = 6;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList dpTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string dp_tracks = 6;</code>
       * @return A list containing the dpTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getDpTracksList() {
        return dpTracks_;
      }
      /**
       * <code>repeated string dp_tracks = 6;</code>
       * @return The count of dpTracks.
       */
      public int getDpTracksCount() {
        return dpTracks_.size();
      }
      /**
       * <code>repeated string dp_tracks = 6;</code>
       * @param index The index of the element to return.
       * @return The dpTracks at the given index.
       */
      public String getDpTracks(int index) {
        return dpTracks_.get(index);
      }
      /**
       * <code>repeated string dp_tracks = 6;</code>
       * @param index The index of the value to return.
       * @return The bytes of the dpTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getDpTracksBytes(int index) {
        return dpTracks_.getByteString(index);
      }

      public static final int IMP_TRACKS_FIELD_NUMBER = 7;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList impTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string imp_tracks = 7;</code>
       * @return A list containing the impTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getImpTracksList() {
        return impTracks_;
      }
      /**
       * <code>repeated string imp_tracks = 7;</code>
       * @return The count of impTracks.
       */
      public int getImpTracksCount() {
        return impTracks_.size();
      }
      /**
       * <code>repeated string imp_tracks = 7;</code>
       * @param index The index of the element to return.
       * @return The impTracks at the given index.
       */
      public String getImpTracks(int index) {
        return impTracks_.get(index);
      }
      /**
       * <code>repeated string imp_tracks = 7;</code>
       * @param index The index of the value to return.
       * @return The bytes of the impTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getImpTracksBytes(int index) {
        return impTracks_.getByteString(index);
      }

      public static final int CLICK_TRACKS_FIELD_NUMBER = 8;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList clickTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string click_tracks = 8;</code>
       * @return A list containing the clickTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getClickTracksList() {
        return clickTracks_;
      }
      /**
       * <code>repeated string click_tracks = 8;</code>
       * @return The count of clickTracks.
       */
      public int getClickTracksCount() {
        return clickTracks_.size();
      }
      /**
       * <code>repeated string click_tracks = 8;</code>
       * @param index The index of the element to return.
       * @return The clickTracks at the given index.
       */
      public String getClickTracks(int index) {
        return clickTracks_.get(index);
      }
      /**
       * <code>repeated string click_tracks = 8;</code>
       * @param index The index of the value to return.
       * @return The bytes of the clickTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getClickTracksBytes(int index) {
        return clickTracks_.getByteString(index);
      }

      public static final int DOWN_START_TRACKS_FIELD_NUMBER = 9;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList downStartTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string down_start_tracks = 9;</code>
       * @return A list containing the downStartTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getDownStartTracksList() {
        return downStartTracks_;
      }
      /**
       * <code>repeated string down_start_tracks = 9;</code>
       * @return The count of downStartTracks.
       */
      public int getDownStartTracksCount() {
        return downStartTracks_.size();
      }
      /**
       * <code>repeated string down_start_tracks = 9;</code>
       * @param index The index of the element to return.
       * @return The downStartTracks at the given index.
       */
      public String getDownStartTracks(int index) {
        return downStartTracks_.get(index);
      }
      /**
       * <code>repeated string down_start_tracks = 9;</code>
       * @param index The index of the value to return.
       * @return The bytes of the downStartTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getDownStartTracksBytes(int index) {
        return downStartTracks_.getByteString(index);
      }

      public static final int DOWN_COMPLETE_TRACKS_FIELD_NUMBER = 10;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList downCompleteTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string down_complete_tracks = 10;</code>
       * @return A list containing the downCompleteTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getDownCompleteTracksList() {
        return downCompleteTracks_;
      }
      /**
       * <code>repeated string down_complete_tracks = 10;</code>
       * @return The count of downCompleteTracks.
       */
      public int getDownCompleteTracksCount() {
        return downCompleteTracks_.size();
      }
      /**
       * <code>repeated string down_complete_tracks = 10;</code>
       * @param index The index of the element to return.
       * @return The downCompleteTracks at the given index.
       */
      public String getDownCompleteTracks(int index) {
        return downCompleteTracks_.get(index);
      }
      /**
       * <code>repeated string down_complete_tracks = 10;</code>
       * @param index The index of the value to return.
       * @return The bytes of the downCompleteTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getDownCompleteTracksBytes(int index) {
        return downCompleteTracks_.getByteString(index);
      }

      public static final int INSTALL_START_TRACKS_FIELD_NUMBER = 12;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList installStartTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string install_start_tracks = 12;</code>
       * @return A list containing the installStartTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getInstallStartTracksList() {
        return installStartTracks_;
      }
      /**
       * <code>repeated string install_start_tracks = 12;</code>
       * @return The count of installStartTracks.
       */
      public int getInstallStartTracksCount() {
        return installStartTracks_.size();
      }
      /**
       * <code>repeated string install_start_tracks = 12;</code>
       * @param index The index of the element to return.
       * @return The installStartTracks at the given index.
       */
      public String getInstallStartTracks(int index) {
        return installStartTracks_.get(index);
      }
      /**
       * <code>repeated string install_start_tracks = 12;</code>
       * @param index The index of the value to return.
       * @return The bytes of the installStartTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getInstallStartTracksBytes(int index) {
        return installStartTracks_.getByteString(index);
      }

      public static final int INSTALL_COMPLETE_TRACKS_FIELD_NUMBER = 13;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList installCompleteTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string install_complete_tracks = 13;</code>
       * @return A list containing the installCompleteTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getInstallCompleteTracksList() {
        return installCompleteTracks_;
      }
      /**
       * <code>repeated string install_complete_tracks = 13;</code>
       * @return The count of installCompleteTracks.
       */
      public int getInstallCompleteTracksCount() {
        return installCompleteTracks_.size();
      }
      /**
       * <code>repeated string install_complete_tracks = 13;</code>
       * @param index The index of the element to return.
       * @return The installCompleteTracks at the given index.
       */
      public String getInstallCompleteTracks(int index) {
        return installCompleteTracks_.get(index);
      }
      /**
       * <code>repeated string install_complete_tracks = 13;</code>
       * @param index The index of the value to return.
       * @return The bytes of the installCompleteTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getInstallCompleteTracksBytes(int index) {
        return installCompleteTracks_.getByteString(index);
      }

      public static final int APPNAME_FIELD_NUMBER = 14;
      @SuppressWarnings("serial")
      private volatile Object appname_ = "";
      /**
       * <code>string appname = 14;</code>
       * @return The appname.
       */
      @Override
      public String getAppname() {
        Object ref = appname_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          appname_ = s;
          return s;
        }
      }
      /**
       * <code>string appname = 14;</code>
       * @return The bytes for appname.
       */
      @Override
      public com.google.protobuf.ByteString
          getAppnameBytes() {
        Object ref = appname_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          appname_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int BUNDLE_FIELD_NUMBER = 15;
      @SuppressWarnings("serial")
      private volatile Object bundle_ = "";
      /**
       * <code>string bundle = 15;</code>
       * @return The bundle.
       */
      @Override
      public String getBundle() {
        Object ref = bundle_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          bundle_ = s;
          return s;
        }
      }
      /**
       * <code>string bundle = 15;</code>
       * @return The bytes for bundle.
       */
      @Override
      public com.google.protobuf.ByteString
          getBundleBytes() {
        Object ref = bundle_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          bundle_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int IMAGE_FIELD_NUMBER = 16;
      private ResImage image_;
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
       * @return Whether the image field is set.
       */
      @Override
      public boolean hasImage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
       * @return The image.
       */
      @Override
      public ResImage getImage() {
        return image_ == null ? ResImage.getDefaultInstance() : image_;
      }
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
       */
      @Override
      public ResImageOrBuilder getImageOrBuilder() {
        return image_ == null ? ResImage.getDefaultInstance() : image_;
      }

      public static final int IMAGES_FIELD_NUMBER = 17;
      @SuppressWarnings("serial")
      private java.util.List<ResImage> images_;
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
       */
      @Override
      public java.util.List<ResImage> getImagesList() {
        return images_;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
       */
      @Override
      public java.util.List<? extends ResImageOrBuilder>
          getImagesOrBuilderList() {
        return images_;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
       */
      @Override
      public int getImagesCount() {
        return images_.size();
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
       */
      @Override
      public ResImage getImages(int index) {
        return images_.get(index);
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
       */
      @Override
      public ResImageOrBuilder getImagesOrBuilder(
          int index) {
        return images_.get(index);
      }

      public static final int LOGO_FIELD_NUMBER = 18;
      private ResImage logo_;
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
       * @return Whether the logo field is set.
       */
      @Override
      public boolean hasLogo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
       * @return The logo.
       */
      @Override
      public ResImage getLogo() {
        return logo_ == null ? ResImage.getDefaultInstance() : logo_;
      }
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
       */
      @Override
      public ResImageOrBuilder getLogoOrBuilder() {
        return logo_ == null ? ResImage.getDefaultInstance() : logo_;
      }

      public static final int APPICON_FIELD_NUMBER = 19;
      private ResImage appicon_;
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
       * @return Whether the appicon field is set.
       */
      @Override
      public boolean hasAppicon() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
       * @return The appicon.
       */
      @Override
      public ResImage getAppicon() {
        return appicon_ == null ? ResImage.getDefaultInstance() : appicon_;
      }
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
       */
      @Override
      public ResImageOrBuilder getAppiconOrBuilder() {
        return appicon_ == null ? ResImage.getDefaultInstance() : appicon_;
      }

      public static final int TITLE_FIELD_NUMBER = 20;
      @SuppressWarnings("serial")
      private volatile Object title_ = "";
      /**
       * <code>string title = 20;</code>
       * @return The title.
       */
      @Override
      public String getTitle() {
        Object ref = title_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          title_ = s;
          return s;
        }
      }
      /**
       * <code>string title = 20;</code>
       * @return The bytes for title.
       */
      @Override
      public com.google.protobuf.ByteString
          getTitleBytes() {
        Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DES_FIELD_NUMBER = 21;
      @SuppressWarnings("serial")
      private volatile Object des_ = "";
      /**
       * <code>string des = 21;</code>
       * @return The des.
       */
      @Override
      public String getDes() {
        Object ref = des_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          des_ = s;
          return s;
        }
      }
      /**
       * <code>string des = 21;</code>
       * @return The bytes for des.
       */
      @Override
      public com.google.protobuf.ByteString
          getDesBytes() {
        Object ref = des_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          des_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int IS_DOWNLOAD_FIELD_NUMBER = 23;
      private boolean isDownload_ = false;
      /**
       * <code>bool is_download = 23;</code>
       * @return The isDownload.
       */
      @Override
      public boolean getIsDownload() {
        return isDownload_;
      }

      public static final int IS_DEEP_FIELD_NUMBER = 24;
      private boolean isDeep_ = false;
      /**
       * <code>bool is_deep = 24;</code>
       * @return The isDeep.
       */
      @Override
      public boolean getIsDeep() {
        return isDeep_;
      }

      public static final int FILE_URL_FIELD_NUMBER = 25;
      @SuppressWarnings("serial")
      private volatile Object fileUrl_ = "";
      /**
       * <code>string file_url = 25;</code>
       * @return The fileUrl.
       */
      @Override
      public String getFileUrl() {
        Object ref = fileUrl_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          fileUrl_ = s;
          return s;
        }
      }
      /**
       * <code>string file_url = 25;</code>
       * @return The bytes for fileUrl.
       */
      @Override
      public com.google.protobuf.ByteString
          getFileUrlBytes() {
        Object ref = fileUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          fileUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int LANDING_URL_FIELD_NUMBER = 26;
      @SuppressWarnings("serial")
      private volatile Object landingUrl_ = "";
      /**
       * <code>string landing_url = 26;</code>
       * @return The landingUrl.
       */
      @Override
      public String getLandingUrl() {
        Object ref = landingUrl_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          landingUrl_ = s;
          return s;
        }
      }
      /**
       * <code>string landing_url = 26;</code>
       * @return The bytes for landingUrl.
       */
      @Override
      public com.google.protobuf.ByteString
          getLandingUrlBytes() {
        Object ref = landingUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          landingUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DEEPLINK_FIELD_NUMBER = 27;
      @SuppressWarnings("serial")
      private volatile Object deeplink_ = "";
      /**
       * <code>string deeplink = 27;</code>
       * @return The deeplink.
       */
      @Override
      public String getDeeplink() {
        Object ref = deeplink_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          deeplink_ = s;
          return s;
        }
      }
      /**
       * <code>string deeplink = 27;</code>
       * @return The bytes for deeplink.
       */
      @Override
      public com.google.protobuf.ByteString
          getDeeplinkBytes() {
        Object ref = deeplink_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          deeplink_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int EXPIRATIONTIME_FIELD_NUMBER = 28;
      private long expirationTime_ = 0L;
      /**
       * <code>int64 expirationTime = 28;</code>
       * @return The expirationTime.
       */
      @Override
      public long getExpirationTime() {
        return expirationTime_;
      }

      public static final int VIDEO_FIELD_NUMBER = 29;
      private ResVideo video_;
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
       * @return Whether the video field is set.
       */
      @Override
      public boolean hasVideo() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
       * @return The video.
       */
      @Override
      public ResVideo getVideo() {
        return video_ == null ? ResVideo.getDefaultInstance() : video_;
      }
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
       */
      @Override
      public ResVideoOrBuilder getVideoOrBuilder() {
        return video_ == null ? ResVideo.getDefaultInstance() : video_;
      }

      public static final int VIDEO_START_TRACKS_FIELD_NUMBER = 30;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList videoStartTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string video_start_tracks = 30;</code>
       * @return A list containing the videoStartTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getVideoStartTracksList() {
        return videoStartTracks_;
      }
      /**
       * <code>repeated string video_start_tracks = 30;</code>
       * @return The count of videoStartTracks.
       */
      public int getVideoStartTracksCount() {
        return videoStartTracks_.size();
      }
      /**
       * <code>repeated string video_start_tracks = 30;</code>
       * @param index The index of the element to return.
       * @return The videoStartTracks at the given index.
       */
      public String getVideoStartTracks(int index) {
        return videoStartTracks_.get(index);
      }
      /**
       * <code>repeated string video_start_tracks = 30;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoStartTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getVideoStartTracksBytes(int index) {
        return videoStartTracks_.getByteString(index);
      }

      public static final int VIDEO_COMPLETE_TRACKS_FIELD_NUMBER = 31;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList videoCompleteTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string video_complete_tracks = 31;</code>
       * @return A list containing the videoCompleteTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getVideoCompleteTracksList() {
        return videoCompleteTracks_;
      }
      /**
       * <code>repeated string video_complete_tracks = 31;</code>
       * @return The count of videoCompleteTracks.
       */
      public int getVideoCompleteTracksCount() {
        return videoCompleteTracks_.size();
      }
      /**
       * <code>repeated string video_complete_tracks = 31;</code>
       * @param index The index of the element to return.
       * @return The videoCompleteTracks at the given index.
       */
      public String getVideoCompleteTracks(int index) {
        return videoCompleteTracks_.get(index);
      }
      /**
       * <code>repeated string video_complete_tracks = 31;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoCompleteTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getVideoCompleteTracksBytes(int index) {
        return videoCompleteTracks_.getByteString(index);
      }

      public static final int AD_CLOSE_TRACKS_FIELD_NUMBER = 32;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList adCloseTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string ad_close_tracks = 32;</code>
       * @return A list containing the adCloseTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getAdCloseTracksList() {
        return adCloseTracks_;
      }
      /**
       * <code>repeated string ad_close_tracks = 32;</code>
       * @return The count of adCloseTracks.
       */
      public int getAdCloseTracksCount() {
        return adCloseTracks_.size();
      }
      /**
       * <code>repeated string ad_close_tracks = 32;</code>
       * @param index The index of the element to return.
       * @return The adCloseTracks at the given index.
       */
      public String getAdCloseTracks(int index) {
        return adCloseTracks_.get(index);
      }
      /**
       * <code>repeated string ad_close_tracks = 32;</code>
       * @param index The index of the value to return.
       * @return The bytes of the adCloseTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getAdCloseTracksBytes(int index) {
        return adCloseTracks_.getByteString(index);
      }

      public static final int MACRO_FIELD_NUMBER = 33;
      private int macro_ = 0;
      /**
       * <code>int32 macro = 33;</code>
       * @return The macro.
       */
      @Override
      public int getMacro() {
        return macro_;
      }

      public static final int FEEDBACKS_FIELD_NUMBER = 34;
      @SuppressWarnings("serial")
      private java.util.List<Feedbacks> feedbacks_;
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
       */
      @Override
      public java.util.List<Feedbacks> getFeedbacksList() {
        return feedbacks_;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
       */
      @Override
      public java.util.List<? extends FeedbacksOrBuilder>
          getFeedbacksOrBuilderList() {
        return feedbacks_;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
       */
      @Override
      public int getFeedbacksCount() {
        return feedbacks_.size();
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
       */
      @Override
      public Feedbacks getFeedbacks(int index) {
        return feedbacks_.get(index);
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
       */
      @Override
      public FeedbacksOrBuilder getFeedbacksOrBuilder(
          int index) {
        return feedbacks_.get(index);
      }

      public static final int VIDEO_PLAY_FIRST_QUARTILE_TRACKS_FIELD_NUMBER = 35;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList videoPlayFirstQuartileTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string video_play_first_quartile_tracks = 35;</code>
       * @return A list containing the videoPlayFirstQuartileTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getVideoPlayFirstQuartileTracksList() {
        return videoPlayFirstQuartileTracks_;
      }
      /**
       * <code>repeated string video_play_first_quartile_tracks = 35;</code>
       * @return The count of videoPlayFirstQuartileTracks.
       */
      public int getVideoPlayFirstQuartileTracksCount() {
        return videoPlayFirstQuartileTracks_.size();
      }
      /**
       * <code>repeated string video_play_first_quartile_tracks = 35;</code>
       * @param index The index of the element to return.
       * @return The videoPlayFirstQuartileTracks at the given index.
       */
      public String getVideoPlayFirstQuartileTracks(int index) {
        return videoPlayFirstQuartileTracks_.get(index);
      }
      /**
       * <code>repeated string video_play_first_quartile_tracks = 35;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoPlayFirstQuartileTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getVideoPlayFirstQuartileTracksBytes(int index) {
        return videoPlayFirstQuartileTracks_.getByteString(index);
      }

      public static final int VIDEO_PLAY_MIDPOINT_TRACKS_FIELD_NUMBER = 36;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList videoPlayMidpointTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string video_play_midpoint_tracks = 36;</code>
       * @return A list containing the videoPlayMidpointTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getVideoPlayMidpointTracksList() {
        return videoPlayMidpointTracks_;
      }
      /**
       * <code>repeated string video_play_midpoint_tracks = 36;</code>
       * @return The count of videoPlayMidpointTracks.
       */
      public int getVideoPlayMidpointTracksCount() {
        return videoPlayMidpointTracks_.size();
      }
      /**
       * <code>repeated string video_play_midpoint_tracks = 36;</code>
       * @param index The index of the element to return.
       * @return The videoPlayMidpointTracks at the given index.
       */
      public String getVideoPlayMidpointTracks(int index) {
        return videoPlayMidpointTracks_.get(index);
      }
      /**
       * <code>repeated string video_play_midpoint_tracks = 36;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoPlayMidpointTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getVideoPlayMidpointTracksBytes(int index) {
        return videoPlayMidpointTracks_.getByteString(index);
      }

      public static final int VIDEO_PLAY_THIRD_QUARTILE_TRACKS_FIELD_NUMBER = 37;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList videoPlayThirdQuartileTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string video_play_third_quartile_tracks = 37;</code>
       * @return A list containing the videoPlayThirdQuartileTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getVideoPlayThirdQuartileTracksList() {
        return videoPlayThirdQuartileTracks_;
      }
      /**
       * <code>repeated string video_play_third_quartile_tracks = 37;</code>
       * @return The count of videoPlayThirdQuartileTracks.
       */
      public int getVideoPlayThirdQuartileTracksCount() {
        return videoPlayThirdQuartileTracks_.size();
      }
      /**
       * <code>repeated string video_play_third_quartile_tracks = 37;</code>
       * @param index The index of the element to return.
       * @return The videoPlayThirdQuartileTracks at the given index.
       */
      public String getVideoPlayThirdQuartileTracks(int index) {
        return videoPlayThirdQuartileTracks_.get(index);
      }
      /**
       * <code>repeated string video_play_third_quartile_tracks = 37;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoPlayThirdQuartileTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getVideoPlayThirdQuartileTracksBytes(int index) {
        return videoPlayThirdQuartileTracks_.getByteString(index);
      }

      public static final int VIDEO_SKIP_TRACKS_FIELD_NUMBER = 38;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList videoSkipTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string video_skip_tracks = 38;</code>
       * @return A list containing the videoSkipTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getVideoSkipTracksList() {
        return videoSkipTracks_;
      }
      /**
       * <code>repeated string video_skip_tracks = 38;</code>
       * @return The count of videoSkipTracks.
       */
      public int getVideoSkipTracksCount() {
        return videoSkipTracks_.size();
      }
      /**
       * <code>repeated string video_skip_tracks = 38;</code>
       * @param index The index of the element to return.
       * @return The videoSkipTracks at the given index.
       */
      public String getVideoSkipTracks(int index) {
        return videoSkipTracks_.get(index);
      }
      /**
       * <code>repeated string video_skip_tracks = 38;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoSkipTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getVideoSkipTracksBytes(int index) {
        return videoSkipTracks_.getByteString(index);
      }

      public static final int VIDEO_CLICK_TRACKS_FIELD_NUMBER = 39;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList videoClickTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string video_click_tracks = 39;</code>
       * @return A list containing the videoClickTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getVideoClickTracksList() {
        return videoClickTracks_;
      }
      /**
       * <code>repeated string video_click_tracks = 39;</code>
       * @return The count of videoClickTracks.
       */
      public int getVideoClickTracksCount() {
        return videoClickTracks_.size();
      }
      /**
       * <code>repeated string video_click_tracks = 39;</code>
       * @param index The index of the element to return.
       * @return The videoClickTracks at the given index.
       */
      public String getVideoClickTracks(int index) {
        return videoClickTracks_.get(index);
      }
      /**
       * <code>repeated string video_click_tracks = 39;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoClickTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getVideoClickTracksBytes(int index) {
        return videoClickTracks_.getByteString(index);
      }

      public static final int VIDEO_MUTE_TRACKS_FIELD_NUMBER = 40;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList videoMuteTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string video_mute_tracks = 40;</code>
       * @return A list containing the videoMuteTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getVideoMuteTracksList() {
        return videoMuteTracks_;
      }
      /**
       * <code>repeated string video_mute_tracks = 40;</code>
       * @return The count of videoMuteTracks.
       */
      public int getVideoMuteTracksCount() {
        return videoMuteTracks_.size();
      }
      /**
       * <code>repeated string video_mute_tracks = 40;</code>
       * @param index The index of the element to return.
       * @return The videoMuteTracks at the given index.
       */
      public String getVideoMuteTracks(int index) {
        return videoMuteTracks_.get(index);
      }
      /**
       * <code>repeated string video_mute_tracks = 40;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoMuteTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getVideoMuteTracksBytes(int index) {
        return videoMuteTracks_.getByteString(index);
      }

      public static final int VIDEO_UNMUTE_TRACKS_FIELD_NUMBER = 41;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList videoUnmuteTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string video_unmute_tracks = 41;</code>
       * @return A list containing the videoUnmuteTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getVideoUnmuteTracksList() {
        return videoUnmuteTracks_;
      }
      /**
       * <code>repeated string video_unmute_tracks = 41;</code>
       * @return The count of videoUnmuteTracks.
       */
      public int getVideoUnmuteTracksCount() {
        return videoUnmuteTracks_.size();
      }
      /**
       * <code>repeated string video_unmute_tracks = 41;</code>
       * @param index The index of the element to return.
       * @return The videoUnmuteTracks at the given index.
       */
      public String getVideoUnmuteTracks(int index) {
        return videoUnmuteTracks_.get(index);
      }
      /**
       * <code>repeated string video_unmute_tracks = 41;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videoUnmuteTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getVideoUnmuteTracksBytes(int index) {
        return videoUnmuteTracks_.getByteString(index);
      }

      public static final int SOURCE_FIELD_NUMBER = 42;
      @SuppressWarnings("serial")
      private volatile Object source_ = "";
      /**
       * <code>string source = 42;</code>
       * @return The source.
       */
      @Override
      public String getSource() {
        Object ref = source_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          source_ = s;
          return s;
        }
      }
      /**
       * <code>string source = 42;</code>
       * @return The bytes for source.
       */
      @Override
      public com.google.protobuf.ByteString
          getSourceBytes() {
        Object ref = source_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          source_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int PRICE_FIELD_NUMBER = 43;
      private int price_ = 0;
      /**
       * <code>int32 price = 43;</code>
       * @return The price.
       */
      @Override
      public int getPrice() {
        return price_;
      }

      public static final int NURL_FIELD_NUMBER = 44;
      @SuppressWarnings("serial")
      private volatile Object nurl_ = "";
      /**
       * <code>string nurl = 44;</code>
       * @return The nurl.
       */
      @Override
      public String getNurl() {
        Object ref = nurl_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          nurl_ = s;
          return s;
        }
      }
      /**
       * <code>string nurl = 44;</code>
       * @return The bytes for nurl.
       */
      @Override
      public com.google.protobuf.ByteString
          getNurlBytes() {
        Object ref = nurl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          nurl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int LURL_FIELD_NUMBER = 45;
      @SuppressWarnings("serial")
      private volatile Object lurl_ = "";
      /**
       * <code>string lurl = 45;</code>
       * @return The lurl.
       */
      @Override
      public String getLurl() {
        Object ref = lurl_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          lurl_ = s;
          return s;
        }
      }
      /**
       * <code>string lurl = 45;</code>
       * @return The bytes for lurl.
       */
      @Override
      public com.google.protobuf.ByteString
          getLurlBytes() {
        Object ref = lurl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          lurl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int EXT_FIELD_NUMBER = 46;
      private Ext ext_;
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
       * @return Whether the ext field is set.
       */
      @Override
      public boolean hasExt() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
       * @return The ext.
       */
      @Override
      public Ext getExt() {
        return ext_ == null ? Ext.getDefaultInstance() : ext_;
      }
      /**
       * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
       */
      @Override
      public ExtOrBuilder getExtOrBuilder() {
        return ext_ == null ? Ext.getDefaultInstance() : ext_;
      }

      public static final int UNIVERSAL_LINK_FIELD_NUMBER = 47;
      @SuppressWarnings("serial")
      private volatile Object universalLink_ = "";
      /**
       * <code>string universal_link = 47;</code>
       * @return The universalLink.
       */
      @Override
      public String getUniversalLink() {
        Object ref = universalLink_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          universalLink_ = s;
          return s;
        }
      }
      /**
       * <code>string universal_link = 47;</code>
       * @return The bytes for universalLink.
       */
      @Override
      public com.google.protobuf.ByteString
          getUniversalLinkBytes() {
        Object ref = universalLink_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          universalLink_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DP_FAIL_TRACKS_FIELD_NUMBER = 48;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList dpFailTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string dp_fail_tracks = 48;</code>
       * @return A list containing the dpFailTracks.
       */
      public com.google.protobuf.ProtocolStringList
          getDpFailTracksList() {
        return dpFailTracks_;
      }
      /**
       * <code>repeated string dp_fail_tracks = 48;</code>
       * @return The count of dpFailTracks.
       */
      public int getDpFailTracksCount() {
        return dpFailTracks_.size();
      }
      /**
       * <code>repeated string dp_fail_tracks = 48;</code>
       * @param index The index of the element to return.
       * @return The dpFailTracks at the given index.
       */
      public String getDpFailTracks(int index) {
        return dpFailTracks_.get(index);
      }
      /**
       * <code>repeated string dp_fail_tracks = 48;</code>
       * @param index The index of the value to return.
       * @return The bytes of the dpFailTracks at the given index.
       */
      public com.google.protobuf.ByteString
          getDpFailTracksBytes(int index) {
        return dpFailTracks_.getByteString(index);
      }

      private byte memoizedIsInitialized = -1;
      @Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 1, id_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(impid_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 2, impid_);
        }
        if (bidtype_ != 0) {
          output.writeInt32(3, bidtype_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(crid_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 4, crid_);
        }
        for (int i = 0; i < dpTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 6, dpTracks_.getRaw(i));
        }
        for (int i = 0; i < impTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 7, impTracks_.getRaw(i));
        }
        for (int i = 0; i < clickTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 8, clickTracks_.getRaw(i));
        }
        for (int i = 0; i < downStartTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 9, downStartTracks_.getRaw(i));
        }
        for (int i = 0; i < downCompleteTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 10, downCompleteTracks_.getRaw(i));
        }
        for (int i = 0; i < installStartTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 12, installStartTracks_.getRaw(i));
        }
        for (int i = 0; i < installCompleteTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 13, installCompleteTracks_.getRaw(i));
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appname_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 14, appname_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bundle_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 15, bundle_);
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeMessage(16, getImage());
        }
        for (int i = 0; i < images_.size(); i++) {
          output.writeMessage(17, images_.get(i));
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeMessage(18, getLogo());
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeMessage(19, getAppicon());
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 20, title_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(des_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 21, des_);
        }
        if (isDownload_ != false) {
          output.writeBool(23, isDownload_);
        }
        if (isDeep_ != false) {
          output.writeBool(24, isDeep_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(fileUrl_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 25, fileUrl_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(landingUrl_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 26, landingUrl_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(deeplink_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 27, deeplink_);
        }
        if (expirationTime_ != 0L) {
          output.writeInt64(28, expirationTime_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          output.writeMessage(29, getVideo());
        }
        for (int i = 0; i < videoStartTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 30, videoStartTracks_.getRaw(i));
        }
        for (int i = 0; i < videoCompleteTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 31, videoCompleteTracks_.getRaw(i));
        }
        for (int i = 0; i < adCloseTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 32, adCloseTracks_.getRaw(i));
        }
        if (macro_ != 0) {
          output.writeInt32(33, macro_);
        }
        for (int i = 0; i < feedbacks_.size(); i++) {
          output.writeMessage(34, feedbacks_.get(i));
        }
        for (int i = 0; i < videoPlayFirstQuartileTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 35, videoPlayFirstQuartileTracks_.getRaw(i));
        }
        for (int i = 0; i < videoPlayMidpointTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 36, videoPlayMidpointTracks_.getRaw(i));
        }
        for (int i = 0; i < videoPlayThirdQuartileTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 37, videoPlayThirdQuartileTracks_.getRaw(i));
        }
        for (int i = 0; i < videoSkipTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 38, videoSkipTracks_.getRaw(i));
        }
        for (int i = 0; i < videoClickTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 39, videoClickTracks_.getRaw(i));
        }
        for (int i = 0; i < videoMuteTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 40, videoMuteTracks_.getRaw(i));
        }
        for (int i = 0; i < videoUnmuteTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 41, videoUnmuteTracks_.getRaw(i));
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(source_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 42, source_);
        }
        if (price_ != 0) {
          output.writeInt32(43, price_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(nurl_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 44, nurl_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(lurl_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 45, lurl_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          output.writeMessage(46, getExt());
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(universalLink_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 47, universalLink_);
        }
        for (int i = 0; i < dpFailTracks_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 48, dpFailTracks_.getRaw(i));
        }
        getUnknownFields().writeTo(output);
      }

      @Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(1, id_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(impid_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(2, impid_);
        }
        if (bidtype_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(3, bidtype_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(crid_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(4, crid_);
        }
        {
          int dataSize = 0;
          for (int i = 0; i < dpTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(dpTracks_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getDpTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < impTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(impTracks_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getImpTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < clickTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(clickTracks_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getClickTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < downStartTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(downStartTracks_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getDownStartTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < downCompleteTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(downCompleteTracks_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getDownCompleteTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < installStartTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(installStartTracks_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getInstallStartTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < installCompleteTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(installCompleteTracks_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getInstallCompleteTracksList().size();
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appname_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(14, appname_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bundle_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(15, bundle_);
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(16, getImage());
        }
        for (int i = 0; i < images_.size(); i++) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(17, images_.get(i));
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(18, getLogo());
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(19, getAppicon());
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(20, title_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(des_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(21, des_);
        }
        if (isDownload_ != false) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(23, isDownload_);
        }
        if (isDeep_ != false) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(24, isDeep_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(fileUrl_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(25, fileUrl_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(landingUrl_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(26, landingUrl_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(deeplink_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(27, deeplink_);
        }
        if (expirationTime_ != 0L) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt64Size(28, expirationTime_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(29, getVideo());
        }
        {
          int dataSize = 0;
          for (int i = 0; i < videoStartTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(videoStartTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getVideoStartTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < videoCompleteTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(videoCompleteTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getVideoCompleteTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < adCloseTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(adCloseTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getAdCloseTracksList().size();
        }
        if (macro_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(33, macro_);
        }
        for (int i = 0; i < feedbacks_.size(); i++) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(34, feedbacks_.get(i));
        }
        {
          int dataSize = 0;
          for (int i = 0; i < videoPlayFirstQuartileTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(videoPlayFirstQuartileTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getVideoPlayFirstQuartileTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < videoPlayMidpointTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(videoPlayMidpointTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getVideoPlayMidpointTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < videoPlayThirdQuartileTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(videoPlayThirdQuartileTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getVideoPlayThirdQuartileTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < videoSkipTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(videoSkipTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getVideoSkipTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < videoClickTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(videoClickTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getVideoClickTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < videoMuteTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(videoMuteTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getVideoMuteTracksList().size();
        }
        {
          int dataSize = 0;
          for (int i = 0; i < videoUnmuteTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(videoUnmuteTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getVideoUnmuteTracksList().size();
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(source_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(42, source_);
        }
        if (price_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(43, price_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(nurl_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(44, nurl_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(lurl_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(45, lurl_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(46, getExt());
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(universalLink_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(47, universalLink_);
        }
        {
          int dataSize = 0;
          for (int i = 0; i < dpFailTracks_.size(); i++) {
            dataSize += computeStringSizeNoTag(dpFailTracks_.getRaw(i));
          }
          size += dataSize;
          size += 2 * getDpFailTracksList().size();
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @Override
      public boolean equals(final Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof Bid)) {
          return super.equals(obj);
        }
        Bid other = (Bid) obj;

        if (!getId()
            .equals(other.getId())) return false;
        if (!getImpid()
            .equals(other.getImpid())) return false;
        if (getBidtype()
            != other.getBidtype()) return false;
        if (!getCrid()
            .equals(other.getCrid())) return false;
        if (!getDpTracksList()
            .equals(other.getDpTracksList())) return false;
        if (!getImpTracksList()
            .equals(other.getImpTracksList())) return false;
        if (!getClickTracksList()
            .equals(other.getClickTracksList())) return false;
        if (!getDownStartTracksList()
            .equals(other.getDownStartTracksList())) return false;
        if (!getDownCompleteTracksList()
            .equals(other.getDownCompleteTracksList())) return false;
        if (!getInstallStartTracksList()
            .equals(other.getInstallStartTracksList())) return false;
        if (!getInstallCompleteTracksList()
            .equals(other.getInstallCompleteTracksList())) return false;
        if (!getAppname()
            .equals(other.getAppname())) return false;
        if (!getBundle()
            .equals(other.getBundle())) return false;
        if (hasImage() != other.hasImage()) return false;
        if (hasImage()) {
          if (!getImage()
              .equals(other.getImage())) return false;
        }
        if (!getImagesList()
            .equals(other.getImagesList())) return false;
        if (hasLogo() != other.hasLogo()) return false;
        if (hasLogo()) {
          if (!getLogo()
              .equals(other.getLogo())) return false;
        }
        if (hasAppicon() != other.hasAppicon()) return false;
        if (hasAppicon()) {
          if (!getAppicon()
              .equals(other.getAppicon())) return false;
        }
        if (!getTitle()
            .equals(other.getTitle())) return false;
        if (!getDes()
            .equals(other.getDes())) return false;
        if (getIsDownload()
            != other.getIsDownload()) return false;
        if (getIsDeep()
            != other.getIsDeep()) return false;
        if (!getFileUrl()
            .equals(other.getFileUrl())) return false;
        if (!getLandingUrl()
            .equals(other.getLandingUrl())) return false;
        if (!getDeeplink()
            .equals(other.getDeeplink())) return false;
        if (getExpirationTime()
            != other.getExpirationTime()) return false;
        if (hasVideo() != other.hasVideo()) return false;
        if (hasVideo()) {
          if (!getVideo()
              .equals(other.getVideo())) return false;
        }
        if (!getVideoStartTracksList()
            .equals(other.getVideoStartTracksList())) return false;
        if (!getVideoCompleteTracksList()
            .equals(other.getVideoCompleteTracksList())) return false;
        if (!getAdCloseTracksList()
            .equals(other.getAdCloseTracksList())) return false;
        if (getMacro()
            != other.getMacro()) return false;
        if (!getFeedbacksList()
            .equals(other.getFeedbacksList())) return false;
        if (!getVideoPlayFirstQuartileTracksList()
            .equals(other.getVideoPlayFirstQuartileTracksList())) return false;
        if (!getVideoPlayMidpointTracksList()
            .equals(other.getVideoPlayMidpointTracksList())) return false;
        if (!getVideoPlayThirdQuartileTracksList()
            .equals(other.getVideoPlayThirdQuartileTracksList())) return false;
        if (!getVideoSkipTracksList()
            .equals(other.getVideoSkipTracksList())) return false;
        if (!getVideoClickTracksList()
            .equals(other.getVideoClickTracksList())) return false;
        if (!getVideoMuteTracksList()
            .equals(other.getVideoMuteTracksList())) return false;
        if (!getVideoUnmuteTracksList()
            .equals(other.getVideoUnmuteTracksList())) return false;
        if (!getSource()
            .equals(other.getSource())) return false;
        if (getPrice()
            != other.getPrice()) return false;
        if (!getNurl()
            .equals(other.getNurl())) return false;
        if (!getLurl()
            .equals(other.getLurl())) return false;
        if (hasExt() != other.hasExt()) return false;
        if (hasExt()) {
          if (!getExt()
              .equals(other.getExt())) return false;
        }
        if (!getUniversalLink()
            .equals(other.getUniversalLink())) return false;
        if (!getDpFailTracksList()
            .equals(other.getDpFailTracksList())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + getId().hashCode();
        hash = (37 * hash) + IMPID_FIELD_NUMBER;
        hash = (53 * hash) + getImpid().hashCode();
        hash = (37 * hash) + BIDTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getBidtype();
        hash = (37 * hash) + CRID_FIELD_NUMBER;
        hash = (53 * hash) + getCrid().hashCode();
        if (getDpTracksCount() > 0) {
          hash = (37 * hash) + DP_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getDpTracksList().hashCode();
        }
        if (getImpTracksCount() > 0) {
          hash = (37 * hash) + IMP_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getImpTracksList().hashCode();
        }
        if (getClickTracksCount() > 0) {
          hash = (37 * hash) + CLICK_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getClickTracksList().hashCode();
        }
        if (getDownStartTracksCount() > 0) {
          hash = (37 * hash) + DOWN_START_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getDownStartTracksList().hashCode();
        }
        if (getDownCompleteTracksCount() > 0) {
          hash = (37 * hash) + DOWN_COMPLETE_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getDownCompleteTracksList().hashCode();
        }
        if (getInstallStartTracksCount() > 0) {
          hash = (37 * hash) + INSTALL_START_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getInstallStartTracksList().hashCode();
        }
        if (getInstallCompleteTracksCount() > 0) {
          hash = (37 * hash) + INSTALL_COMPLETE_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getInstallCompleteTracksList().hashCode();
        }
        hash = (37 * hash) + APPNAME_FIELD_NUMBER;
        hash = (53 * hash) + getAppname().hashCode();
        hash = (37 * hash) + BUNDLE_FIELD_NUMBER;
        hash = (53 * hash) + getBundle().hashCode();
        if (hasImage()) {
          hash = (37 * hash) + IMAGE_FIELD_NUMBER;
          hash = (53 * hash) + getImage().hashCode();
        }
        if (getImagesCount() > 0) {
          hash = (37 * hash) + IMAGES_FIELD_NUMBER;
          hash = (53 * hash) + getImagesList().hashCode();
        }
        if (hasLogo()) {
          hash = (37 * hash) + LOGO_FIELD_NUMBER;
          hash = (53 * hash) + getLogo().hashCode();
        }
        if (hasAppicon()) {
          hash = (37 * hash) + APPICON_FIELD_NUMBER;
          hash = (53 * hash) + getAppicon().hashCode();
        }
        hash = (37 * hash) + TITLE_FIELD_NUMBER;
        hash = (53 * hash) + getTitle().hashCode();
        hash = (37 * hash) + DES_FIELD_NUMBER;
        hash = (53 * hash) + getDes().hashCode();
        hash = (37 * hash) + IS_DOWNLOAD_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsDownload());
        hash = (37 * hash) + IS_DEEP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsDeep());
        hash = (37 * hash) + FILE_URL_FIELD_NUMBER;
        hash = (53 * hash) + getFileUrl().hashCode();
        hash = (37 * hash) + LANDING_URL_FIELD_NUMBER;
        hash = (53 * hash) + getLandingUrl().hashCode();
        hash = (37 * hash) + DEEPLINK_FIELD_NUMBER;
        hash = (53 * hash) + getDeeplink().hashCode();
        hash = (37 * hash) + EXPIRATIONTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getExpirationTime());
        if (hasVideo()) {
          hash = (37 * hash) + VIDEO_FIELD_NUMBER;
          hash = (53 * hash) + getVideo().hashCode();
        }
        if (getVideoStartTracksCount() > 0) {
          hash = (37 * hash) + VIDEO_START_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getVideoStartTracksList().hashCode();
        }
        if (getVideoCompleteTracksCount() > 0) {
          hash = (37 * hash) + VIDEO_COMPLETE_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getVideoCompleteTracksList().hashCode();
        }
        if (getAdCloseTracksCount() > 0) {
          hash = (37 * hash) + AD_CLOSE_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getAdCloseTracksList().hashCode();
        }
        hash = (37 * hash) + MACRO_FIELD_NUMBER;
        hash = (53 * hash) + getMacro();
        if (getFeedbacksCount() > 0) {
          hash = (37 * hash) + FEEDBACKS_FIELD_NUMBER;
          hash = (53 * hash) + getFeedbacksList().hashCode();
        }
        if (getVideoPlayFirstQuartileTracksCount() > 0) {
          hash = (37 * hash) + VIDEO_PLAY_FIRST_QUARTILE_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getVideoPlayFirstQuartileTracksList().hashCode();
        }
        if (getVideoPlayMidpointTracksCount() > 0) {
          hash = (37 * hash) + VIDEO_PLAY_MIDPOINT_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getVideoPlayMidpointTracksList().hashCode();
        }
        if (getVideoPlayThirdQuartileTracksCount() > 0) {
          hash = (37 * hash) + VIDEO_PLAY_THIRD_QUARTILE_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getVideoPlayThirdQuartileTracksList().hashCode();
        }
        if (getVideoSkipTracksCount() > 0) {
          hash = (37 * hash) + VIDEO_SKIP_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getVideoSkipTracksList().hashCode();
        }
        if (getVideoClickTracksCount() > 0) {
          hash = (37 * hash) + VIDEO_CLICK_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getVideoClickTracksList().hashCode();
        }
        if (getVideoMuteTracksCount() > 0) {
          hash = (37 * hash) + VIDEO_MUTE_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getVideoMuteTracksList().hashCode();
        }
        if (getVideoUnmuteTracksCount() > 0) {
          hash = (37 * hash) + VIDEO_UNMUTE_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getVideoUnmuteTracksList().hashCode();
        }
        hash = (37 * hash) + SOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getSource().hashCode();
        hash = (37 * hash) + PRICE_FIELD_NUMBER;
        hash = (53 * hash) + getPrice();
        hash = (37 * hash) + NURL_FIELD_NUMBER;
        hash = (53 * hash) + getNurl().hashCode();
        hash = (37 * hash) + LURL_FIELD_NUMBER;
        hash = (53 * hash) + getLurl().hashCode();
        if (hasExt()) {
          hash = (37 * hash) + EXT_FIELD_NUMBER;
          hash = (53 * hash) + getExt().hashCode();
        }
        hash = (37 * hash) + UNIVERSAL_LINK_FIELD_NUMBER;
        hash = (53 * hash) + getUniversalLink().hashCode();
        if (getDpFailTracksCount() > 0) {
          hash = (37 * hash) + DP_FAIL_TRACKS_FIELD_NUMBER;
          hash = (53 * hash) + getDpFailTracksList().hashCode();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static Bid parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static Bid parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static Bid parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static Bid parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static Bid parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static Bid parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static Bid parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static Bid parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static Bid parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static Bid parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static Bid parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static Bid parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(Bid prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @Override
      protected Builder newBuilderForType(
          BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code dsp.BidResponse.SeatBid.Bid}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:dsp.BidResponse.SeatBid.Bid)
          BidOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_descriptor;
        }

        @Override
        protected FieldAccessorTable
            internalGetFieldAccessorTable() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  Bid.class, Builder.class);
        }

        // Construct using cn.taken.ad.logic.prossor.hailiang.dto.BidResponse.SeatBid.Bid.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessage
                  .alwaysUseFieldBuilders) {
            getImageFieldBuilder();
            getImagesFieldBuilder();
            getLogoFieldBuilder();
            getAppiconFieldBuilder();
            getVideoFieldBuilder();
            getFeedbacksFieldBuilder();
            getExtFieldBuilder();
          }
        }
        @Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          bitField1_ = 0;
          id_ = "";
          impid_ = "";
          bidtype_ = 0;
          crid_ = "";
          dpTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          impTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          clickTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          downStartTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          downCompleteTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          installStartTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          installCompleteTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          appname_ = "";
          bundle_ = "";
          image_ = null;
          if (imageBuilder_ != null) {
            imageBuilder_.dispose();
            imageBuilder_ = null;
          }
          if (imagesBuilder_ == null) {
            images_ = java.util.Collections.emptyList();
          } else {
            images_ = null;
            imagesBuilder_.clear();
          }
          bitField0_ = (bitField0_ & ~0x00004000);
          logo_ = null;
          if (logoBuilder_ != null) {
            logoBuilder_.dispose();
            logoBuilder_ = null;
          }
          appicon_ = null;
          if (appiconBuilder_ != null) {
            appiconBuilder_.dispose();
            appiconBuilder_ = null;
          }
          title_ = "";
          des_ = "";
          isDownload_ = false;
          isDeep_ = false;
          fileUrl_ = "";
          landingUrl_ = "";
          deeplink_ = "";
          expirationTime_ = 0L;
          video_ = null;
          if (videoBuilder_ != null) {
            videoBuilder_.dispose();
            videoBuilder_ = null;
          }
          videoStartTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          videoCompleteTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          adCloseTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          macro_ = 0;
          if (feedbacksBuilder_ == null) {
            feedbacks_ = java.util.Collections.emptyList();
          } else {
            feedbacks_ = null;
            feedbacksBuilder_.clear();
          }
          bitField0_ = (bitField0_ & ~0x40000000);
          videoPlayFirstQuartileTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          videoPlayMidpointTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          videoPlayThirdQuartileTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          videoSkipTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          videoClickTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          videoMuteTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          videoUnmuteTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          source_ = "";
          price_ = 0;
          nurl_ = "";
          lurl_ = "";
          ext_ = null;
          if (extBuilder_ != null) {
            extBuilder_.dispose();
            extBuilder_ = null;
          }
          universalLink_ = "";
          dpFailTracks_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          return this;
        }

        @Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_Bid_descriptor;
        }

        @Override
        public Bid getDefaultInstanceForType() {
          return Bid.getDefaultInstance();
        }

        @Override
        public Bid build() {
          Bid result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @Override
        public Bid buildPartial() {
          Bid result = new Bid(this);
          buildPartialRepeatedFields(result);
          if (bitField0_ != 0) { buildPartial0(result); }
          if (bitField1_ != 0) { buildPartial1(result); }
          onBuilt();
          return result;
        }

        private void buildPartialRepeatedFields(Bid result) {
          if (imagesBuilder_ == null) {
            if (((bitField0_ & 0x00004000) != 0)) {
              images_ = java.util.Collections.unmodifiableList(images_);
              bitField0_ = (bitField0_ & ~0x00004000);
            }
            result.images_ = images_;
          } else {
            result.images_ = imagesBuilder_.build();
          }
          if (feedbacksBuilder_ == null) {
            if (((bitField0_ & 0x40000000) != 0)) {
              feedbacks_ = java.util.Collections.unmodifiableList(feedbacks_);
              bitField0_ = (bitField0_ & ~0x40000000);
            }
            result.feedbacks_ = feedbacks_;
          } else {
            result.feedbacks_ = feedbacksBuilder_.build();
          }
        }

        private void buildPartial0(Bid result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.id_ = id_;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.impid_ = impid_;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.bidtype_ = bidtype_;
          }
          if (((from_bitField0_ & 0x00000008) != 0)) {
            result.crid_ = crid_;
          }
          if (((from_bitField0_ & 0x00000010) != 0)) {
            dpTracks_.makeImmutable();
            result.dpTracks_ = dpTracks_;
          }
          if (((from_bitField0_ & 0x00000020) != 0)) {
            impTracks_.makeImmutable();
            result.impTracks_ = impTracks_;
          }
          if (((from_bitField0_ & 0x00000040) != 0)) {
            clickTracks_.makeImmutable();
            result.clickTracks_ = clickTracks_;
          }
          if (((from_bitField0_ & 0x00000080) != 0)) {
            downStartTracks_.makeImmutable();
            result.downStartTracks_ = downStartTracks_;
          }
          if (((from_bitField0_ & 0x00000100) != 0)) {
            downCompleteTracks_.makeImmutable();
            result.downCompleteTracks_ = downCompleteTracks_;
          }
          if (((from_bitField0_ & 0x00000200) != 0)) {
            installStartTracks_.makeImmutable();
            result.installStartTracks_ = installStartTracks_;
          }
          if (((from_bitField0_ & 0x00000400) != 0)) {
            installCompleteTracks_.makeImmutable();
            result.installCompleteTracks_ = installCompleteTracks_;
          }
          if (((from_bitField0_ & 0x00000800) != 0)) {
            result.appname_ = appname_;
          }
          if (((from_bitField0_ & 0x00001000) != 0)) {
            result.bundle_ = bundle_;
          }
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00002000) != 0)) {
            result.image_ = imageBuilder_ == null
                ? image_
                : imageBuilder_.build();
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00008000) != 0)) {
            result.logo_ = logoBuilder_ == null
                ? logo_
                : logoBuilder_.build();
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00010000) != 0)) {
            result.appicon_ = appiconBuilder_ == null
                ? appicon_
                : appiconBuilder_.build();
            to_bitField0_ |= 0x00000004;
          }
          if (((from_bitField0_ & 0x00020000) != 0)) {
            result.title_ = title_;
          }
          if (((from_bitField0_ & 0x00040000) != 0)) {
            result.des_ = des_;
          }
          if (((from_bitField0_ & 0x00080000) != 0)) {
            result.isDownload_ = isDownload_;
          }
          if (((from_bitField0_ & 0x00100000) != 0)) {
            result.isDeep_ = isDeep_;
          }
          if (((from_bitField0_ & 0x00200000) != 0)) {
            result.fileUrl_ = fileUrl_;
          }
          if (((from_bitField0_ & 0x00400000) != 0)) {
            result.landingUrl_ = landingUrl_;
          }
          if (((from_bitField0_ & 0x00800000) != 0)) {
            result.deeplink_ = deeplink_;
          }
          if (((from_bitField0_ & 0x01000000) != 0)) {
            result.expirationTime_ = expirationTime_;
          }
          if (((from_bitField0_ & 0x02000000) != 0)) {
            result.video_ = videoBuilder_ == null
                ? video_
                : videoBuilder_.build();
            to_bitField0_ |= 0x00000008;
          }
          if (((from_bitField0_ & 0x04000000) != 0)) {
            videoStartTracks_.makeImmutable();
            result.videoStartTracks_ = videoStartTracks_;
          }
          if (((from_bitField0_ & 0x08000000) != 0)) {
            videoCompleteTracks_.makeImmutable();
            result.videoCompleteTracks_ = videoCompleteTracks_;
          }
          if (((from_bitField0_ & 0x10000000) != 0)) {
            adCloseTracks_.makeImmutable();
            result.adCloseTracks_ = adCloseTracks_;
          }
          if (((from_bitField0_ & 0x20000000) != 0)) {
            result.macro_ = macro_;
          }
          if (((from_bitField0_ & 0x80000000) != 0)) {
            videoPlayFirstQuartileTracks_.makeImmutable();
            result.videoPlayFirstQuartileTracks_ = videoPlayFirstQuartileTracks_;
          }
          result.bitField0_ |= to_bitField0_;
        }

        private void buildPartial1(Bid result) {
          int from_bitField1_ = bitField1_;
          if (((from_bitField1_ & 0x00000001) != 0)) {
            videoPlayMidpointTracks_.makeImmutable();
            result.videoPlayMidpointTracks_ = videoPlayMidpointTracks_;
          }
          if (((from_bitField1_ & 0x00000002) != 0)) {
            videoPlayThirdQuartileTracks_.makeImmutable();
            result.videoPlayThirdQuartileTracks_ = videoPlayThirdQuartileTracks_;
          }
          if (((from_bitField1_ & 0x00000004) != 0)) {
            videoSkipTracks_.makeImmutable();
            result.videoSkipTracks_ = videoSkipTracks_;
          }
          if (((from_bitField1_ & 0x00000008) != 0)) {
            videoClickTracks_.makeImmutable();
            result.videoClickTracks_ = videoClickTracks_;
          }
          if (((from_bitField1_ & 0x00000010) != 0)) {
            videoMuteTracks_.makeImmutable();
            result.videoMuteTracks_ = videoMuteTracks_;
          }
          if (((from_bitField1_ & 0x00000020) != 0)) {
            videoUnmuteTracks_.makeImmutable();
            result.videoUnmuteTracks_ = videoUnmuteTracks_;
          }
          if (((from_bitField1_ & 0x00000040) != 0)) {
            result.source_ = source_;
          }
          if (((from_bitField1_ & 0x00000080) != 0)) {
            result.price_ = price_;
          }
          if (((from_bitField1_ & 0x00000100) != 0)) {
            result.nurl_ = nurl_;
          }
          if (((from_bitField1_ & 0x00000200) != 0)) {
            result.lurl_ = lurl_;
          }
          int to_bitField0_ = 0;
          if (((from_bitField1_ & 0x00000400) != 0)) {
            result.ext_ = extBuilder_ == null
                ? ext_
                : extBuilder_.build();
            to_bitField0_ |= 0x00000010;
          }
          if (((from_bitField1_ & 0x00000800) != 0)) {
            result.universalLink_ = universalLink_;
          }
          if (((from_bitField1_ & 0x00001000) != 0)) {
            dpFailTracks_.makeImmutable();
            result.dpFailTracks_ = dpFailTracks_;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof Bid) {
            return mergeFrom((Bid)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(Bid other) {
          if (other == Bid.getDefaultInstance()) return this;
          if (!other.getId().isEmpty()) {
            id_ = other.id_;
            bitField0_ |= 0x00000001;
            onChanged();
          }
          if (!other.getImpid().isEmpty()) {
            impid_ = other.impid_;
            bitField0_ |= 0x00000002;
            onChanged();
          }
          if (other.getBidtype() != 0) {
            setBidtype(other.getBidtype());
          }
          if (!other.getCrid().isEmpty()) {
            crid_ = other.crid_;
            bitField0_ |= 0x00000008;
            onChanged();
          }
          if (!other.dpTracks_.isEmpty()) {
            if (dpTracks_.isEmpty()) {
              dpTracks_ = other.dpTracks_;
              bitField0_ |= 0x00000010;
            } else {
              ensureDpTracksIsMutable();
              dpTracks_.addAll(other.dpTracks_);
            }
            onChanged();
          }
          if (!other.impTracks_.isEmpty()) {
            if (impTracks_.isEmpty()) {
              impTracks_ = other.impTracks_;
              bitField0_ |= 0x00000020;
            } else {
              ensureImpTracksIsMutable();
              impTracks_.addAll(other.impTracks_);
            }
            onChanged();
          }
          if (!other.clickTracks_.isEmpty()) {
            if (clickTracks_.isEmpty()) {
              clickTracks_ = other.clickTracks_;
              bitField0_ |= 0x00000040;
            } else {
              ensureClickTracksIsMutable();
              clickTracks_.addAll(other.clickTracks_);
            }
            onChanged();
          }
          if (!other.downStartTracks_.isEmpty()) {
            if (downStartTracks_.isEmpty()) {
              downStartTracks_ = other.downStartTracks_;
              bitField0_ |= 0x00000080;
            } else {
              ensureDownStartTracksIsMutable();
              downStartTracks_.addAll(other.downStartTracks_);
            }
            onChanged();
          }
          if (!other.downCompleteTracks_.isEmpty()) {
            if (downCompleteTracks_.isEmpty()) {
              downCompleteTracks_ = other.downCompleteTracks_;
              bitField0_ |= 0x00000100;
            } else {
              ensureDownCompleteTracksIsMutable();
              downCompleteTracks_.addAll(other.downCompleteTracks_);
            }
            onChanged();
          }
          if (!other.installStartTracks_.isEmpty()) {
            if (installStartTracks_.isEmpty()) {
              installStartTracks_ = other.installStartTracks_;
              bitField0_ |= 0x00000200;
            } else {
              ensureInstallStartTracksIsMutable();
              installStartTracks_.addAll(other.installStartTracks_);
            }
            onChanged();
          }
          if (!other.installCompleteTracks_.isEmpty()) {
            if (installCompleteTracks_.isEmpty()) {
              installCompleteTracks_ = other.installCompleteTracks_;
              bitField0_ |= 0x00000400;
            } else {
              ensureInstallCompleteTracksIsMutable();
              installCompleteTracks_.addAll(other.installCompleteTracks_);
            }
            onChanged();
          }
          if (!other.getAppname().isEmpty()) {
            appname_ = other.appname_;
            bitField0_ |= 0x00000800;
            onChanged();
          }
          if (!other.getBundle().isEmpty()) {
            bundle_ = other.bundle_;
            bitField0_ |= 0x00001000;
            onChanged();
          }
          if (other.hasImage()) {
            mergeImage(other.getImage());
          }
          if (imagesBuilder_ == null) {
            if (!other.images_.isEmpty()) {
              if (images_.isEmpty()) {
                images_ = other.images_;
                bitField0_ = (bitField0_ & ~0x00004000);
              } else {
                ensureImagesIsMutable();
                images_.addAll(other.images_);
              }
              onChanged();
            }
          } else {
            if (!other.images_.isEmpty()) {
              if (imagesBuilder_.isEmpty()) {
                imagesBuilder_.dispose();
                imagesBuilder_ = null;
                images_ = other.images_;
                bitField0_ = (bitField0_ & ~0x00004000);
                imagesBuilder_ = 
                  com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                     getImagesFieldBuilder() : null;
              } else {
                imagesBuilder_.addAllMessages(other.images_);
              }
            }
          }
          if (other.hasLogo()) {
            mergeLogo(other.getLogo());
          }
          if (other.hasAppicon()) {
            mergeAppicon(other.getAppicon());
          }
          if (!other.getTitle().isEmpty()) {
            title_ = other.title_;
            bitField0_ |= 0x00020000;
            onChanged();
          }
          if (!other.getDes().isEmpty()) {
            des_ = other.des_;
            bitField0_ |= 0x00040000;
            onChanged();
          }
          if (other.getIsDownload() != false) {
            setIsDownload(other.getIsDownload());
          }
          if (other.getIsDeep() != false) {
            setIsDeep(other.getIsDeep());
          }
          if (!other.getFileUrl().isEmpty()) {
            fileUrl_ = other.fileUrl_;
            bitField0_ |= 0x00200000;
            onChanged();
          }
          if (!other.getLandingUrl().isEmpty()) {
            landingUrl_ = other.landingUrl_;
            bitField0_ |= 0x00400000;
            onChanged();
          }
          if (!other.getDeeplink().isEmpty()) {
            deeplink_ = other.deeplink_;
            bitField0_ |= 0x00800000;
            onChanged();
          }
          if (other.getExpirationTime() != 0L) {
            setExpirationTime(other.getExpirationTime());
          }
          if (other.hasVideo()) {
            mergeVideo(other.getVideo());
          }
          if (!other.videoStartTracks_.isEmpty()) {
            if (videoStartTracks_.isEmpty()) {
              videoStartTracks_ = other.videoStartTracks_;
              bitField0_ |= 0x04000000;
            } else {
              ensureVideoStartTracksIsMutable();
              videoStartTracks_.addAll(other.videoStartTracks_);
            }
            onChanged();
          }
          if (!other.videoCompleteTracks_.isEmpty()) {
            if (videoCompleteTracks_.isEmpty()) {
              videoCompleteTracks_ = other.videoCompleteTracks_;
              bitField0_ |= 0x08000000;
            } else {
              ensureVideoCompleteTracksIsMutable();
              videoCompleteTracks_.addAll(other.videoCompleteTracks_);
            }
            onChanged();
          }
          if (!other.adCloseTracks_.isEmpty()) {
            if (adCloseTracks_.isEmpty()) {
              adCloseTracks_ = other.adCloseTracks_;
              bitField0_ |= 0x10000000;
            } else {
              ensureAdCloseTracksIsMutable();
              adCloseTracks_.addAll(other.adCloseTracks_);
            }
            onChanged();
          }
          if (other.getMacro() != 0) {
            setMacro(other.getMacro());
          }
          if (feedbacksBuilder_ == null) {
            if (!other.feedbacks_.isEmpty()) {
              if (feedbacks_.isEmpty()) {
                feedbacks_ = other.feedbacks_;
                bitField0_ = (bitField0_ & ~0x40000000);
              } else {
                ensureFeedbacksIsMutable();
                feedbacks_.addAll(other.feedbacks_);
              }
              onChanged();
            }
          } else {
            if (!other.feedbacks_.isEmpty()) {
              if (feedbacksBuilder_.isEmpty()) {
                feedbacksBuilder_.dispose();
                feedbacksBuilder_ = null;
                feedbacks_ = other.feedbacks_;
                bitField0_ = (bitField0_ & ~0x40000000);
                feedbacksBuilder_ = 
                  com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                     getFeedbacksFieldBuilder() : null;
              } else {
                feedbacksBuilder_.addAllMessages(other.feedbacks_);
              }
            }
          }
          if (!other.videoPlayFirstQuartileTracks_.isEmpty()) {
            if (videoPlayFirstQuartileTracks_.isEmpty()) {
              videoPlayFirstQuartileTracks_ = other.videoPlayFirstQuartileTracks_;
              bitField0_ |= 0x80000000;
            } else {
              ensureVideoPlayFirstQuartileTracksIsMutable();
              videoPlayFirstQuartileTracks_.addAll(other.videoPlayFirstQuartileTracks_);
            }
            onChanged();
          }
          if (!other.videoPlayMidpointTracks_.isEmpty()) {
            if (videoPlayMidpointTracks_.isEmpty()) {
              videoPlayMidpointTracks_ = other.videoPlayMidpointTracks_;
              bitField1_ |= 0x00000001;
            } else {
              ensureVideoPlayMidpointTracksIsMutable();
              videoPlayMidpointTracks_.addAll(other.videoPlayMidpointTracks_);
            }
            onChanged();
          }
          if (!other.videoPlayThirdQuartileTracks_.isEmpty()) {
            if (videoPlayThirdQuartileTracks_.isEmpty()) {
              videoPlayThirdQuartileTracks_ = other.videoPlayThirdQuartileTracks_;
              bitField1_ |= 0x00000002;
            } else {
              ensureVideoPlayThirdQuartileTracksIsMutable();
              videoPlayThirdQuartileTracks_.addAll(other.videoPlayThirdQuartileTracks_);
            }
            onChanged();
          }
          if (!other.videoSkipTracks_.isEmpty()) {
            if (videoSkipTracks_.isEmpty()) {
              videoSkipTracks_ = other.videoSkipTracks_;
              bitField1_ |= 0x00000004;
            } else {
              ensureVideoSkipTracksIsMutable();
              videoSkipTracks_.addAll(other.videoSkipTracks_);
            }
            onChanged();
          }
          if (!other.videoClickTracks_.isEmpty()) {
            if (videoClickTracks_.isEmpty()) {
              videoClickTracks_ = other.videoClickTracks_;
              bitField1_ |= 0x00000008;
            } else {
              ensureVideoClickTracksIsMutable();
              videoClickTracks_.addAll(other.videoClickTracks_);
            }
            onChanged();
          }
          if (!other.videoMuteTracks_.isEmpty()) {
            if (videoMuteTracks_.isEmpty()) {
              videoMuteTracks_ = other.videoMuteTracks_;
              bitField1_ |= 0x00000010;
            } else {
              ensureVideoMuteTracksIsMutable();
              videoMuteTracks_.addAll(other.videoMuteTracks_);
            }
            onChanged();
          }
          if (!other.videoUnmuteTracks_.isEmpty()) {
            if (videoUnmuteTracks_.isEmpty()) {
              videoUnmuteTracks_ = other.videoUnmuteTracks_;
              bitField1_ |= 0x00000020;
            } else {
              ensureVideoUnmuteTracksIsMutable();
              videoUnmuteTracks_.addAll(other.videoUnmuteTracks_);
            }
            onChanged();
          }
          if (!other.getSource().isEmpty()) {
            source_ = other.source_;
            bitField1_ |= 0x00000040;
            onChanged();
          }
          if (other.getPrice() != 0) {
            setPrice(other.getPrice());
          }
          if (!other.getNurl().isEmpty()) {
            nurl_ = other.nurl_;
            bitField1_ |= 0x00000100;
            onChanged();
          }
          if (!other.getLurl().isEmpty()) {
            lurl_ = other.lurl_;
            bitField1_ |= 0x00000200;
            onChanged();
          }
          if (other.hasExt()) {
            mergeExt(other.getExt());
          }
          if (!other.getUniversalLink().isEmpty()) {
            universalLink_ = other.universalLink_;
            bitField1_ |= 0x00000800;
            onChanged();
          }
          if (!other.dpFailTracks_.isEmpty()) {
            if (dpFailTracks_.isEmpty()) {
              dpFailTracks_ = other.dpFailTracks_;
              bitField1_ |= 0x00001000;
            } else {
              ensureDpFailTracksIsMutable();
              dpFailTracks_.addAll(other.dpFailTracks_);
            }
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @Override
        public final boolean isInitialized() {
          return true;
        }

        @Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  id_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 10
                case 18: {
                  impid_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 18
                case 24: {
                  bidtype_ = input.readInt32();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                case 34: {
                  crid_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000008;
                  break;
                } // case 34
                case 50: {
                  String s = input.readStringRequireUtf8();
                  ensureDpTracksIsMutable();
                  dpTracks_.add(s);
                  break;
                } // case 50
                case 58: {
                  String s = input.readStringRequireUtf8();
                  ensureImpTracksIsMutable();
                  impTracks_.add(s);
                  break;
                } // case 58
                case 66: {
                  String s = input.readStringRequireUtf8();
                  ensureClickTracksIsMutable();
                  clickTracks_.add(s);
                  break;
                } // case 66
                case 74: {
                  String s = input.readStringRequireUtf8();
                  ensureDownStartTracksIsMutable();
                  downStartTracks_.add(s);
                  break;
                } // case 74
                case 82: {
                  String s = input.readStringRequireUtf8();
                  ensureDownCompleteTracksIsMutable();
                  downCompleteTracks_.add(s);
                  break;
                } // case 82
                case 98: {
                  String s = input.readStringRequireUtf8();
                  ensureInstallStartTracksIsMutable();
                  installStartTracks_.add(s);
                  break;
                } // case 98
                case 106: {
                  String s = input.readStringRequireUtf8();
                  ensureInstallCompleteTracksIsMutable();
                  installCompleteTracks_.add(s);
                  break;
                } // case 106
                case 114: {
                  appname_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000800;
                  break;
                } // case 114
                case 122: {
                  bundle_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00001000;
                  break;
                } // case 122
                case 130: {
                  input.readMessage(
                      getImageFieldBuilder().getBuilder(),
                      extensionRegistry);
                  bitField0_ |= 0x00002000;
                  break;
                } // case 130
                case 138: {
                  ResImage m =
                      input.readMessage(
                          ResImage.parser(),
                          extensionRegistry);
                  if (imagesBuilder_ == null) {
                    ensureImagesIsMutable();
                    images_.add(m);
                  } else {
                    imagesBuilder_.addMessage(m);
                  }
                  break;
                } // case 138
                case 146: {
                  input.readMessage(
                      getLogoFieldBuilder().getBuilder(),
                      extensionRegistry);
                  bitField0_ |= 0x00008000;
                  break;
                } // case 146
                case 154: {
                  input.readMessage(
                      getAppiconFieldBuilder().getBuilder(),
                      extensionRegistry);
                  bitField0_ |= 0x00010000;
                  break;
                } // case 154
                case 162: {
                  title_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00020000;
                  break;
                } // case 162
                case 170: {
                  des_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00040000;
                  break;
                } // case 170
                case 184: {
                  isDownload_ = input.readBool();
                  bitField0_ |= 0x00080000;
                  break;
                } // case 184
                case 192: {
                  isDeep_ = input.readBool();
                  bitField0_ |= 0x00100000;
                  break;
                } // case 192
                case 202: {
                  fileUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00200000;
                  break;
                } // case 202
                case 210: {
                  landingUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00400000;
                  break;
                } // case 210
                case 218: {
                  deeplink_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00800000;
                  break;
                } // case 218
                case 224: {
                  expirationTime_ = input.readInt64();
                  bitField0_ |= 0x01000000;
                  break;
                } // case 224
                case 234: {
                  input.readMessage(
                      getVideoFieldBuilder().getBuilder(),
                      extensionRegistry);
                  bitField0_ |= 0x02000000;
                  break;
                } // case 234
                case 242: {
                  String s = input.readStringRequireUtf8();
                  ensureVideoStartTracksIsMutable();
                  videoStartTracks_.add(s);
                  break;
                } // case 242
                case 250: {
                  String s = input.readStringRequireUtf8();
                  ensureVideoCompleteTracksIsMutable();
                  videoCompleteTracks_.add(s);
                  break;
                } // case 250
                case 258: {
                  String s = input.readStringRequireUtf8();
                  ensureAdCloseTracksIsMutable();
                  adCloseTracks_.add(s);
                  break;
                } // case 258
                case 264: {
                  macro_ = input.readInt32();
                  bitField0_ |= 0x20000000;
                  break;
                } // case 264
                case 274: {
                  Feedbacks m =
                      input.readMessage(
                          Feedbacks.parser(),
                          extensionRegistry);
                  if (feedbacksBuilder_ == null) {
                    ensureFeedbacksIsMutable();
                    feedbacks_.add(m);
                  } else {
                    feedbacksBuilder_.addMessage(m);
                  }
                  break;
                } // case 274
                case 282: {
                  String s = input.readStringRequireUtf8();
                  ensureVideoPlayFirstQuartileTracksIsMutable();
                  videoPlayFirstQuartileTracks_.add(s);
                  break;
                } // case 282
                case 290: {
                  String s = input.readStringRequireUtf8();
                  ensureVideoPlayMidpointTracksIsMutable();
                  videoPlayMidpointTracks_.add(s);
                  break;
                } // case 290
                case 298: {
                  String s = input.readStringRequireUtf8();
                  ensureVideoPlayThirdQuartileTracksIsMutable();
                  videoPlayThirdQuartileTracks_.add(s);
                  break;
                } // case 298
                case 306: {
                  String s = input.readStringRequireUtf8();
                  ensureVideoSkipTracksIsMutable();
                  videoSkipTracks_.add(s);
                  break;
                } // case 306
                case 314: {
                  String s = input.readStringRequireUtf8();
                  ensureVideoClickTracksIsMutable();
                  videoClickTracks_.add(s);
                  break;
                } // case 314
                case 322: {
                  String s = input.readStringRequireUtf8();
                  ensureVideoMuteTracksIsMutable();
                  videoMuteTracks_.add(s);
                  break;
                } // case 322
                case 330: {
                  String s = input.readStringRequireUtf8();
                  ensureVideoUnmuteTracksIsMutable();
                  videoUnmuteTracks_.add(s);
                  break;
                } // case 330
                case 338: {
                  source_ = input.readStringRequireUtf8();
                  bitField1_ |= 0x00000040;
                  break;
                } // case 338
                case 344: {
                  price_ = input.readInt32();
                  bitField1_ |= 0x00000080;
                  break;
                } // case 344
                case 354: {
                  nurl_ = input.readStringRequireUtf8();
                  bitField1_ |= 0x00000100;
                  break;
                } // case 354
                case 362: {
                  lurl_ = input.readStringRequireUtf8();
                  bitField1_ |= 0x00000200;
                  break;
                } // case 362
                case 370: {
                  input.readMessage(
                      getExtFieldBuilder().getBuilder(),
                      extensionRegistry);
                  bitField1_ |= 0x00000400;
                  break;
                } // case 370
                case 378: {
                  universalLink_ = input.readStringRequireUtf8();
                  bitField1_ |= 0x00000800;
                  break;
                } // case 378
                case 386: {
                  String s = input.readStringRequireUtf8();
                  ensureDpFailTracksIsMutable();
                  dpFailTracks_.add(s);
                  break;
                } // case 386
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;
        private int bitField1_;

        private Object id_ = "";
        /**
         * <code>string id = 1;</code>
         * @return The id.
         */
        public String getId() {
          Object ref = id_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            id_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string id = 1;</code>
         * @return The bytes for id.
         */
        public com.google.protobuf.ByteString
            getIdBytes() {
          Object ref = id_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            id_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string id = 1;</code>
         * @param value The id to set.
         * @return This builder for chaining.
         */
        public Builder setId(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          id_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>string id = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearId() {
          id_ = getDefaultInstance().getId();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
          return this;
        }
        /**
         * <code>string id = 1;</code>
         * @param value The bytes for id to set.
         * @return This builder for chaining.
         */
        public Builder setIdBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          id_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }

        private Object impid_ = "";
        /**
         * <code>string impid = 2;</code>
         * @return The impid.
         */
        public String getImpid() {
          Object ref = impid_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            impid_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string impid = 2;</code>
         * @return The bytes for impid.
         */
        public com.google.protobuf.ByteString
            getImpidBytes() {
          Object ref = impid_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            impid_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string impid = 2;</code>
         * @param value The impid to set.
         * @return This builder for chaining.
         */
        public Builder setImpid(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          impid_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>string impid = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearImpid() {
          impid_ = getDefaultInstance().getImpid();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
          return this;
        }
        /**
         * <code>string impid = 2;</code>
         * @param value The bytes for impid to set.
         * @return This builder for chaining.
         */
        public Builder setImpidBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          impid_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }

        private int bidtype_ ;
        /**
         * <code>int32 bidtype = 3;</code>
         * @return The bidtype.
         */
        @Override
        public int getBidtype() {
          return bidtype_;
        }
        /**
         * <code>int32 bidtype = 3;</code>
         * @param value The bidtype to set.
         * @return This builder for chaining.
         */
        public Builder setBidtype(int value) {

          bidtype_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>int32 bidtype = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearBidtype() {
          bitField0_ = (bitField0_ & ~0x00000004);
          bidtype_ = 0;
          onChanged();
          return this;
        }

        private Object crid_ = "";
        /**
         * <code>string crid = 4;</code>
         * @return The crid.
         */
        public String getCrid() {
          Object ref = crid_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            crid_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string crid = 4;</code>
         * @return The bytes for crid.
         */
        public com.google.protobuf.ByteString
            getCridBytes() {
          Object ref = crid_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            crid_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string crid = 4;</code>
         * @param value The crid to set.
         * @return This builder for chaining.
         */
        public Builder setCrid(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          crid_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <code>string crid = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearCrid() {
          crid_ = getDefaultInstance().getCrid();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
          return this;
        }
        /**
         * <code>string crid = 4;</code>
         * @param value The bytes for crid to set.
         * @return This builder for chaining.
         */
        public Builder setCridBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          crid_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList dpTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureDpTracksIsMutable() {
          if (!dpTracks_.isModifiable()) {
            dpTracks_ = new com.google.protobuf.LazyStringArrayList(dpTracks_);
          }
          bitField0_ |= 0x00000010;
        }
        /**
         * <code>repeated string dp_tracks = 6;</code>
         * @return A list containing the dpTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getDpTracksList() {
          dpTracks_.makeImmutable();
          return dpTracks_;
        }
        /**
         * <code>repeated string dp_tracks = 6;</code>
         * @return The count of dpTracks.
         */
        public int getDpTracksCount() {
          return dpTracks_.size();
        }
        /**
         * <code>repeated string dp_tracks = 6;</code>
         * @param index The index of the element to return.
         * @return The dpTracks at the given index.
         */
        public String getDpTracks(int index) {
          return dpTracks_.get(index);
        }
        /**
         * <code>repeated string dp_tracks = 6;</code>
         * @param index The index of the value to return.
         * @return The bytes of the dpTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getDpTracksBytes(int index) {
          return dpTracks_.getByteString(index);
        }
        /**
         * <code>repeated string dp_tracks = 6;</code>
         * @param index The index to set the value at.
         * @param value The dpTracks to set.
         * @return This builder for chaining.
         */
        public Builder setDpTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureDpTracksIsMutable();
          dpTracks_.set(index, value);
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string dp_tracks = 6;</code>
         * @param value The dpTracks to add.
         * @return This builder for chaining.
         */
        public Builder addDpTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureDpTracksIsMutable();
          dpTracks_.add(value);
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string dp_tracks = 6;</code>
         * @param values The dpTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllDpTracks(
            Iterable<String> values) {
          ensureDpTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dpTracks_);
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string dp_tracks = 6;</code>
         * @return This builder for chaining.
         */
        public Builder clearDpTracks() {
          dpTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string dp_tracks = 6;</code>
         * @param value The bytes of the dpTracks to add.
         * @return This builder for chaining.
         */
        public Builder addDpTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureDpTracksIsMutable();
          dpTracks_.add(value);
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList impTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureImpTracksIsMutable() {
          if (!impTracks_.isModifiable()) {
            impTracks_ = new com.google.protobuf.LazyStringArrayList(impTracks_);
          }
          bitField0_ |= 0x00000020;
        }
        /**
         * <code>repeated string imp_tracks = 7;</code>
         * @return A list containing the impTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getImpTracksList() {
          impTracks_.makeImmutable();
          return impTracks_;
        }
        /**
         * <code>repeated string imp_tracks = 7;</code>
         * @return The count of impTracks.
         */
        public int getImpTracksCount() {
          return impTracks_.size();
        }
        /**
         * <code>repeated string imp_tracks = 7;</code>
         * @param index The index of the element to return.
         * @return The impTracks at the given index.
         */
        public String getImpTracks(int index) {
          return impTracks_.get(index);
        }
        /**
         * <code>repeated string imp_tracks = 7;</code>
         * @param index The index of the value to return.
         * @return The bytes of the impTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getImpTracksBytes(int index) {
          return impTracks_.getByteString(index);
        }
        /**
         * <code>repeated string imp_tracks = 7;</code>
         * @param index The index to set the value at.
         * @param value The impTracks to set.
         * @return This builder for chaining.
         */
        public Builder setImpTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureImpTracksIsMutable();
          impTracks_.set(index, value);
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string imp_tracks = 7;</code>
         * @param value The impTracks to add.
         * @return This builder for chaining.
         */
        public Builder addImpTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureImpTracksIsMutable();
          impTracks_.add(value);
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string imp_tracks = 7;</code>
         * @param values The impTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllImpTracks(
            Iterable<String> values) {
          ensureImpTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, impTracks_);
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string imp_tracks = 7;</code>
         * @return This builder for chaining.
         */
        public Builder clearImpTracks() {
          impTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string imp_tracks = 7;</code>
         * @param value The bytes of the impTracks to add.
         * @return This builder for chaining.
         */
        public Builder addImpTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureImpTracksIsMutable();
          impTracks_.add(value);
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList clickTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureClickTracksIsMutable() {
          if (!clickTracks_.isModifiable()) {
            clickTracks_ = new com.google.protobuf.LazyStringArrayList(clickTracks_);
          }
          bitField0_ |= 0x00000040;
        }
        /**
         * <code>repeated string click_tracks = 8;</code>
         * @return A list containing the clickTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getClickTracksList() {
          clickTracks_.makeImmutable();
          return clickTracks_;
        }
        /**
         * <code>repeated string click_tracks = 8;</code>
         * @return The count of clickTracks.
         */
        public int getClickTracksCount() {
          return clickTracks_.size();
        }
        /**
         * <code>repeated string click_tracks = 8;</code>
         * @param index The index of the element to return.
         * @return The clickTracks at the given index.
         */
        public String getClickTracks(int index) {
          return clickTracks_.get(index);
        }
        /**
         * <code>repeated string click_tracks = 8;</code>
         * @param index The index of the value to return.
         * @return The bytes of the clickTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getClickTracksBytes(int index) {
          return clickTracks_.getByteString(index);
        }
        /**
         * <code>repeated string click_tracks = 8;</code>
         * @param index The index to set the value at.
         * @param value The clickTracks to set.
         * @return This builder for chaining.
         */
        public Builder setClickTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureClickTracksIsMutable();
          clickTracks_.set(index, value);
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string click_tracks = 8;</code>
         * @param value The clickTracks to add.
         * @return This builder for chaining.
         */
        public Builder addClickTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureClickTracksIsMutable();
          clickTracks_.add(value);
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string click_tracks = 8;</code>
         * @param values The clickTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllClickTracks(
            Iterable<String> values) {
          ensureClickTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, clickTracks_);
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string click_tracks = 8;</code>
         * @return This builder for chaining.
         */
        public Builder clearClickTracks() {
          clickTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string click_tracks = 8;</code>
         * @param value The bytes of the clickTracks to add.
         * @return This builder for chaining.
         */
        public Builder addClickTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureClickTracksIsMutable();
          clickTracks_.add(value);
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList downStartTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureDownStartTracksIsMutable() {
          if (!downStartTracks_.isModifiable()) {
            downStartTracks_ = new com.google.protobuf.LazyStringArrayList(downStartTracks_);
          }
          bitField0_ |= 0x00000080;
        }
        /**
         * <code>repeated string down_start_tracks = 9;</code>
         * @return A list containing the downStartTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getDownStartTracksList() {
          downStartTracks_.makeImmutable();
          return downStartTracks_;
        }
        /**
         * <code>repeated string down_start_tracks = 9;</code>
         * @return The count of downStartTracks.
         */
        public int getDownStartTracksCount() {
          return downStartTracks_.size();
        }
        /**
         * <code>repeated string down_start_tracks = 9;</code>
         * @param index The index of the element to return.
         * @return The downStartTracks at the given index.
         */
        public String getDownStartTracks(int index) {
          return downStartTracks_.get(index);
        }
        /**
         * <code>repeated string down_start_tracks = 9;</code>
         * @param index The index of the value to return.
         * @return The bytes of the downStartTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getDownStartTracksBytes(int index) {
          return downStartTracks_.getByteString(index);
        }
        /**
         * <code>repeated string down_start_tracks = 9;</code>
         * @param index The index to set the value at.
         * @param value The downStartTracks to set.
         * @return This builder for chaining.
         */
        public Builder setDownStartTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureDownStartTracksIsMutable();
          downStartTracks_.set(index, value);
          bitField0_ |= 0x00000080;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string down_start_tracks = 9;</code>
         * @param value The downStartTracks to add.
         * @return This builder for chaining.
         */
        public Builder addDownStartTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureDownStartTracksIsMutable();
          downStartTracks_.add(value);
          bitField0_ |= 0x00000080;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string down_start_tracks = 9;</code>
         * @param values The downStartTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllDownStartTracks(
            Iterable<String> values) {
          ensureDownStartTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, downStartTracks_);
          bitField0_ |= 0x00000080;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string down_start_tracks = 9;</code>
         * @return This builder for chaining.
         */
        public Builder clearDownStartTracks() {
          downStartTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000080);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string down_start_tracks = 9;</code>
         * @param value The bytes of the downStartTracks to add.
         * @return This builder for chaining.
         */
        public Builder addDownStartTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureDownStartTracksIsMutable();
          downStartTracks_.add(value);
          bitField0_ |= 0x00000080;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList downCompleteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureDownCompleteTracksIsMutable() {
          if (!downCompleteTracks_.isModifiable()) {
            downCompleteTracks_ = new com.google.protobuf.LazyStringArrayList(downCompleteTracks_);
          }
          bitField0_ |= 0x00000100;
        }
        /**
         * <code>repeated string down_complete_tracks = 10;</code>
         * @return A list containing the downCompleteTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getDownCompleteTracksList() {
          downCompleteTracks_.makeImmutable();
          return downCompleteTracks_;
        }
        /**
         * <code>repeated string down_complete_tracks = 10;</code>
         * @return The count of downCompleteTracks.
         */
        public int getDownCompleteTracksCount() {
          return downCompleteTracks_.size();
        }
        /**
         * <code>repeated string down_complete_tracks = 10;</code>
         * @param index The index of the element to return.
         * @return The downCompleteTracks at the given index.
         */
        public String getDownCompleteTracks(int index) {
          return downCompleteTracks_.get(index);
        }
        /**
         * <code>repeated string down_complete_tracks = 10;</code>
         * @param index The index of the value to return.
         * @return The bytes of the downCompleteTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getDownCompleteTracksBytes(int index) {
          return downCompleteTracks_.getByteString(index);
        }
        /**
         * <code>repeated string down_complete_tracks = 10;</code>
         * @param index The index to set the value at.
         * @param value The downCompleteTracks to set.
         * @return This builder for chaining.
         */
        public Builder setDownCompleteTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureDownCompleteTracksIsMutable();
          downCompleteTracks_.set(index, value);
          bitField0_ |= 0x00000100;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string down_complete_tracks = 10;</code>
         * @param value The downCompleteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addDownCompleteTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureDownCompleteTracksIsMutable();
          downCompleteTracks_.add(value);
          bitField0_ |= 0x00000100;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string down_complete_tracks = 10;</code>
         * @param values The downCompleteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllDownCompleteTracks(
            Iterable<String> values) {
          ensureDownCompleteTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, downCompleteTracks_);
          bitField0_ |= 0x00000100;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string down_complete_tracks = 10;</code>
         * @return This builder for chaining.
         */
        public Builder clearDownCompleteTracks() {
          downCompleteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000100);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string down_complete_tracks = 10;</code>
         * @param value The bytes of the downCompleteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addDownCompleteTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureDownCompleteTracksIsMutable();
          downCompleteTracks_.add(value);
          bitField0_ |= 0x00000100;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList installStartTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureInstallStartTracksIsMutable() {
          if (!installStartTracks_.isModifiable()) {
            installStartTracks_ = new com.google.protobuf.LazyStringArrayList(installStartTracks_);
          }
          bitField0_ |= 0x00000200;
        }
        /**
         * <code>repeated string install_start_tracks = 12;</code>
         * @return A list containing the installStartTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getInstallStartTracksList() {
          installStartTracks_.makeImmutable();
          return installStartTracks_;
        }
        /**
         * <code>repeated string install_start_tracks = 12;</code>
         * @return The count of installStartTracks.
         */
        public int getInstallStartTracksCount() {
          return installStartTracks_.size();
        }
        /**
         * <code>repeated string install_start_tracks = 12;</code>
         * @param index The index of the element to return.
         * @return The installStartTracks at the given index.
         */
        public String getInstallStartTracks(int index) {
          return installStartTracks_.get(index);
        }
        /**
         * <code>repeated string install_start_tracks = 12;</code>
         * @param index The index of the value to return.
         * @return The bytes of the installStartTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getInstallStartTracksBytes(int index) {
          return installStartTracks_.getByteString(index);
        }
        /**
         * <code>repeated string install_start_tracks = 12;</code>
         * @param index The index to set the value at.
         * @param value The installStartTracks to set.
         * @return This builder for chaining.
         */
        public Builder setInstallStartTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureInstallStartTracksIsMutable();
          installStartTracks_.set(index, value);
          bitField0_ |= 0x00000200;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string install_start_tracks = 12;</code>
         * @param value The installStartTracks to add.
         * @return This builder for chaining.
         */
        public Builder addInstallStartTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureInstallStartTracksIsMutable();
          installStartTracks_.add(value);
          bitField0_ |= 0x00000200;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string install_start_tracks = 12;</code>
         * @param values The installStartTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllInstallStartTracks(
            Iterable<String> values) {
          ensureInstallStartTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, installStartTracks_);
          bitField0_ |= 0x00000200;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string install_start_tracks = 12;</code>
         * @return This builder for chaining.
         */
        public Builder clearInstallStartTracks() {
          installStartTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000200);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string install_start_tracks = 12;</code>
         * @param value The bytes of the installStartTracks to add.
         * @return This builder for chaining.
         */
        public Builder addInstallStartTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureInstallStartTracksIsMutable();
          installStartTracks_.add(value);
          bitField0_ |= 0x00000200;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList installCompleteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureInstallCompleteTracksIsMutable() {
          if (!installCompleteTracks_.isModifiable()) {
            installCompleteTracks_ = new com.google.protobuf.LazyStringArrayList(installCompleteTracks_);
          }
          bitField0_ |= 0x00000400;
        }
        /**
         * <code>repeated string install_complete_tracks = 13;</code>
         * @return A list containing the installCompleteTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getInstallCompleteTracksList() {
          installCompleteTracks_.makeImmutable();
          return installCompleteTracks_;
        }
        /**
         * <code>repeated string install_complete_tracks = 13;</code>
         * @return The count of installCompleteTracks.
         */
        public int getInstallCompleteTracksCount() {
          return installCompleteTracks_.size();
        }
        /**
         * <code>repeated string install_complete_tracks = 13;</code>
         * @param index The index of the element to return.
         * @return The installCompleteTracks at the given index.
         */
        public String getInstallCompleteTracks(int index) {
          return installCompleteTracks_.get(index);
        }
        /**
         * <code>repeated string install_complete_tracks = 13;</code>
         * @param index The index of the value to return.
         * @return The bytes of the installCompleteTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getInstallCompleteTracksBytes(int index) {
          return installCompleteTracks_.getByteString(index);
        }
        /**
         * <code>repeated string install_complete_tracks = 13;</code>
         * @param index The index to set the value at.
         * @param value The installCompleteTracks to set.
         * @return This builder for chaining.
         */
        public Builder setInstallCompleteTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureInstallCompleteTracksIsMutable();
          installCompleteTracks_.set(index, value);
          bitField0_ |= 0x00000400;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string install_complete_tracks = 13;</code>
         * @param value The installCompleteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addInstallCompleteTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureInstallCompleteTracksIsMutable();
          installCompleteTracks_.add(value);
          bitField0_ |= 0x00000400;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string install_complete_tracks = 13;</code>
         * @param values The installCompleteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllInstallCompleteTracks(
            Iterable<String> values) {
          ensureInstallCompleteTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, installCompleteTracks_);
          bitField0_ |= 0x00000400;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string install_complete_tracks = 13;</code>
         * @return This builder for chaining.
         */
        public Builder clearInstallCompleteTracks() {
          installCompleteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000400);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string install_complete_tracks = 13;</code>
         * @param value The bytes of the installCompleteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addInstallCompleteTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureInstallCompleteTracksIsMutable();
          installCompleteTracks_.add(value);
          bitField0_ |= 0x00000400;
          onChanged();
          return this;
        }

        private Object appname_ = "";
        /**
         * <code>string appname = 14;</code>
         * @return The appname.
         */
        public String getAppname() {
          Object ref = appname_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            appname_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string appname = 14;</code>
         * @return The bytes for appname.
         */
        public com.google.protobuf.ByteString
            getAppnameBytes() {
          Object ref = appname_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            appname_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string appname = 14;</code>
         * @param value The appname to set.
         * @return This builder for chaining.
         */
        public Builder setAppname(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          appname_ = value;
          bitField0_ |= 0x00000800;
          onChanged();
          return this;
        }
        /**
         * <code>string appname = 14;</code>
         * @return This builder for chaining.
         */
        public Builder clearAppname() {
          appname_ = getDefaultInstance().getAppname();
          bitField0_ = (bitField0_ & ~0x00000800);
          onChanged();
          return this;
        }
        /**
         * <code>string appname = 14;</code>
         * @param value The bytes for appname to set.
         * @return This builder for chaining.
         */
        public Builder setAppnameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          appname_ = value;
          bitField0_ |= 0x00000800;
          onChanged();
          return this;
        }

        private Object bundle_ = "";
        /**
         * <code>string bundle = 15;</code>
         * @return The bundle.
         */
        public String getBundle() {
          Object ref = bundle_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            bundle_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string bundle = 15;</code>
         * @return The bytes for bundle.
         */
        public com.google.protobuf.ByteString
            getBundleBytes() {
          Object ref = bundle_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            bundle_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string bundle = 15;</code>
         * @param value The bundle to set.
         * @return This builder for chaining.
         */
        public Builder setBundle(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          bundle_ = value;
          bitField0_ |= 0x00001000;
          onChanged();
          return this;
        }
        /**
         * <code>string bundle = 15;</code>
         * @return This builder for chaining.
         */
        public Builder clearBundle() {
          bundle_ = getDefaultInstance().getBundle();
          bitField0_ = (bitField0_ & ~0x00001000);
          onChanged();
          return this;
        }
        /**
         * <code>string bundle = 15;</code>
         * @param value The bytes for bundle to set.
         * @return This builder for chaining.
         */
        public Builder setBundleBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          bundle_ = value;
          bitField0_ |= 0x00001000;
          onChanged();
          return this;
        }

        private ResImage image_;
        private com.google.protobuf.SingleFieldBuilder<
            ResImage, ResImage.Builder, ResImageOrBuilder> imageBuilder_;
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
         * @return Whether the image field is set.
         */
        public boolean hasImage() {
          return ((bitField0_ & 0x00002000) != 0);
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
         * @return The image.
         */
        public ResImage getImage() {
          if (imageBuilder_ == null) {
            return image_ == null ? ResImage.getDefaultInstance() : image_;
          } else {
            return imageBuilder_.getMessage();
          }
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
         */
        public Builder setImage(ResImage value) {
          if (imageBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            image_ = value;
          } else {
            imageBuilder_.setMessage(value);
          }
          bitField0_ |= 0x00002000;
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
         */
        public Builder setImage(
            ResImage.Builder builderForValue) {
          if (imageBuilder_ == null) {
            image_ = builderForValue.build();
          } else {
            imageBuilder_.setMessage(builderForValue.build());
          }
          bitField0_ |= 0x00002000;
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
         */
        public Builder mergeImage(ResImage value) {
          if (imageBuilder_ == null) {
            if (((bitField0_ & 0x00002000) != 0) &&
              image_ != null &&
              image_ != ResImage.getDefaultInstance()) {
              getImageBuilder().mergeFrom(value);
            } else {
              image_ = value;
            }
          } else {
            imageBuilder_.mergeFrom(value);
          }
          if (image_ != null) {
            bitField0_ |= 0x00002000;
            onChanged();
          }
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
         */
        public Builder clearImage() {
          bitField0_ = (bitField0_ & ~0x00002000);
          image_ = null;
          if (imageBuilder_ != null) {
            imageBuilder_.dispose();
            imageBuilder_ = null;
          }
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
         */
        public ResImage.Builder getImageBuilder() {
          bitField0_ |= 0x00002000;
          onChanged();
          return getImageFieldBuilder().getBuilder();
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
         */
        public ResImageOrBuilder getImageOrBuilder() {
          if (imageBuilder_ != null) {
            return imageBuilder_.getMessageOrBuilder();
          } else {
            return image_ == null ?
                ResImage.getDefaultInstance() : image_;
          }
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage image = 16;</code>
         */
        private com.google.protobuf.SingleFieldBuilder<
            ResImage, ResImage.Builder, ResImageOrBuilder>
            getImageFieldBuilder() {
          if (imageBuilder_ == null) {
            imageBuilder_ = new com.google.protobuf.SingleFieldBuilder<
                ResImage, ResImage.Builder, ResImageOrBuilder>(
                    getImage(),
                    getParentForChildren(),
                    isClean());
            image_ = null;
          }
          return imageBuilder_;
        }

        private java.util.List<ResImage> images_ =
          java.util.Collections.emptyList();
        private void ensureImagesIsMutable() {
          if (!((bitField0_ & 0x00004000) != 0)) {
            images_ = new java.util.ArrayList<ResImage>(images_);
            bitField0_ |= 0x00004000;
           }
        }

        private com.google.protobuf.RepeatedFieldBuilder<
            ResImage, ResImage.Builder, ResImageOrBuilder> imagesBuilder_;

        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public java.util.List<ResImage> getImagesList() {
          if (imagesBuilder_ == null) {
            return java.util.Collections.unmodifiableList(images_);
          } else {
            return imagesBuilder_.getMessageList();
          }
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public int getImagesCount() {
          if (imagesBuilder_ == null) {
            return images_.size();
          } else {
            return imagesBuilder_.getCount();
          }
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public ResImage getImages(int index) {
          if (imagesBuilder_ == null) {
            return images_.get(index);
          } else {
            return imagesBuilder_.getMessage(index);
          }
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public Builder setImages(
            int index, ResImage value) {
          if (imagesBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureImagesIsMutable();
            images_.set(index, value);
            onChanged();
          } else {
            imagesBuilder_.setMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public Builder setImages(
            int index, ResImage.Builder builderForValue) {
          if (imagesBuilder_ == null) {
            ensureImagesIsMutable();
            images_.set(index, builderForValue.build());
            onChanged();
          } else {
            imagesBuilder_.setMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public Builder addImages(ResImage value) {
          if (imagesBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureImagesIsMutable();
            images_.add(value);
            onChanged();
          } else {
            imagesBuilder_.addMessage(value);
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public Builder addImages(
            int index, ResImage value) {
          if (imagesBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureImagesIsMutable();
            images_.add(index, value);
            onChanged();
          } else {
            imagesBuilder_.addMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public Builder addImages(
            ResImage.Builder builderForValue) {
          if (imagesBuilder_ == null) {
            ensureImagesIsMutable();
            images_.add(builderForValue.build());
            onChanged();
          } else {
            imagesBuilder_.addMessage(builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public Builder addImages(
            int index, ResImage.Builder builderForValue) {
          if (imagesBuilder_ == null) {
            ensureImagesIsMutable();
            images_.add(index, builderForValue.build());
            onChanged();
          } else {
            imagesBuilder_.addMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public Builder addAllImages(
            Iterable<? extends ResImage> values) {
          if (imagesBuilder_ == null) {
            ensureImagesIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, images_);
            onChanged();
          } else {
            imagesBuilder_.addAllMessages(values);
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public Builder clearImages() {
          if (imagesBuilder_ == null) {
            images_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00004000);
            onChanged();
          } else {
            imagesBuilder_.clear();
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public Builder removeImages(int index) {
          if (imagesBuilder_ == null) {
            ensureImagesIsMutable();
            images_.remove(index);
            onChanged();
          } else {
            imagesBuilder_.remove(index);
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public ResImage.Builder getImagesBuilder(
            int index) {
          return getImagesFieldBuilder().getBuilder(index);
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public ResImageOrBuilder getImagesOrBuilder(
            int index) {
          if (imagesBuilder_ == null) {
            return images_.get(index);  } else {
            return imagesBuilder_.getMessageOrBuilder(index);
          }
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public java.util.List<? extends ResImageOrBuilder>
             getImagesOrBuilderList() {
          if (imagesBuilder_ != null) {
            return imagesBuilder_.getMessageOrBuilderList();
          } else {
            return java.util.Collections.unmodifiableList(images_);
          }
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public ResImage.Builder addImagesBuilder() {
          return getImagesFieldBuilder().addBuilder(
              ResImage.getDefaultInstance());
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public ResImage.Builder addImagesBuilder(
            int index) {
          return getImagesFieldBuilder().addBuilder(
              index, ResImage.getDefaultInstance());
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.ResImage images = 17;</code>
         */
        public java.util.List<ResImage.Builder>
             getImagesBuilderList() {
          return getImagesFieldBuilder().getBuilderList();
        }
        private com.google.protobuf.RepeatedFieldBuilder<
            ResImage, ResImage.Builder, ResImageOrBuilder>
            getImagesFieldBuilder() {
          if (imagesBuilder_ == null) {
            imagesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
                ResImage, ResImage.Builder, ResImageOrBuilder>(
                    images_,
                    ((bitField0_ & 0x00004000) != 0),
                    getParentForChildren(),
                    isClean());
            images_ = null;
          }
          return imagesBuilder_;
        }

        private ResImage logo_;
        private com.google.protobuf.SingleFieldBuilder<
            ResImage, ResImage.Builder, ResImageOrBuilder> logoBuilder_;
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
         * @return Whether the logo field is set.
         */
        public boolean hasLogo() {
          return ((bitField0_ & 0x00008000) != 0);
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
         * @return The logo.
         */
        public ResImage getLogo() {
          if (logoBuilder_ == null) {
            return logo_ == null ? ResImage.getDefaultInstance() : logo_;
          } else {
            return logoBuilder_.getMessage();
          }
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
         */
        public Builder setLogo(ResImage value) {
          if (logoBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            logo_ = value;
          } else {
            logoBuilder_.setMessage(value);
          }
          bitField0_ |= 0x00008000;
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
         */
        public Builder setLogo(
            ResImage.Builder builderForValue) {
          if (logoBuilder_ == null) {
            logo_ = builderForValue.build();
          } else {
            logoBuilder_.setMessage(builderForValue.build());
          }
          bitField0_ |= 0x00008000;
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
         */
        public Builder mergeLogo(ResImage value) {
          if (logoBuilder_ == null) {
            if (((bitField0_ & 0x00008000) != 0) &&
              logo_ != null &&
              logo_ != ResImage.getDefaultInstance()) {
              getLogoBuilder().mergeFrom(value);
            } else {
              logo_ = value;
            }
          } else {
            logoBuilder_.mergeFrom(value);
          }
          if (logo_ != null) {
            bitField0_ |= 0x00008000;
            onChanged();
          }
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
         */
        public Builder clearLogo() {
          bitField0_ = (bitField0_ & ~0x00008000);
          logo_ = null;
          if (logoBuilder_ != null) {
            logoBuilder_.dispose();
            logoBuilder_ = null;
          }
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
         */
        public ResImage.Builder getLogoBuilder() {
          bitField0_ |= 0x00008000;
          onChanged();
          return getLogoFieldBuilder().getBuilder();
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
         */
        public ResImageOrBuilder getLogoOrBuilder() {
          if (logoBuilder_ != null) {
            return logoBuilder_.getMessageOrBuilder();
          } else {
            return logo_ == null ?
                ResImage.getDefaultInstance() : logo_;
          }
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage logo = 18;</code>
         */
        private com.google.protobuf.SingleFieldBuilder<
            ResImage, ResImage.Builder, ResImageOrBuilder>
            getLogoFieldBuilder() {
          if (logoBuilder_ == null) {
            logoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
                ResImage, ResImage.Builder, ResImageOrBuilder>(
                    getLogo(),
                    getParentForChildren(),
                    isClean());
            logo_ = null;
          }
          return logoBuilder_;
        }

        private ResImage appicon_;
        private com.google.protobuf.SingleFieldBuilder<
            ResImage, ResImage.Builder, ResImageOrBuilder> appiconBuilder_;
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
         * @return Whether the appicon field is set.
         */
        public boolean hasAppicon() {
          return ((bitField0_ & 0x00010000) != 0);
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
         * @return The appicon.
         */
        public ResImage getAppicon() {
          if (appiconBuilder_ == null) {
            return appicon_ == null ? ResImage.getDefaultInstance() : appicon_;
          } else {
            return appiconBuilder_.getMessage();
          }
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
         */
        public Builder setAppicon(ResImage value) {
          if (appiconBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            appicon_ = value;
          } else {
            appiconBuilder_.setMessage(value);
          }
          bitField0_ |= 0x00010000;
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
         */
        public Builder setAppicon(
            ResImage.Builder builderForValue) {
          if (appiconBuilder_ == null) {
            appicon_ = builderForValue.build();
          } else {
            appiconBuilder_.setMessage(builderForValue.build());
          }
          bitField0_ |= 0x00010000;
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
         */
        public Builder mergeAppicon(ResImage value) {
          if (appiconBuilder_ == null) {
            if (((bitField0_ & 0x00010000) != 0) &&
              appicon_ != null &&
              appicon_ != ResImage.getDefaultInstance()) {
              getAppiconBuilder().mergeFrom(value);
            } else {
              appicon_ = value;
            }
          } else {
            appiconBuilder_.mergeFrom(value);
          }
          if (appicon_ != null) {
            bitField0_ |= 0x00010000;
            onChanged();
          }
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
         */
        public Builder clearAppicon() {
          bitField0_ = (bitField0_ & ~0x00010000);
          appicon_ = null;
          if (appiconBuilder_ != null) {
            appiconBuilder_.dispose();
            appiconBuilder_ = null;
          }
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
         */
        public ResImage.Builder getAppiconBuilder() {
          bitField0_ |= 0x00010000;
          onChanged();
          return getAppiconFieldBuilder().getBuilder();
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
         */
        public ResImageOrBuilder getAppiconOrBuilder() {
          if (appiconBuilder_ != null) {
            return appiconBuilder_.getMessageOrBuilder();
          } else {
            return appicon_ == null ?
                ResImage.getDefaultInstance() : appicon_;
          }
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResImage appicon = 19;</code>
         */
        private com.google.protobuf.SingleFieldBuilder<
            ResImage, ResImage.Builder, ResImageOrBuilder>
            getAppiconFieldBuilder() {
          if (appiconBuilder_ == null) {
            appiconBuilder_ = new com.google.protobuf.SingleFieldBuilder<
                ResImage, ResImage.Builder, ResImageOrBuilder>(
                    getAppicon(),
                    getParentForChildren(),
                    isClean());
            appicon_ = null;
          }
          return appiconBuilder_;
        }

        private Object title_ = "";
        /**
         * <code>string title = 20;</code>
         * @return The title.
         */
        public String getTitle() {
          Object ref = title_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            title_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string title = 20;</code>
         * @return The bytes for title.
         */
        public com.google.protobuf.ByteString
            getTitleBytes() {
          Object ref = title_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            title_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string title = 20;</code>
         * @param value The title to set.
         * @return This builder for chaining.
         */
        public Builder setTitle(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          title_ = value;
          bitField0_ |= 0x00020000;
          onChanged();
          return this;
        }
        /**
         * <code>string title = 20;</code>
         * @return This builder for chaining.
         */
        public Builder clearTitle() {
          title_ = getDefaultInstance().getTitle();
          bitField0_ = (bitField0_ & ~0x00020000);
          onChanged();
          return this;
        }
        /**
         * <code>string title = 20;</code>
         * @param value The bytes for title to set.
         * @return This builder for chaining.
         */
        public Builder setTitleBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          title_ = value;
          bitField0_ |= 0x00020000;
          onChanged();
          return this;
        }

        private Object des_ = "";
        /**
         * <code>string des = 21;</code>
         * @return The des.
         */
        public String getDes() {
          Object ref = des_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            des_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string des = 21;</code>
         * @return The bytes for des.
         */
        public com.google.protobuf.ByteString
            getDesBytes() {
          Object ref = des_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            des_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string des = 21;</code>
         * @param value The des to set.
         * @return This builder for chaining.
         */
        public Builder setDes(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          des_ = value;
          bitField0_ |= 0x00040000;
          onChanged();
          return this;
        }
        /**
         * <code>string des = 21;</code>
         * @return This builder for chaining.
         */
        public Builder clearDes() {
          des_ = getDefaultInstance().getDes();
          bitField0_ = (bitField0_ & ~0x00040000);
          onChanged();
          return this;
        }
        /**
         * <code>string des = 21;</code>
         * @param value The bytes for des to set.
         * @return This builder for chaining.
         */
        public Builder setDesBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          des_ = value;
          bitField0_ |= 0x00040000;
          onChanged();
          return this;
        }

        private boolean isDownload_ ;
        /**
         * <code>bool is_download = 23;</code>
         * @return The isDownload.
         */
        @Override
        public boolean getIsDownload() {
          return isDownload_;
        }
        /**
         * <code>bool is_download = 23;</code>
         * @param value The isDownload to set.
         * @return This builder for chaining.
         */
        public Builder setIsDownload(boolean value) {

          isDownload_ = value;
          bitField0_ |= 0x00080000;
          onChanged();
          return this;
        }
        /**
         * <code>bool is_download = 23;</code>
         * @return This builder for chaining.
         */
        public Builder clearIsDownload() {
          bitField0_ = (bitField0_ & ~0x00080000);
          isDownload_ = false;
          onChanged();
          return this;
        }

        private boolean isDeep_ ;
        /**
         * <code>bool is_deep = 24;</code>
         * @return The isDeep.
         */
        @Override
        public boolean getIsDeep() {
          return isDeep_;
        }
        /**
         * <code>bool is_deep = 24;</code>
         * @param value The isDeep to set.
         * @return This builder for chaining.
         */
        public Builder setIsDeep(boolean value) {

          isDeep_ = value;
          bitField0_ |= 0x00100000;
          onChanged();
          return this;
        }
        /**
         * <code>bool is_deep = 24;</code>
         * @return This builder for chaining.
         */
        public Builder clearIsDeep() {
          bitField0_ = (bitField0_ & ~0x00100000);
          isDeep_ = false;
          onChanged();
          return this;
        }

        private Object fileUrl_ = "";
        /**
         * <code>string file_url = 25;</code>
         * @return The fileUrl.
         */
        public String getFileUrl() {
          Object ref = fileUrl_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            fileUrl_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string file_url = 25;</code>
         * @return The bytes for fileUrl.
         */
        public com.google.protobuf.ByteString
            getFileUrlBytes() {
          Object ref = fileUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            fileUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string file_url = 25;</code>
         * @param value The fileUrl to set.
         * @return This builder for chaining.
         */
        public Builder setFileUrl(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          fileUrl_ = value;
          bitField0_ |= 0x00200000;
          onChanged();
          return this;
        }
        /**
         * <code>string file_url = 25;</code>
         * @return This builder for chaining.
         */
        public Builder clearFileUrl() {
          fileUrl_ = getDefaultInstance().getFileUrl();
          bitField0_ = (bitField0_ & ~0x00200000);
          onChanged();
          return this;
        }
        /**
         * <code>string file_url = 25;</code>
         * @param value The bytes for fileUrl to set.
         * @return This builder for chaining.
         */
        public Builder setFileUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          fileUrl_ = value;
          bitField0_ |= 0x00200000;
          onChanged();
          return this;
        }

        private Object landingUrl_ = "";
        /**
         * <code>string landing_url = 26;</code>
         * @return The landingUrl.
         */
        public String getLandingUrl() {
          Object ref = landingUrl_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            landingUrl_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string landing_url = 26;</code>
         * @return The bytes for landingUrl.
         */
        public com.google.protobuf.ByteString
            getLandingUrlBytes() {
          Object ref = landingUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            landingUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string landing_url = 26;</code>
         * @param value The landingUrl to set.
         * @return This builder for chaining.
         */
        public Builder setLandingUrl(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          landingUrl_ = value;
          bitField0_ |= 0x00400000;
          onChanged();
          return this;
        }
        /**
         * <code>string landing_url = 26;</code>
         * @return This builder for chaining.
         */
        public Builder clearLandingUrl() {
          landingUrl_ = getDefaultInstance().getLandingUrl();
          bitField0_ = (bitField0_ & ~0x00400000);
          onChanged();
          return this;
        }
        /**
         * <code>string landing_url = 26;</code>
         * @param value The bytes for landingUrl to set.
         * @return This builder for chaining.
         */
        public Builder setLandingUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          landingUrl_ = value;
          bitField0_ |= 0x00400000;
          onChanged();
          return this;
        }

        private Object deeplink_ = "";
        /**
         * <code>string deeplink = 27;</code>
         * @return The deeplink.
         */
        public String getDeeplink() {
          Object ref = deeplink_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            deeplink_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string deeplink = 27;</code>
         * @return The bytes for deeplink.
         */
        public com.google.protobuf.ByteString
            getDeeplinkBytes() {
          Object ref = deeplink_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            deeplink_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string deeplink = 27;</code>
         * @param value The deeplink to set.
         * @return This builder for chaining.
         */
        public Builder setDeeplink(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          deeplink_ = value;
          bitField0_ |= 0x00800000;
          onChanged();
          return this;
        }
        /**
         * <code>string deeplink = 27;</code>
         * @return This builder for chaining.
         */
        public Builder clearDeeplink() {
          deeplink_ = getDefaultInstance().getDeeplink();
          bitField0_ = (bitField0_ & ~0x00800000);
          onChanged();
          return this;
        }
        /**
         * <code>string deeplink = 27;</code>
         * @param value The bytes for deeplink to set.
         * @return This builder for chaining.
         */
        public Builder setDeeplinkBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          deeplink_ = value;
          bitField0_ |= 0x00800000;
          onChanged();
          return this;
        }

        private long expirationTime_ ;
        /**
         * <code>int64 expirationTime = 28;</code>
         * @return The expirationTime.
         */
        @Override
        public long getExpirationTime() {
          return expirationTime_;
        }
        /**
         * <code>int64 expirationTime = 28;</code>
         * @param value The expirationTime to set.
         * @return This builder for chaining.
         */
        public Builder setExpirationTime(long value) {

          expirationTime_ = value;
          bitField0_ |= 0x01000000;
          onChanged();
          return this;
        }
        /**
         * <code>int64 expirationTime = 28;</code>
         * @return This builder for chaining.
         */
        public Builder clearExpirationTime() {
          bitField0_ = (bitField0_ & ~0x01000000);
          expirationTime_ = 0L;
          onChanged();
          return this;
        }

        private ResVideo video_;
        private com.google.protobuf.SingleFieldBuilder<
            ResVideo, ResVideo.Builder, ResVideoOrBuilder> videoBuilder_;
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
         * @return Whether the video field is set.
         */
        public boolean hasVideo() {
          return ((bitField0_ & 0x02000000) != 0);
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
         * @return The video.
         */
        public ResVideo getVideo() {
          if (videoBuilder_ == null) {
            return video_ == null ? ResVideo.getDefaultInstance() : video_;
          } else {
            return videoBuilder_.getMessage();
          }
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
         */
        public Builder setVideo(ResVideo value) {
          if (videoBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            video_ = value;
          } else {
            videoBuilder_.setMessage(value);
          }
          bitField0_ |= 0x02000000;
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
         */
        public Builder setVideo(
            ResVideo.Builder builderForValue) {
          if (videoBuilder_ == null) {
            video_ = builderForValue.build();
          } else {
            videoBuilder_.setMessage(builderForValue.build());
          }
          bitField0_ |= 0x02000000;
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
         */
        public Builder mergeVideo(ResVideo value) {
          if (videoBuilder_ == null) {
            if (((bitField0_ & 0x02000000) != 0) &&
              video_ != null &&
              video_ != ResVideo.getDefaultInstance()) {
              getVideoBuilder().mergeFrom(value);
            } else {
              video_ = value;
            }
          } else {
            videoBuilder_.mergeFrom(value);
          }
          if (video_ != null) {
            bitField0_ |= 0x02000000;
            onChanged();
          }
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
         */
        public Builder clearVideo() {
          bitField0_ = (bitField0_ & ~0x02000000);
          video_ = null;
          if (videoBuilder_ != null) {
            videoBuilder_.dispose();
            videoBuilder_ = null;
          }
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
         */
        public ResVideo.Builder getVideoBuilder() {
          bitField0_ |= 0x02000000;
          onChanged();
          return getVideoFieldBuilder().getBuilder();
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
         */
        public ResVideoOrBuilder getVideoOrBuilder() {
          if (videoBuilder_ != null) {
            return videoBuilder_.getMessageOrBuilder();
          } else {
            return video_ == null ?
                ResVideo.getDefaultInstance() : video_;
          }
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.ResVideo video = 29;</code>
         */
        private com.google.protobuf.SingleFieldBuilder<
            ResVideo, ResVideo.Builder, ResVideoOrBuilder>
            getVideoFieldBuilder() {
          if (videoBuilder_ == null) {
            videoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
                ResVideo, ResVideo.Builder, ResVideoOrBuilder>(
                    getVideo(),
                    getParentForChildren(),
                    isClean());
            video_ = null;
          }
          return videoBuilder_;
        }

        private com.google.protobuf.LazyStringArrayList videoStartTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureVideoStartTracksIsMutable() {
          if (!videoStartTracks_.isModifiable()) {
            videoStartTracks_ = new com.google.protobuf.LazyStringArrayList(videoStartTracks_);
          }
          bitField0_ |= 0x04000000;
        }
        /**
         * <code>repeated string video_start_tracks = 30;</code>
         * @return A list containing the videoStartTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getVideoStartTracksList() {
          videoStartTracks_.makeImmutable();
          return videoStartTracks_;
        }
        /**
         * <code>repeated string video_start_tracks = 30;</code>
         * @return The count of videoStartTracks.
         */
        public int getVideoStartTracksCount() {
          return videoStartTracks_.size();
        }
        /**
         * <code>repeated string video_start_tracks = 30;</code>
         * @param index The index of the element to return.
         * @return The videoStartTracks at the given index.
         */
        public String getVideoStartTracks(int index) {
          return videoStartTracks_.get(index);
        }
        /**
         * <code>repeated string video_start_tracks = 30;</code>
         * @param index The index of the value to return.
         * @return The bytes of the videoStartTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getVideoStartTracksBytes(int index) {
          return videoStartTracks_.getByteString(index);
        }
        /**
         * <code>repeated string video_start_tracks = 30;</code>
         * @param index The index to set the value at.
         * @param value The videoStartTracks to set.
         * @return This builder for chaining.
         */
        public Builder setVideoStartTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoStartTracksIsMutable();
          videoStartTracks_.set(index, value);
          bitField0_ |= 0x04000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_start_tracks = 30;</code>
         * @param value The videoStartTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoStartTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoStartTracksIsMutable();
          videoStartTracks_.add(value);
          bitField0_ |= 0x04000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_start_tracks = 30;</code>
         * @param values The videoStartTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllVideoStartTracks(
            Iterable<String> values) {
          ensureVideoStartTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, videoStartTracks_);
          bitField0_ |= 0x04000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_start_tracks = 30;</code>
         * @return This builder for chaining.
         */
        public Builder clearVideoStartTracks() {
          videoStartTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x04000000);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_start_tracks = 30;</code>
         * @param value The bytes of the videoStartTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoStartTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureVideoStartTracksIsMutable();
          videoStartTracks_.add(value);
          bitField0_ |= 0x04000000;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList videoCompleteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureVideoCompleteTracksIsMutable() {
          if (!videoCompleteTracks_.isModifiable()) {
            videoCompleteTracks_ = new com.google.protobuf.LazyStringArrayList(videoCompleteTracks_);
          }
          bitField0_ |= 0x08000000;
        }
        /**
         * <code>repeated string video_complete_tracks = 31;</code>
         * @return A list containing the videoCompleteTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getVideoCompleteTracksList() {
          videoCompleteTracks_.makeImmutable();
          return videoCompleteTracks_;
        }
        /**
         * <code>repeated string video_complete_tracks = 31;</code>
         * @return The count of videoCompleteTracks.
         */
        public int getVideoCompleteTracksCount() {
          return videoCompleteTracks_.size();
        }
        /**
         * <code>repeated string video_complete_tracks = 31;</code>
         * @param index The index of the element to return.
         * @return The videoCompleteTracks at the given index.
         */
        public String getVideoCompleteTracks(int index) {
          return videoCompleteTracks_.get(index);
        }
        /**
         * <code>repeated string video_complete_tracks = 31;</code>
         * @param index The index of the value to return.
         * @return The bytes of the videoCompleteTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getVideoCompleteTracksBytes(int index) {
          return videoCompleteTracks_.getByteString(index);
        }
        /**
         * <code>repeated string video_complete_tracks = 31;</code>
         * @param index The index to set the value at.
         * @param value The videoCompleteTracks to set.
         * @return This builder for chaining.
         */
        public Builder setVideoCompleteTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoCompleteTracksIsMutable();
          videoCompleteTracks_.set(index, value);
          bitField0_ |= 0x08000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_complete_tracks = 31;</code>
         * @param value The videoCompleteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoCompleteTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoCompleteTracksIsMutable();
          videoCompleteTracks_.add(value);
          bitField0_ |= 0x08000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_complete_tracks = 31;</code>
         * @param values The videoCompleteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllVideoCompleteTracks(
            Iterable<String> values) {
          ensureVideoCompleteTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, videoCompleteTracks_);
          bitField0_ |= 0x08000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_complete_tracks = 31;</code>
         * @return This builder for chaining.
         */
        public Builder clearVideoCompleteTracks() {
          videoCompleteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x08000000);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_complete_tracks = 31;</code>
         * @param value The bytes of the videoCompleteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoCompleteTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureVideoCompleteTracksIsMutable();
          videoCompleteTracks_.add(value);
          bitField0_ |= 0x08000000;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList adCloseTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureAdCloseTracksIsMutable() {
          if (!adCloseTracks_.isModifiable()) {
            adCloseTracks_ = new com.google.protobuf.LazyStringArrayList(adCloseTracks_);
          }
          bitField0_ |= 0x10000000;
        }
        /**
         * <code>repeated string ad_close_tracks = 32;</code>
         * @return A list containing the adCloseTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getAdCloseTracksList() {
          adCloseTracks_.makeImmutable();
          return adCloseTracks_;
        }
        /**
         * <code>repeated string ad_close_tracks = 32;</code>
         * @return The count of adCloseTracks.
         */
        public int getAdCloseTracksCount() {
          return adCloseTracks_.size();
        }
        /**
         * <code>repeated string ad_close_tracks = 32;</code>
         * @param index The index of the element to return.
         * @return The adCloseTracks at the given index.
         */
        public String getAdCloseTracks(int index) {
          return adCloseTracks_.get(index);
        }
        /**
         * <code>repeated string ad_close_tracks = 32;</code>
         * @param index The index of the value to return.
         * @return The bytes of the adCloseTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getAdCloseTracksBytes(int index) {
          return adCloseTracks_.getByteString(index);
        }
        /**
         * <code>repeated string ad_close_tracks = 32;</code>
         * @param index The index to set the value at.
         * @param value The adCloseTracks to set.
         * @return This builder for chaining.
         */
        public Builder setAdCloseTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureAdCloseTracksIsMutable();
          adCloseTracks_.set(index, value);
          bitField0_ |= 0x10000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string ad_close_tracks = 32;</code>
         * @param value The adCloseTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAdCloseTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureAdCloseTracksIsMutable();
          adCloseTracks_.add(value);
          bitField0_ |= 0x10000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string ad_close_tracks = 32;</code>
         * @param values The adCloseTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllAdCloseTracks(
            Iterable<String> values) {
          ensureAdCloseTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, adCloseTracks_);
          bitField0_ |= 0x10000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string ad_close_tracks = 32;</code>
         * @return This builder for chaining.
         */
        public Builder clearAdCloseTracks() {
          adCloseTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x10000000);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string ad_close_tracks = 32;</code>
         * @param value The bytes of the adCloseTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAdCloseTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureAdCloseTracksIsMutable();
          adCloseTracks_.add(value);
          bitField0_ |= 0x10000000;
          onChanged();
          return this;
        }

        private int macro_ ;
        /**
         * <code>int32 macro = 33;</code>
         * @return The macro.
         */
        @Override
        public int getMacro() {
          return macro_;
        }
        /**
         * <code>int32 macro = 33;</code>
         * @param value The macro to set.
         * @return This builder for chaining.
         */
        public Builder setMacro(int value) {

          macro_ = value;
          bitField0_ |= 0x20000000;
          onChanged();
          return this;
        }
        /**
         * <code>int32 macro = 33;</code>
         * @return This builder for chaining.
         */
        public Builder clearMacro() {
          bitField0_ = (bitField0_ & ~0x20000000);
          macro_ = 0;
          onChanged();
          return this;
        }

        private java.util.List<Feedbacks> feedbacks_ =
          java.util.Collections.emptyList();
        private void ensureFeedbacksIsMutable() {
          if (!((bitField0_ & 0x40000000) != 0)) {
            feedbacks_ = new java.util.ArrayList<Feedbacks>(feedbacks_);
            bitField0_ |= 0x40000000;
           }
        }

        private com.google.protobuf.RepeatedFieldBuilder<
            Feedbacks, Feedbacks.Builder, FeedbacksOrBuilder> feedbacksBuilder_;

        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public java.util.List<Feedbacks> getFeedbacksList() {
          if (feedbacksBuilder_ == null) {
            return java.util.Collections.unmodifiableList(feedbacks_);
          } else {
            return feedbacksBuilder_.getMessageList();
          }
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public int getFeedbacksCount() {
          if (feedbacksBuilder_ == null) {
            return feedbacks_.size();
          } else {
            return feedbacksBuilder_.getCount();
          }
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Feedbacks getFeedbacks(int index) {
          if (feedbacksBuilder_ == null) {
            return feedbacks_.get(index);
          } else {
            return feedbacksBuilder_.getMessage(index);
          }
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Builder setFeedbacks(
            int index, Feedbacks value) {
          if (feedbacksBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureFeedbacksIsMutable();
            feedbacks_.set(index, value);
            onChanged();
          } else {
            feedbacksBuilder_.setMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Builder setFeedbacks(
            int index, Feedbacks.Builder builderForValue) {
          if (feedbacksBuilder_ == null) {
            ensureFeedbacksIsMutable();
            feedbacks_.set(index, builderForValue.build());
            onChanged();
          } else {
            feedbacksBuilder_.setMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Builder addFeedbacks(Feedbacks value) {
          if (feedbacksBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureFeedbacksIsMutable();
            feedbacks_.add(value);
            onChanged();
          } else {
            feedbacksBuilder_.addMessage(value);
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Builder addFeedbacks(
            int index, Feedbacks value) {
          if (feedbacksBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureFeedbacksIsMutable();
            feedbacks_.add(index, value);
            onChanged();
          } else {
            feedbacksBuilder_.addMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Builder addFeedbacks(
            Feedbacks.Builder builderForValue) {
          if (feedbacksBuilder_ == null) {
            ensureFeedbacksIsMutable();
            feedbacks_.add(builderForValue.build());
            onChanged();
          } else {
            feedbacksBuilder_.addMessage(builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Builder addFeedbacks(
            int index, Feedbacks.Builder builderForValue) {
          if (feedbacksBuilder_ == null) {
            ensureFeedbacksIsMutable();
            feedbacks_.add(index, builderForValue.build());
            onChanged();
          } else {
            feedbacksBuilder_.addMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Builder addAllFeedbacks(
            Iterable<? extends Feedbacks> values) {
          if (feedbacksBuilder_ == null) {
            ensureFeedbacksIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, feedbacks_);
            onChanged();
          } else {
            feedbacksBuilder_.addAllMessages(values);
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Builder clearFeedbacks() {
          if (feedbacksBuilder_ == null) {
            feedbacks_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x40000000);
            onChanged();
          } else {
            feedbacksBuilder_.clear();
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Builder removeFeedbacks(int index) {
          if (feedbacksBuilder_ == null) {
            ensureFeedbacksIsMutable();
            feedbacks_.remove(index);
            onChanged();
          } else {
            feedbacksBuilder_.remove(index);
          }
          return this;
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Feedbacks.Builder getFeedbacksBuilder(
            int index) {
          return getFeedbacksFieldBuilder().getBuilder(index);
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public FeedbacksOrBuilder getFeedbacksOrBuilder(
            int index) {
          if (feedbacksBuilder_ == null) {
            return feedbacks_.get(index);  } else {
            return feedbacksBuilder_.getMessageOrBuilder(index);
          }
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public java.util.List<? extends FeedbacksOrBuilder>
             getFeedbacksOrBuilderList() {
          if (feedbacksBuilder_ != null) {
            return feedbacksBuilder_.getMessageOrBuilderList();
          } else {
            return java.util.Collections.unmodifiableList(feedbacks_);
          }
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Feedbacks.Builder addFeedbacksBuilder() {
          return getFeedbacksFieldBuilder().addBuilder(
              Feedbacks.getDefaultInstance());
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public Feedbacks.Builder addFeedbacksBuilder(
            int index) {
          return getFeedbacksFieldBuilder().addBuilder(
              index, Feedbacks.getDefaultInstance());
        }
        /**
         * <code>repeated .dsp.BidResponse.SeatBid.Bid.Feedbacks feedbacks = 34;</code>
         */
        public java.util.List<Feedbacks.Builder>
             getFeedbacksBuilderList() {
          return getFeedbacksFieldBuilder().getBuilderList();
        }
        private com.google.protobuf.RepeatedFieldBuilder<
            Feedbacks, Feedbacks.Builder, FeedbacksOrBuilder>
            getFeedbacksFieldBuilder() {
          if (feedbacksBuilder_ == null) {
            feedbacksBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
                Feedbacks, Feedbacks.Builder, FeedbacksOrBuilder>(
                    feedbacks_,
                    ((bitField0_ & 0x40000000) != 0),
                    getParentForChildren(),
                    isClean());
            feedbacks_ = null;
          }
          return feedbacksBuilder_;
        }

        private com.google.protobuf.LazyStringArrayList videoPlayFirstQuartileTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureVideoPlayFirstQuartileTracksIsMutable() {
          if (!videoPlayFirstQuartileTracks_.isModifiable()) {
            videoPlayFirstQuartileTracks_ = new com.google.protobuf.LazyStringArrayList(videoPlayFirstQuartileTracks_);
          }
          bitField0_ |= 0x80000000;
        }
        /**
         * <code>repeated string video_play_first_quartile_tracks = 35;</code>
         * @return A list containing the videoPlayFirstQuartileTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getVideoPlayFirstQuartileTracksList() {
          videoPlayFirstQuartileTracks_.makeImmutable();
          return videoPlayFirstQuartileTracks_;
        }
        /**
         * <code>repeated string video_play_first_quartile_tracks = 35;</code>
         * @return The count of videoPlayFirstQuartileTracks.
         */
        public int getVideoPlayFirstQuartileTracksCount() {
          return videoPlayFirstQuartileTracks_.size();
        }
        /**
         * <code>repeated string video_play_first_quartile_tracks = 35;</code>
         * @param index The index of the element to return.
         * @return The videoPlayFirstQuartileTracks at the given index.
         */
        public String getVideoPlayFirstQuartileTracks(int index) {
          return videoPlayFirstQuartileTracks_.get(index);
        }
        /**
         * <code>repeated string video_play_first_quartile_tracks = 35;</code>
         * @param index The index of the value to return.
         * @return The bytes of the videoPlayFirstQuartileTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getVideoPlayFirstQuartileTracksBytes(int index) {
          return videoPlayFirstQuartileTracks_.getByteString(index);
        }
        /**
         * <code>repeated string video_play_first_quartile_tracks = 35;</code>
         * @param index The index to set the value at.
         * @param value The videoPlayFirstQuartileTracks to set.
         * @return This builder for chaining.
         */
        public Builder setVideoPlayFirstQuartileTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoPlayFirstQuartileTracksIsMutable();
          videoPlayFirstQuartileTracks_.set(index, value);
          bitField0_ |= 0x80000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_first_quartile_tracks = 35;</code>
         * @param value The videoPlayFirstQuartileTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoPlayFirstQuartileTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoPlayFirstQuartileTracksIsMutable();
          videoPlayFirstQuartileTracks_.add(value);
          bitField0_ |= 0x80000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_first_quartile_tracks = 35;</code>
         * @param values The videoPlayFirstQuartileTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllVideoPlayFirstQuartileTracks(
            Iterable<String> values) {
          ensureVideoPlayFirstQuartileTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, videoPlayFirstQuartileTracks_);
          bitField0_ |= 0x80000000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_first_quartile_tracks = 35;</code>
         * @return This builder for chaining.
         */
        public Builder clearVideoPlayFirstQuartileTracks() {
          videoPlayFirstQuartileTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x80000000);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_first_quartile_tracks = 35;</code>
         * @param value The bytes of the videoPlayFirstQuartileTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoPlayFirstQuartileTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureVideoPlayFirstQuartileTracksIsMutable();
          videoPlayFirstQuartileTracks_.add(value);
          bitField0_ |= 0x80000000;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList videoPlayMidpointTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureVideoPlayMidpointTracksIsMutable() {
          if (!videoPlayMidpointTracks_.isModifiable()) {
            videoPlayMidpointTracks_ = new com.google.protobuf.LazyStringArrayList(videoPlayMidpointTracks_);
          }
          bitField1_ |= 0x00000001;
        }
        /**
         * <code>repeated string video_play_midpoint_tracks = 36;</code>
         * @return A list containing the videoPlayMidpointTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getVideoPlayMidpointTracksList() {
          videoPlayMidpointTracks_.makeImmutable();
          return videoPlayMidpointTracks_;
        }
        /**
         * <code>repeated string video_play_midpoint_tracks = 36;</code>
         * @return The count of videoPlayMidpointTracks.
         */
        public int getVideoPlayMidpointTracksCount() {
          return videoPlayMidpointTracks_.size();
        }
        /**
         * <code>repeated string video_play_midpoint_tracks = 36;</code>
         * @param index The index of the element to return.
         * @return The videoPlayMidpointTracks at the given index.
         */
        public String getVideoPlayMidpointTracks(int index) {
          return videoPlayMidpointTracks_.get(index);
        }
        /**
         * <code>repeated string video_play_midpoint_tracks = 36;</code>
         * @param index The index of the value to return.
         * @return The bytes of the videoPlayMidpointTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getVideoPlayMidpointTracksBytes(int index) {
          return videoPlayMidpointTracks_.getByteString(index);
        }
        /**
         * <code>repeated string video_play_midpoint_tracks = 36;</code>
         * @param index The index to set the value at.
         * @param value The videoPlayMidpointTracks to set.
         * @return This builder for chaining.
         */
        public Builder setVideoPlayMidpointTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoPlayMidpointTracksIsMutable();
          videoPlayMidpointTracks_.set(index, value);
          bitField1_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_midpoint_tracks = 36;</code>
         * @param value The videoPlayMidpointTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoPlayMidpointTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoPlayMidpointTracksIsMutable();
          videoPlayMidpointTracks_.add(value);
          bitField1_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_midpoint_tracks = 36;</code>
         * @param values The videoPlayMidpointTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllVideoPlayMidpointTracks(
            Iterable<String> values) {
          ensureVideoPlayMidpointTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, videoPlayMidpointTracks_);
          bitField1_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_midpoint_tracks = 36;</code>
         * @return This builder for chaining.
         */
        public Builder clearVideoPlayMidpointTracks() {
          videoPlayMidpointTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField1_ = (bitField1_ & ~0x00000001);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_midpoint_tracks = 36;</code>
         * @param value The bytes of the videoPlayMidpointTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoPlayMidpointTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureVideoPlayMidpointTracksIsMutable();
          videoPlayMidpointTracks_.add(value);
          bitField1_ |= 0x00000001;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList videoPlayThirdQuartileTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureVideoPlayThirdQuartileTracksIsMutable() {
          if (!videoPlayThirdQuartileTracks_.isModifiable()) {
            videoPlayThirdQuartileTracks_ = new com.google.protobuf.LazyStringArrayList(videoPlayThirdQuartileTracks_);
          }
          bitField1_ |= 0x00000002;
        }
        /**
         * <code>repeated string video_play_third_quartile_tracks = 37;</code>
         * @return A list containing the videoPlayThirdQuartileTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getVideoPlayThirdQuartileTracksList() {
          videoPlayThirdQuartileTracks_.makeImmutable();
          return videoPlayThirdQuartileTracks_;
        }
        /**
         * <code>repeated string video_play_third_quartile_tracks = 37;</code>
         * @return The count of videoPlayThirdQuartileTracks.
         */
        public int getVideoPlayThirdQuartileTracksCount() {
          return videoPlayThirdQuartileTracks_.size();
        }
        /**
         * <code>repeated string video_play_third_quartile_tracks = 37;</code>
         * @param index The index of the element to return.
         * @return The videoPlayThirdQuartileTracks at the given index.
         */
        public String getVideoPlayThirdQuartileTracks(int index) {
          return videoPlayThirdQuartileTracks_.get(index);
        }
        /**
         * <code>repeated string video_play_third_quartile_tracks = 37;</code>
         * @param index The index of the value to return.
         * @return The bytes of the videoPlayThirdQuartileTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getVideoPlayThirdQuartileTracksBytes(int index) {
          return videoPlayThirdQuartileTracks_.getByteString(index);
        }
        /**
         * <code>repeated string video_play_third_quartile_tracks = 37;</code>
         * @param index The index to set the value at.
         * @param value The videoPlayThirdQuartileTracks to set.
         * @return This builder for chaining.
         */
        public Builder setVideoPlayThirdQuartileTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoPlayThirdQuartileTracksIsMutable();
          videoPlayThirdQuartileTracks_.set(index, value);
          bitField1_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_third_quartile_tracks = 37;</code>
         * @param value The videoPlayThirdQuartileTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoPlayThirdQuartileTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoPlayThirdQuartileTracksIsMutable();
          videoPlayThirdQuartileTracks_.add(value);
          bitField1_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_third_quartile_tracks = 37;</code>
         * @param values The videoPlayThirdQuartileTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllVideoPlayThirdQuartileTracks(
            Iterable<String> values) {
          ensureVideoPlayThirdQuartileTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, videoPlayThirdQuartileTracks_);
          bitField1_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_third_quartile_tracks = 37;</code>
         * @return This builder for chaining.
         */
        public Builder clearVideoPlayThirdQuartileTracks() {
          videoPlayThirdQuartileTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField1_ = (bitField1_ & ~0x00000002);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_play_third_quartile_tracks = 37;</code>
         * @param value The bytes of the videoPlayThirdQuartileTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoPlayThirdQuartileTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureVideoPlayThirdQuartileTracksIsMutable();
          videoPlayThirdQuartileTracks_.add(value);
          bitField1_ |= 0x00000002;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList videoSkipTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureVideoSkipTracksIsMutable() {
          if (!videoSkipTracks_.isModifiable()) {
            videoSkipTracks_ = new com.google.protobuf.LazyStringArrayList(videoSkipTracks_);
          }
          bitField1_ |= 0x00000004;
        }
        /**
         * <code>repeated string video_skip_tracks = 38;</code>
         * @return A list containing the videoSkipTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getVideoSkipTracksList() {
          videoSkipTracks_.makeImmutable();
          return videoSkipTracks_;
        }
        /**
         * <code>repeated string video_skip_tracks = 38;</code>
         * @return The count of videoSkipTracks.
         */
        public int getVideoSkipTracksCount() {
          return videoSkipTracks_.size();
        }
        /**
         * <code>repeated string video_skip_tracks = 38;</code>
         * @param index The index of the element to return.
         * @return The videoSkipTracks at the given index.
         */
        public String getVideoSkipTracks(int index) {
          return videoSkipTracks_.get(index);
        }
        /**
         * <code>repeated string video_skip_tracks = 38;</code>
         * @param index The index of the value to return.
         * @return The bytes of the videoSkipTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getVideoSkipTracksBytes(int index) {
          return videoSkipTracks_.getByteString(index);
        }
        /**
         * <code>repeated string video_skip_tracks = 38;</code>
         * @param index The index to set the value at.
         * @param value The videoSkipTracks to set.
         * @return This builder for chaining.
         */
        public Builder setVideoSkipTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoSkipTracksIsMutable();
          videoSkipTracks_.set(index, value);
          bitField1_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_skip_tracks = 38;</code>
         * @param value The videoSkipTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoSkipTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoSkipTracksIsMutable();
          videoSkipTracks_.add(value);
          bitField1_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_skip_tracks = 38;</code>
         * @param values The videoSkipTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllVideoSkipTracks(
            Iterable<String> values) {
          ensureVideoSkipTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, videoSkipTracks_);
          bitField1_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_skip_tracks = 38;</code>
         * @return This builder for chaining.
         */
        public Builder clearVideoSkipTracks() {
          videoSkipTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField1_ = (bitField1_ & ~0x00000004);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_skip_tracks = 38;</code>
         * @param value The bytes of the videoSkipTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoSkipTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureVideoSkipTracksIsMutable();
          videoSkipTracks_.add(value);
          bitField1_ |= 0x00000004;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList videoClickTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureVideoClickTracksIsMutable() {
          if (!videoClickTracks_.isModifiable()) {
            videoClickTracks_ = new com.google.protobuf.LazyStringArrayList(videoClickTracks_);
          }
          bitField1_ |= 0x00000008;
        }
        /**
         * <code>repeated string video_click_tracks = 39;</code>
         * @return A list containing the videoClickTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getVideoClickTracksList() {
          videoClickTracks_.makeImmutable();
          return videoClickTracks_;
        }
        /**
         * <code>repeated string video_click_tracks = 39;</code>
         * @return The count of videoClickTracks.
         */
        public int getVideoClickTracksCount() {
          return videoClickTracks_.size();
        }
        /**
         * <code>repeated string video_click_tracks = 39;</code>
         * @param index The index of the element to return.
         * @return The videoClickTracks at the given index.
         */
        public String getVideoClickTracks(int index) {
          return videoClickTracks_.get(index);
        }
        /**
         * <code>repeated string video_click_tracks = 39;</code>
         * @param index The index of the value to return.
         * @return The bytes of the videoClickTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getVideoClickTracksBytes(int index) {
          return videoClickTracks_.getByteString(index);
        }
        /**
         * <code>repeated string video_click_tracks = 39;</code>
         * @param index The index to set the value at.
         * @param value The videoClickTracks to set.
         * @return This builder for chaining.
         */
        public Builder setVideoClickTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoClickTracksIsMutable();
          videoClickTracks_.set(index, value);
          bitField1_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_click_tracks = 39;</code>
         * @param value The videoClickTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoClickTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoClickTracksIsMutable();
          videoClickTracks_.add(value);
          bitField1_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_click_tracks = 39;</code>
         * @param values The videoClickTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllVideoClickTracks(
            Iterable<String> values) {
          ensureVideoClickTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, videoClickTracks_);
          bitField1_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_click_tracks = 39;</code>
         * @return This builder for chaining.
         */
        public Builder clearVideoClickTracks() {
          videoClickTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField1_ = (bitField1_ & ~0x00000008);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_click_tracks = 39;</code>
         * @param value The bytes of the videoClickTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoClickTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureVideoClickTracksIsMutable();
          videoClickTracks_.add(value);
          bitField1_ |= 0x00000008;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList videoMuteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureVideoMuteTracksIsMutable() {
          if (!videoMuteTracks_.isModifiable()) {
            videoMuteTracks_ = new com.google.protobuf.LazyStringArrayList(videoMuteTracks_);
          }
          bitField1_ |= 0x00000010;
        }
        /**
         * <code>repeated string video_mute_tracks = 40;</code>
         * @return A list containing the videoMuteTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getVideoMuteTracksList() {
          videoMuteTracks_.makeImmutable();
          return videoMuteTracks_;
        }
        /**
         * <code>repeated string video_mute_tracks = 40;</code>
         * @return The count of videoMuteTracks.
         */
        public int getVideoMuteTracksCount() {
          return videoMuteTracks_.size();
        }
        /**
         * <code>repeated string video_mute_tracks = 40;</code>
         * @param index The index of the element to return.
         * @return The videoMuteTracks at the given index.
         */
        public String getVideoMuteTracks(int index) {
          return videoMuteTracks_.get(index);
        }
        /**
         * <code>repeated string video_mute_tracks = 40;</code>
         * @param index The index of the value to return.
         * @return The bytes of the videoMuteTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getVideoMuteTracksBytes(int index) {
          return videoMuteTracks_.getByteString(index);
        }
        /**
         * <code>repeated string video_mute_tracks = 40;</code>
         * @param index The index to set the value at.
         * @param value The videoMuteTracks to set.
         * @return This builder for chaining.
         */
        public Builder setVideoMuteTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoMuteTracksIsMutable();
          videoMuteTracks_.set(index, value);
          bitField1_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_mute_tracks = 40;</code>
         * @param value The videoMuteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoMuteTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoMuteTracksIsMutable();
          videoMuteTracks_.add(value);
          bitField1_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_mute_tracks = 40;</code>
         * @param values The videoMuteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllVideoMuteTracks(
            Iterable<String> values) {
          ensureVideoMuteTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, videoMuteTracks_);
          bitField1_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_mute_tracks = 40;</code>
         * @return This builder for chaining.
         */
        public Builder clearVideoMuteTracks() {
          videoMuteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField1_ = (bitField1_ & ~0x00000010);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_mute_tracks = 40;</code>
         * @param value The bytes of the videoMuteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoMuteTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureVideoMuteTracksIsMutable();
          videoMuteTracks_.add(value);
          bitField1_ |= 0x00000010;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList videoUnmuteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureVideoUnmuteTracksIsMutable() {
          if (!videoUnmuteTracks_.isModifiable()) {
            videoUnmuteTracks_ = new com.google.protobuf.LazyStringArrayList(videoUnmuteTracks_);
          }
          bitField1_ |= 0x00000020;
        }
        /**
         * <code>repeated string video_unmute_tracks = 41;</code>
         * @return A list containing the videoUnmuteTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getVideoUnmuteTracksList() {
          videoUnmuteTracks_.makeImmutable();
          return videoUnmuteTracks_;
        }
        /**
         * <code>repeated string video_unmute_tracks = 41;</code>
         * @return The count of videoUnmuteTracks.
         */
        public int getVideoUnmuteTracksCount() {
          return videoUnmuteTracks_.size();
        }
        /**
         * <code>repeated string video_unmute_tracks = 41;</code>
         * @param index The index of the element to return.
         * @return The videoUnmuteTracks at the given index.
         */
        public String getVideoUnmuteTracks(int index) {
          return videoUnmuteTracks_.get(index);
        }
        /**
         * <code>repeated string video_unmute_tracks = 41;</code>
         * @param index The index of the value to return.
         * @return The bytes of the videoUnmuteTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getVideoUnmuteTracksBytes(int index) {
          return videoUnmuteTracks_.getByteString(index);
        }
        /**
         * <code>repeated string video_unmute_tracks = 41;</code>
         * @param index The index to set the value at.
         * @param value The videoUnmuteTracks to set.
         * @return This builder for chaining.
         */
        public Builder setVideoUnmuteTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoUnmuteTracksIsMutable();
          videoUnmuteTracks_.set(index, value);
          bitField1_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_unmute_tracks = 41;</code>
         * @param value The videoUnmuteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoUnmuteTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureVideoUnmuteTracksIsMutable();
          videoUnmuteTracks_.add(value);
          bitField1_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_unmute_tracks = 41;</code>
         * @param values The videoUnmuteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllVideoUnmuteTracks(
            Iterable<String> values) {
          ensureVideoUnmuteTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, videoUnmuteTracks_);
          bitField1_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_unmute_tracks = 41;</code>
         * @return This builder for chaining.
         */
        public Builder clearVideoUnmuteTracks() {
          videoUnmuteTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField1_ = (bitField1_ & ~0x00000020);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string video_unmute_tracks = 41;</code>
         * @param value The bytes of the videoUnmuteTracks to add.
         * @return This builder for chaining.
         */
        public Builder addVideoUnmuteTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureVideoUnmuteTracksIsMutable();
          videoUnmuteTracks_.add(value);
          bitField1_ |= 0x00000020;
          onChanged();
          return this;
        }

        private Object source_ = "";
        /**
         * <code>string source = 42;</code>
         * @return The source.
         */
        public String getSource() {
          Object ref = source_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            source_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string source = 42;</code>
         * @return The bytes for source.
         */
        public com.google.protobuf.ByteString
            getSourceBytes() {
          Object ref = source_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            source_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string source = 42;</code>
         * @param value The source to set.
         * @return This builder for chaining.
         */
        public Builder setSource(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          source_ = value;
          bitField1_ |= 0x00000040;
          onChanged();
          return this;
        }
        /**
         * <code>string source = 42;</code>
         * @return This builder for chaining.
         */
        public Builder clearSource() {
          source_ = getDefaultInstance().getSource();
          bitField1_ = (bitField1_ & ~0x00000040);
          onChanged();
          return this;
        }
        /**
         * <code>string source = 42;</code>
         * @param value The bytes for source to set.
         * @return This builder for chaining.
         */
        public Builder setSourceBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          source_ = value;
          bitField1_ |= 0x00000040;
          onChanged();
          return this;
        }

        private int price_ ;
        /**
         * <code>int32 price = 43;</code>
         * @return The price.
         */
        @Override
        public int getPrice() {
          return price_;
        }
        /**
         * <code>int32 price = 43;</code>
         * @param value The price to set.
         * @return This builder for chaining.
         */
        public Builder setPrice(int value) {

          price_ = value;
          bitField1_ |= 0x00000080;
          onChanged();
          return this;
        }
        /**
         * <code>int32 price = 43;</code>
         * @return This builder for chaining.
         */
        public Builder clearPrice() {
          bitField1_ = (bitField1_ & ~0x00000080);
          price_ = 0;
          onChanged();
          return this;
        }

        private Object nurl_ = "";
        /**
         * <code>string nurl = 44;</code>
         * @return The nurl.
         */
        public String getNurl() {
          Object ref = nurl_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            nurl_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string nurl = 44;</code>
         * @return The bytes for nurl.
         */
        public com.google.protobuf.ByteString
            getNurlBytes() {
          Object ref = nurl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            nurl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string nurl = 44;</code>
         * @param value The nurl to set.
         * @return This builder for chaining.
         */
        public Builder setNurl(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          nurl_ = value;
          bitField1_ |= 0x00000100;
          onChanged();
          return this;
        }
        /**
         * <code>string nurl = 44;</code>
         * @return This builder for chaining.
         */
        public Builder clearNurl() {
          nurl_ = getDefaultInstance().getNurl();
          bitField1_ = (bitField1_ & ~0x00000100);
          onChanged();
          return this;
        }
        /**
         * <code>string nurl = 44;</code>
         * @param value The bytes for nurl to set.
         * @return This builder for chaining.
         */
        public Builder setNurlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          nurl_ = value;
          bitField1_ |= 0x00000100;
          onChanged();
          return this;
        }

        private Object lurl_ = "";
        /**
         * <code>string lurl = 45;</code>
         * @return The lurl.
         */
        public String getLurl() {
          Object ref = lurl_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            lurl_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string lurl = 45;</code>
         * @return The bytes for lurl.
         */
        public com.google.protobuf.ByteString
            getLurlBytes() {
          Object ref = lurl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            lurl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string lurl = 45;</code>
         * @param value The lurl to set.
         * @return This builder for chaining.
         */
        public Builder setLurl(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          lurl_ = value;
          bitField1_ |= 0x00000200;
          onChanged();
          return this;
        }
        /**
         * <code>string lurl = 45;</code>
         * @return This builder for chaining.
         */
        public Builder clearLurl() {
          lurl_ = getDefaultInstance().getLurl();
          bitField1_ = (bitField1_ & ~0x00000200);
          onChanged();
          return this;
        }
        /**
         * <code>string lurl = 45;</code>
         * @param value The bytes for lurl to set.
         * @return This builder for chaining.
         */
        public Builder setLurlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          lurl_ = value;
          bitField1_ |= 0x00000200;
          onChanged();
          return this;
        }

        private Ext ext_;
        private com.google.protobuf.SingleFieldBuilder<
            Ext, Ext.Builder, ExtOrBuilder> extBuilder_;
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
         * @return Whether the ext field is set.
         */
        public boolean hasExt() {
          return ((bitField1_ & 0x00000400) != 0);
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
         * @return The ext.
         */
        public Ext getExt() {
          if (extBuilder_ == null) {
            return ext_ == null ? Ext.getDefaultInstance() : ext_;
          } else {
            return extBuilder_.getMessage();
          }
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
         */
        public Builder setExt(Ext value) {
          if (extBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ext_ = value;
          } else {
            extBuilder_.setMessage(value);
          }
          bitField1_ |= 0x00000400;
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
         */
        public Builder setExt(
            Ext.Builder builderForValue) {
          if (extBuilder_ == null) {
            ext_ = builderForValue.build();
          } else {
            extBuilder_.setMessage(builderForValue.build());
          }
          bitField1_ |= 0x00000400;
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
         */
        public Builder mergeExt(Ext value) {
          if (extBuilder_ == null) {
            if (((bitField1_ & 0x00000400) != 0) &&
              ext_ != null &&
              ext_ != Ext.getDefaultInstance()) {
              getExtBuilder().mergeFrom(value);
            } else {
              ext_ = value;
            }
          } else {
            extBuilder_.mergeFrom(value);
          }
          if (ext_ != null) {
            bitField1_ |= 0x00000400;
            onChanged();
          }
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
         */
        public Builder clearExt() {
          bitField1_ = (bitField1_ & ~0x00000400);
          ext_ = null;
          if (extBuilder_ != null) {
            extBuilder_.dispose();
            extBuilder_ = null;
          }
          onChanged();
          return this;
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
         */
        public Ext.Builder getExtBuilder() {
          bitField1_ |= 0x00000400;
          onChanged();
          return getExtFieldBuilder().getBuilder();
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
         */
        public ExtOrBuilder getExtOrBuilder() {
          if (extBuilder_ != null) {
            return extBuilder_.getMessageOrBuilder();
          } else {
            return ext_ == null ?
                Ext.getDefaultInstance() : ext_;
          }
        }
        /**
         * <code>.dsp.BidResponse.SeatBid.Bid.Ext ext = 46;</code>
         */
        private com.google.protobuf.SingleFieldBuilder<
            Ext, Ext.Builder, ExtOrBuilder>
            getExtFieldBuilder() {
          if (extBuilder_ == null) {
            extBuilder_ = new com.google.protobuf.SingleFieldBuilder<
                Ext, Ext.Builder, ExtOrBuilder>(
                    getExt(),
                    getParentForChildren(),
                    isClean());
            ext_ = null;
          }
          return extBuilder_;
        }

        private Object universalLink_ = "";
        /**
         * <code>string universal_link = 47;</code>
         * @return The universalLink.
         */
        public String getUniversalLink() {
          Object ref = universalLink_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            universalLink_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string universal_link = 47;</code>
         * @return The bytes for universalLink.
         */
        public com.google.protobuf.ByteString
            getUniversalLinkBytes() {
          Object ref = universalLink_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            universalLink_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string universal_link = 47;</code>
         * @param value The universalLink to set.
         * @return This builder for chaining.
         */
        public Builder setUniversalLink(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          universalLink_ = value;
          bitField1_ |= 0x00000800;
          onChanged();
          return this;
        }
        /**
         * <code>string universal_link = 47;</code>
         * @return This builder for chaining.
         */
        public Builder clearUniversalLink() {
          universalLink_ = getDefaultInstance().getUniversalLink();
          bitField1_ = (bitField1_ & ~0x00000800);
          onChanged();
          return this;
        }
        /**
         * <code>string universal_link = 47;</code>
         * @param value The bytes for universalLink to set.
         * @return This builder for chaining.
         */
        public Builder setUniversalLinkBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          universalLink_ = value;
          bitField1_ |= 0x00000800;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList dpFailTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureDpFailTracksIsMutable() {
          if (!dpFailTracks_.isModifiable()) {
            dpFailTracks_ = new com.google.protobuf.LazyStringArrayList(dpFailTracks_);
          }
          bitField1_ |= 0x00001000;
        }
        /**
         * <code>repeated string dp_fail_tracks = 48;</code>
         * @return A list containing the dpFailTracks.
         */
        public com.google.protobuf.ProtocolStringList
            getDpFailTracksList() {
          dpFailTracks_.makeImmutable();
          return dpFailTracks_;
        }
        /**
         * <code>repeated string dp_fail_tracks = 48;</code>
         * @return The count of dpFailTracks.
         */
        public int getDpFailTracksCount() {
          return dpFailTracks_.size();
        }
        /**
         * <code>repeated string dp_fail_tracks = 48;</code>
         * @param index The index of the element to return.
         * @return The dpFailTracks at the given index.
         */
        public String getDpFailTracks(int index) {
          return dpFailTracks_.get(index);
        }
        /**
         * <code>repeated string dp_fail_tracks = 48;</code>
         * @param index The index of the value to return.
         * @return The bytes of the dpFailTracks at the given index.
         */
        public com.google.protobuf.ByteString
            getDpFailTracksBytes(int index) {
          return dpFailTracks_.getByteString(index);
        }
        /**
         * <code>repeated string dp_fail_tracks = 48;</code>
         * @param index The index to set the value at.
         * @param value The dpFailTracks to set.
         * @return This builder for chaining.
         */
        public Builder setDpFailTracks(
            int index, String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureDpFailTracksIsMutable();
          dpFailTracks_.set(index, value);
          bitField1_ |= 0x00001000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string dp_fail_tracks = 48;</code>
         * @param value The dpFailTracks to add.
         * @return This builder for chaining.
         */
        public Builder addDpFailTracks(
            String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureDpFailTracksIsMutable();
          dpFailTracks_.add(value);
          bitField1_ |= 0x00001000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string dp_fail_tracks = 48;</code>
         * @param values The dpFailTracks to add.
         * @return This builder for chaining.
         */
        public Builder addAllDpFailTracks(
            Iterable<String> values) {
          ensureDpFailTracksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dpFailTracks_);
          bitField1_ |= 0x00001000;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string dp_fail_tracks = 48;</code>
         * @return This builder for chaining.
         */
        public Builder clearDpFailTracks() {
          dpFailTracks_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField1_ = (bitField1_ & ~0x00001000);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string dp_fail_tracks = 48;</code>
         * @param value The bytes of the dpFailTracks to add.
         * @return This builder for chaining.
         */
        public Builder addDpFailTracksBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureDpFailTracksIsMutable();
          dpFailTracks_.add(value);
          bitField1_ |= 0x00001000;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:dsp.BidResponse.SeatBid.Bid)
      }

      // @@protoc_insertion_point(class_scope:dsp.BidResponse.SeatBid.Bid)
      private static final Bid DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new Bid();
      }

      public static Bid getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Bid>
          PARSER = new com.google.protobuf.AbstractParser<Bid>() {
        @Override
        public Bid parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Bid> parser() {
        return PARSER;
      }

      @Override
      public com.google.protobuf.Parser<Bid> getParserForType() {
        return PARSER;
      }

      @Override
      public Bid getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int BID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<Bid> bid_;
    /**
     * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
     */
    @Override
    public java.util.List<Bid> getBidList() {
      return bid_;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
     */
    @Override
    public java.util.List<? extends BidOrBuilder>
        getBidOrBuilderList() {
      return bid_;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
     */
    @Override
    public int getBidCount() {
      return bid_.size();
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
     */
    @Override
    public Bid getBid(int index) {
      return bid_.get(index);
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
     */
    @Override
    public BidOrBuilder getBidOrBuilder(
        int index) {
      return bid_.get(index);
    }

    public static final int SEAT_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile Object seat_ = "";
    /**
     * <code>string seat = 2;</code>
     * @return The seat.
     */
    @Override
    public String getSeat() {
      Object ref = seat_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        seat_ = s;
        return s;
      }
    }
    /**
     * <code>string seat = 2;</code>
     * @return The bytes for seat.
     */
    @Override
    public com.google.protobuf.ByteString
        getSeatBytes() {
      Object ref = seat_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        seat_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < bid_.size(); i++) {
        output.writeMessage(1, bid_.get(i));
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(seat_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, seat_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < bid_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, bid_.get(i));
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(seat_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, seat_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof SeatBid)) {
        return super.equals(obj);
      }
      SeatBid other = (SeatBid) obj;

      if (!getBidList()
          .equals(other.getBidList())) return false;
      if (!getSeat()
          .equals(other.getSeat())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getBidCount() > 0) {
        hash = (37 * hash) + BID_FIELD_NUMBER;
        hash = (53 * hash) + getBidList().hashCode();
      }
      hash = (37 * hash) + SEAT_FIELD_NUMBER;
      hash = (53 * hash) + getSeat().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static SeatBid parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SeatBid parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SeatBid parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SeatBid parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SeatBid parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SeatBid parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SeatBid parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static SeatBid parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static SeatBid parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static SeatBid parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static SeatBid parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static SeatBid parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(SeatBid prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code dsp.BidResponse.SeatBid}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:dsp.BidResponse.SeatBid)
        SeatBidOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                SeatBid.class, Builder.class);
      }

      // Construct using cn.taken.ad.logic.prossor.hailiang.dto.BidResponse.SeatBid.newBuilder()
      private Builder() {

      }

      private Builder(
          BuilderParent parent) {
        super(parent);

      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (bidBuilder_ == null) {
          bid_ = java.util.Collections.emptyList();
        } else {
          bid_ = null;
          bidBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        seat_ = "";
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return HaiLiangDto.internal_static_dsp_BidResponse_SeatBid_descriptor;
      }

      @Override
      public SeatBid getDefaultInstanceForType() {
        return SeatBid.getDefaultInstance();
      }

      @Override
      public SeatBid build() {
        SeatBid result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public SeatBid buildPartial() {
        SeatBid result = new SeatBid(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(SeatBid result) {
        if (bidBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            bid_ = java.util.Collections.unmodifiableList(bid_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.bid_ = bid_;
        } else {
          result.bid_ = bidBuilder_.build();
        }
      }

      private void buildPartial0(SeatBid result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.seat_ = seat_;
        }
      }

      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof SeatBid) {
          return mergeFrom((SeatBid)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(SeatBid other) {
        if (other == SeatBid.getDefaultInstance()) return this;
        if (bidBuilder_ == null) {
          if (!other.bid_.isEmpty()) {
            if (bid_.isEmpty()) {
              bid_ = other.bid_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureBidIsMutable();
              bid_.addAll(other.bid_);
            }
            onChanged();
          }
        } else {
          if (!other.bid_.isEmpty()) {
            if (bidBuilder_.isEmpty()) {
              bidBuilder_.dispose();
              bidBuilder_ = null;
              bid_ = other.bid_;
              bitField0_ = (bitField0_ & ~0x00000001);
              bidBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getBidFieldBuilder() : null;
            } else {
              bidBuilder_.addAllMessages(other.bid_);
            }
          }
        }
        if (!other.getSeat().isEmpty()) {
          seat_ = other.seat_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                Bid m =
                    input.readMessage(
                        Bid.parser(),
                        extensionRegistry);
                if (bidBuilder_ == null) {
                  ensureBidIsMutable();
                  bid_.add(m);
                } else {
                  bidBuilder_.addMessage(m);
                }
                break;
              } // case 10
              case 18: {
                seat_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<Bid> bid_ =
        java.util.Collections.emptyList();
      private void ensureBidIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          bid_ = new java.util.ArrayList<Bid>(bid_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          Bid, Bid.Builder, BidOrBuilder> bidBuilder_;

      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public java.util.List<Bid> getBidList() {
        if (bidBuilder_ == null) {
          return java.util.Collections.unmodifiableList(bid_);
        } else {
          return bidBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public int getBidCount() {
        if (bidBuilder_ == null) {
          return bid_.size();
        } else {
          return bidBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Bid getBid(int index) {
        if (bidBuilder_ == null) {
          return bid_.get(index);
        } else {
          return bidBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Builder setBid(
          int index, Bid value) {
        if (bidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBidIsMutable();
          bid_.set(index, value);
          onChanged();
        } else {
          bidBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Builder setBid(
          int index, Bid.Builder builderForValue) {
        if (bidBuilder_ == null) {
          ensureBidIsMutable();
          bid_.set(index, builderForValue.build());
          onChanged();
        } else {
          bidBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Builder addBid(Bid value) {
        if (bidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBidIsMutable();
          bid_.add(value);
          onChanged();
        } else {
          bidBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Builder addBid(
          int index, Bid value) {
        if (bidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBidIsMutable();
          bid_.add(index, value);
          onChanged();
        } else {
          bidBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Builder addBid(
          Bid.Builder builderForValue) {
        if (bidBuilder_ == null) {
          ensureBidIsMutable();
          bid_.add(builderForValue.build());
          onChanged();
        } else {
          bidBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Builder addBid(
          int index, Bid.Builder builderForValue) {
        if (bidBuilder_ == null) {
          ensureBidIsMutable();
          bid_.add(index, builderForValue.build());
          onChanged();
        } else {
          bidBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Builder addAllBid(
          Iterable<? extends Bid> values) {
        if (bidBuilder_ == null) {
          ensureBidIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, bid_);
          onChanged();
        } else {
          bidBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Builder clearBid() {
        if (bidBuilder_ == null) {
          bid_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          bidBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Builder removeBid(int index) {
        if (bidBuilder_ == null) {
          ensureBidIsMutable();
          bid_.remove(index);
          onChanged();
        } else {
          bidBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Bid.Builder getBidBuilder(
          int index) {
        return getBidFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public BidOrBuilder getBidOrBuilder(
          int index) {
        if (bidBuilder_ == null) {
          return bid_.get(index);  } else {
          return bidBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public java.util.List<? extends BidOrBuilder>
           getBidOrBuilderList() {
        if (bidBuilder_ != null) {
          return bidBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(bid_);
        }
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Bid.Builder addBidBuilder() {
        return getBidFieldBuilder().addBuilder(
            Bid.getDefaultInstance());
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public Bid.Builder addBidBuilder(
          int index) {
        return getBidFieldBuilder().addBuilder(
            index, Bid.getDefaultInstance());
      }
      /**
       * <code>repeated .dsp.BidResponse.SeatBid.Bid bid = 1;</code>
       */
      public java.util.List<Bid.Builder>
           getBidBuilderList() {
        return getBidFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          Bid, Bid.Builder, BidOrBuilder>
          getBidFieldBuilder() {
        if (bidBuilder_ == null) {
          bidBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              Bid, Bid.Builder, BidOrBuilder>(
                  bid_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          bid_ = null;
        }
        return bidBuilder_;
      }

      private Object seat_ = "";
      /**
       * <code>string seat = 2;</code>
       * @return The seat.
       */
      public String getSeat() {
        Object ref = seat_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          seat_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string seat = 2;</code>
       * @return The bytes for seat.
       */
      public com.google.protobuf.ByteString
          getSeatBytes() {
        Object ref = seat_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          seat_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string seat = 2;</code>
       * @param value The seat to set.
       * @return This builder for chaining.
       */
      public Builder setSeat(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        seat_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string seat = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeat() {
        seat_ = getDefaultInstance().getSeat();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string seat = 2;</code>
       * @param value The bytes for seat to set.
       * @return This builder for chaining.
       */
      public Builder setSeatBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        seat_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:dsp.BidResponse.SeatBid)
    }

    // @@protoc_insertion_point(class_scope:dsp.BidResponse.SeatBid)
    private static final SeatBid DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new SeatBid();
    }

    public static SeatBid getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SeatBid>
        PARSER = new com.google.protobuf.AbstractParser<SeatBid>() {
      @Override
      public SeatBid parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SeatBid> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<SeatBid> getParserForType() {
      return PARSER;
    }

    @Override
    public SeatBid getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile Object id_ = "";
  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  @Override
  public String getId() {
    Object ref = id_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    Object ref = id_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SEATBID_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<SeatBid> seatbid_;
  /**
   * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
   */
  @Override
  public java.util.List<SeatBid> getSeatbidList() {
    return seatbid_;
  }
  /**
   * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
   */
  @Override
  public java.util.List<? extends SeatBidOrBuilder>
      getSeatbidOrBuilderList() {
    return seatbid_;
  }
  /**
   * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
   */
  @Override
  public int getSeatbidCount() {
    return seatbid_.size();
  }
  /**
   * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
   */
  @Override
  public SeatBid getSeatbid(int index) {
    return seatbid_.get(index);
  }
  /**
   * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
   */
  @Override
  public SeatBidOrBuilder getSeatbidOrBuilder(
      int index) {
    return seatbid_.get(index);
  }

  public static final int BIDID_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile Object bidid_ = "";
  /**
   * <code>string bidid = 3;</code>
   * @return The bidid.
   */
  @Override
  public String getBidid() {
    Object ref = bidid_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      bidid_ = s;
      return s;
    }
  }
  /**
   * <code>string bidid = 3;</code>
   * @return The bytes for bidid.
   */
  @Override
  public com.google.protobuf.ByteString
      getBididBytes() {
    Object ref = bidid_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      bidid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ERRORCODE_FIELD_NUMBER = 5;
  private long errorcode_ = 0L;
  /**
   * <code>int64 errorcode = 5;</code>
   * @return The errorcode.
   */
  @Override
  public long getErrorcode() {
    return errorcode_;
  }

  public static final int MSG_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile Object msg_ = "";
  /**
   * <code>string msg = 6;</code>
   * @return The msg.
   */
  @Override
  public String getMsg() {
    Object ref = msg_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      msg_ = s;
      return s;
    }
  }
  /**
   * <code>string msg = 6;</code>
   * @return The bytes for msg.
   */
  @Override
  public com.google.protobuf.ByteString
      getMsgBytes() {
    Object ref = msg_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      msg_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, id_);
    }
    for (int i = 0; i < seatbid_.size(); i++) {
      output.writeMessage(2, seatbid_.get(i));
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bidid_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, bidid_);
    }
    if (errorcode_ != 0L) {
      output.writeInt64(5, errorcode_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(msg_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, msg_);
    }
    getUnknownFields().writeTo(output);
  }

  @Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, id_);
    }
    for (int i = 0; i < seatbid_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, seatbid_.get(i));
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bidid_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, bidid_);
    }
    if (errorcode_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, errorcode_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(msg_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, msg_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof BidResponse)) {
      return super.equals(obj);
    }
    BidResponse other = (BidResponse) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getSeatbidList()
        .equals(other.getSeatbidList())) return false;
    if (!getBidid()
        .equals(other.getBidid())) return false;
    if (getErrorcode()
        != other.getErrorcode()) return false;
    if (!getMsg()
        .equals(other.getMsg())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    if (getSeatbidCount() > 0) {
      hash = (37 * hash) + SEATBID_FIELD_NUMBER;
      hash = (53 * hash) + getSeatbidList().hashCode();
    }
    hash = (37 * hash) + BIDID_FIELD_NUMBER;
    hash = (53 * hash) + getBidid().hashCode();
    hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getErrorcode());
    hash = (37 * hash) + MSG_FIELD_NUMBER;
    hash = (53 * hash) + getMsg().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static BidResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static BidResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static BidResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static BidResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static BidResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static BidResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static BidResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static BidResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static BidResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static BidResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static BidResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static BidResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(BidResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code dsp.BidResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:dsp.BidResponse)
      BidResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return HaiLiangDto.internal_static_dsp_BidResponse_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return HaiLiangDto.internal_static_dsp_BidResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              BidResponse.class, Builder.class);
    }

    // Construct using cn.taken.ad.logic.prossor.hailiang.dto.BidResponse.newBuilder()
    private Builder() {

    }

    private Builder(
        BuilderParent parent) {
      super(parent);

    }
    @Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      if (seatbidBuilder_ == null) {
        seatbid_ = java.util.Collections.emptyList();
      } else {
        seatbid_ = null;
        seatbidBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      bidid_ = "";
      errorcode_ = 0L;
      msg_ = "";
      return this;
    }

    @Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return HaiLiangDto.internal_static_dsp_BidResponse_descriptor;
    }

    @Override
    public BidResponse getDefaultInstanceForType() {
      return BidResponse.getDefaultInstance();
    }

    @Override
    public BidResponse build() {
      BidResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @Override
    public BidResponse buildPartial() {
      BidResponse result = new BidResponse(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(BidResponse result) {
      if (seatbidBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          seatbid_ = java.util.Collections.unmodifiableList(seatbid_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.seatbid_ = seatbid_;
      } else {
        result.seatbid_ = seatbidBuilder_.build();
      }
    }

    private void buildPartial0(BidResponse result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.bidid_ = bidid_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.errorcode_ = errorcode_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.msg_ = msg_;
      }
    }

    @Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof BidResponse) {
        return mergeFrom((BidResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(BidResponse other) {
      if (other == BidResponse.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (seatbidBuilder_ == null) {
        if (!other.seatbid_.isEmpty()) {
          if (seatbid_.isEmpty()) {
            seatbid_ = other.seatbid_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureSeatbidIsMutable();
            seatbid_.addAll(other.seatbid_);
          }
          onChanged();
        }
      } else {
        if (!other.seatbid_.isEmpty()) {
          if (seatbidBuilder_.isEmpty()) {
            seatbidBuilder_.dispose();
            seatbidBuilder_ = null;
            seatbid_ = other.seatbid_;
            bitField0_ = (bitField0_ & ~0x00000002);
            seatbidBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 getSeatbidFieldBuilder() : null;
          } else {
            seatbidBuilder_.addAllMessages(other.seatbid_);
          }
        }
      }
      if (!other.getBidid().isEmpty()) {
        bidid_ = other.bidid_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.getErrorcode() != 0L) {
        setErrorcode(other.getErrorcode());
      }
      if (!other.getMsg().isEmpty()) {
        msg_ = other.msg_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @Override
    public final boolean isInitialized() {
      return true;
    }

    @Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              SeatBid m =
                  input.readMessage(
                      SeatBid.parser(),
                      extensionRegistry);
              if (seatbidBuilder_ == null) {
                ensureSeatbidIsMutable();
                seatbid_.add(m);
              } else {
                seatbidBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              bidid_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 40: {
              errorcode_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 40
            case 50: {
              msg_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    public String getId() {
      Object ref = id_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.util.List<SeatBid> seatbid_ =
      java.util.Collections.emptyList();
    private void ensureSeatbidIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        seatbid_ = new java.util.ArrayList<SeatBid>(seatbid_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        SeatBid, SeatBid.Builder, SeatBidOrBuilder> seatbidBuilder_;

    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public java.util.List<SeatBid> getSeatbidList() {
      if (seatbidBuilder_ == null) {
        return java.util.Collections.unmodifiableList(seatbid_);
      } else {
        return seatbidBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public int getSeatbidCount() {
      if (seatbidBuilder_ == null) {
        return seatbid_.size();
      } else {
        return seatbidBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public SeatBid getSeatbid(int index) {
      if (seatbidBuilder_ == null) {
        return seatbid_.get(index);
      } else {
        return seatbidBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public Builder setSeatbid(
        int index, SeatBid value) {
      if (seatbidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSeatbidIsMutable();
        seatbid_.set(index, value);
        onChanged();
      } else {
        seatbidBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public Builder setSeatbid(
        int index, SeatBid.Builder builderForValue) {
      if (seatbidBuilder_ == null) {
        ensureSeatbidIsMutable();
        seatbid_.set(index, builderForValue.build());
        onChanged();
      } else {
        seatbidBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public Builder addSeatbid(SeatBid value) {
      if (seatbidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSeatbidIsMutable();
        seatbid_.add(value);
        onChanged();
      } else {
        seatbidBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public Builder addSeatbid(
        int index, SeatBid value) {
      if (seatbidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSeatbidIsMutable();
        seatbid_.add(index, value);
        onChanged();
      } else {
        seatbidBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public Builder addSeatbid(
        SeatBid.Builder builderForValue) {
      if (seatbidBuilder_ == null) {
        ensureSeatbidIsMutable();
        seatbid_.add(builderForValue.build());
        onChanged();
      } else {
        seatbidBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public Builder addSeatbid(
        int index, SeatBid.Builder builderForValue) {
      if (seatbidBuilder_ == null) {
        ensureSeatbidIsMutable();
        seatbid_.add(index, builderForValue.build());
        onChanged();
      } else {
        seatbidBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public Builder addAllSeatbid(
        Iterable<? extends SeatBid> values) {
      if (seatbidBuilder_ == null) {
        ensureSeatbidIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, seatbid_);
        onChanged();
      } else {
        seatbidBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public Builder clearSeatbid() {
      if (seatbidBuilder_ == null) {
        seatbid_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        seatbidBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public Builder removeSeatbid(int index) {
      if (seatbidBuilder_ == null) {
        ensureSeatbidIsMutable();
        seatbid_.remove(index);
        onChanged();
      } else {
        seatbidBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public SeatBid.Builder getSeatbidBuilder(
        int index) {
      return getSeatbidFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public SeatBidOrBuilder getSeatbidOrBuilder(
        int index) {
      if (seatbidBuilder_ == null) {
        return seatbid_.get(index);  } else {
        return seatbidBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public java.util.List<? extends SeatBidOrBuilder>
         getSeatbidOrBuilderList() {
      if (seatbidBuilder_ != null) {
        return seatbidBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(seatbid_);
      }
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public SeatBid.Builder addSeatbidBuilder() {
      return getSeatbidFieldBuilder().addBuilder(
          SeatBid.getDefaultInstance());
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public SeatBid.Builder addSeatbidBuilder(
        int index) {
      return getSeatbidFieldBuilder().addBuilder(
          index, SeatBid.getDefaultInstance());
    }
    /**
     * <code>repeated .dsp.BidResponse.SeatBid seatbid = 2;</code>
     */
    public java.util.List<SeatBid.Builder>
         getSeatbidBuilderList() {
      return getSeatbidFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        SeatBid, SeatBid.Builder, SeatBidOrBuilder>
        getSeatbidFieldBuilder() {
      if (seatbidBuilder_ == null) {
        seatbidBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            SeatBid, SeatBid.Builder, SeatBidOrBuilder>(
                seatbid_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        seatbid_ = null;
      }
      return seatbidBuilder_;
    }

    private Object bidid_ = "";
    /**
     * <code>string bidid = 3;</code>
     * @return The bidid.
     */
    public String getBidid() {
      Object ref = bidid_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        bidid_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string bidid = 3;</code>
     * @return The bytes for bidid.
     */
    public com.google.protobuf.ByteString
        getBididBytes() {
      Object ref = bidid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        bidid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string bidid = 3;</code>
     * @param value The bidid to set.
     * @return This builder for chaining.
     */
    public Builder setBidid(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      bidid_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string bidid = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearBidid() {
      bidid_ = getDefaultInstance().getBidid();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string bidid = 3;</code>
     * @param value The bytes for bidid to set.
     * @return This builder for chaining.
     */
    public Builder setBididBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      bidid_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private long errorcode_ ;
    /**
     * <code>int64 errorcode = 5;</code>
     * @return The errorcode.
     */
    @Override
    public long getErrorcode() {
      return errorcode_;
    }
    /**
     * <code>int64 errorcode = 5;</code>
     * @param value The errorcode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorcode(long value) {

      errorcode_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>int64 errorcode = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorcode() {
      bitField0_ = (bitField0_ & ~0x00000008);
      errorcode_ = 0L;
      onChanged();
      return this;
    }

    private Object msg_ = "";
    /**
     * <code>string msg = 6;</code>
     * @return The msg.
     */
    public String getMsg() {
      Object ref = msg_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        msg_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string msg = 6;</code>
     * @return The bytes for msg.
     */
    public com.google.protobuf.ByteString
        getMsgBytes() {
      Object ref = msg_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string msg = 6;</code>
     * @param value The msg to set.
     * @return This builder for chaining.
     */
    public Builder setMsg(
        String value) {
      if (value == null) { throw new NullPointerException(); }
      msg_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>string msg = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearMsg() {
      msg_ = getDefaultInstance().getMsg();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>string msg = 6;</code>
     * @param value The bytes for msg to set.
     * @return This builder for chaining.
     */
    public Builder setMsgBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      msg_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:dsp.BidResponse)
  }

  // @@protoc_insertion_point(class_scope:dsp.BidResponse)
  private static final BidResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new BidResponse();
  }

  public static BidResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BidResponse>
      PARSER = new com.google.protobuf.AbstractParser<BidResponse>() {
    @Override
    public BidResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BidResponse> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<BidResponse> getParserForType() {
    return PARSER;
  }

  @Override
  public BidResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

