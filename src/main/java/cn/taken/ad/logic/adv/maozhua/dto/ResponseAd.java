package cn.taken.ad.logic.adv.maozhua.dto;

import java.io.Serializable;
import java.util.List;

public class ResponseAd implements Serializable {

    private String imp_id ;//required string 对应请求中的 imp_id
    private Integer price;// optional int 出价，cpm价格，单位：分,竞价模式返回
    //nurl optional string 竟胜通知URL （后续版本中已经移到trackers中）
    private Integer action_type;// required int 广告点击后的交互类型 1 - 点击打开落地页 2 - 点击下载广告 3 - 唤醒 APP 4-广点通 5-纯曝光广告 6-小程序广告
    private String click_url;// required string 广告点击链接 广告点击时可能需要采集用户的点击坐 标，详情参见附录。 优 先 使 用
    private UrlWithHeader click_url_with_header;// 进 行 跳 转 ， 若 click_url_with_header为空，则使用本项 click_url_with_header optional UrlWithHeader 广告点击跳转落地页，可以支持重定向-14里面可能会有 宏参数替换
    private String app_url;//
    private String deep_link;// optional string 唤醒链接地址 如果 action_type=3， 优先使用 deep_link调起 APP， 如 果不支持 deeplink或调起 失败，则使用 click_url作 为落地页地址。
    private String html;// optional string H5 广告返回字段
    private Integer is_gdt;// optional int 如果为广点通广告，则返回 1
    private String logo;// optional string 广告图标，如百度的熊掌图标
    private String title;// optional string 广告标题
    private String desc;// optional string 广告描述
    private String image;// optional string 素材图片url
    private String mask_image;// optional string 富媒体遮罩效果图 (部分富媒体⼴告会有该字段，⽐如 Gallery)
    private String slogan;// optional string 富媒体可点击标语 (富媒体⼴告会有该字段)。
    private String btn_text;// optional string 交互按钮文字
    private Integer enable_shake;// optional int 是否使⽤摇⼀摇， 0(默认) - 不使⽤， 1 - 使⽤； 注意：摇一摇功能和全屏点击功能互斥
    private Integer method_type;// optional int 上报地址请求方法类型: 0:get请求; 1：post请求， 该值为0或者不存在默认为get请求
    private Creative creative;// optional Creative 广告创意的文本图片 video optional
    private Video video;// 视频广告
    private List<Audio> audios;// optional list<Audio> 音频广告
    private ResponseApp app;// optional App 下载应用信息
    private Mp mp;// optional Mp 微信小程序
    private List<Tracker> trackers;// required list<Tracker> 监测对象列表，具体监测类型参考 Tracker 对象列表
    private List<VideoExtTracker> video_ext_trackers ;//optional list<VideoExtTracker> 视频播放额外检测Tracker对象列表
    private AdClickAreaReport click_area_report;// optional AdClickAreaReport 点击坐标打点上报，上报方法见3.2, 部分预算可能返回

    public String getImp_id() {
        return imp_id;
    }

    public void setImp_id(String imp_id) {
        this.imp_id = imp_id;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getAction_type() {
        return action_type;
    }

    public void setAction_type(Integer action_type) {
        this.action_type = action_type;
    }

    public void setIs_gdt(Integer is_gdt) {
        this.is_gdt = is_gdt;
    }

    public void setEnable_shake(Integer enable_shake) {
        this.enable_shake = enable_shake;
    }

    public void setMethod_type(Integer method_type) {
        this.method_type = method_type;
    }

    public String getClick_url() {
        return click_url;
    }

    public void setClick_url(String click_url) {
        this.click_url = click_url;
    }

    public UrlWithHeader getClick_url_with_header() {
        return click_url_with_header;
    }

    public void setClick_url_with_header(UrlWithHeader click_url_with_header) {
        this.click_url_with_header = click_url_with_header;
    }

    public String getApp_url() {
        return app_url;
    }

    public void setApp_url(String app_url) {
        this.app_url = app_url;
    }

    public String getDeep_link() {
        return deep_link;
    }

    public void setDeep_link(String deep_link) {
        this.deep_link = deep_link;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }


    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getMask_image() {
        return mask_image;
    }

    public void setMask_image(String mask_image) {
        this.mask_image = mask_image;
    }

    public String getSlogan() {
        return slogan;
    }

    public void setSlogan(String slogan) {
        this.slogan = slogan;
    }

    public String getBtn_text() {
        return btn_text;
    }

    public void setBtn_text(String btn_text) {
        this.btn_text = btn_text;
    }

    public Integer getIs_gdt() {
        return is_gdt;
    }

    public Integer getEnable_shake() {
        return enable_shake;
    }

    public Integer getMethod_type() {
        return method_type;
    }

    public Creative getCreative() {
        return creative;
    }

    public void setCreative(Creative creative) {
        this.creative = creative;
    }

    public Video getVideo() {
        return video;
    }

    public void setVideo(Video video) {
        this.video = video;
    }

    public List<Audio> getAudios() {
        return audios;
    }

    public void setAudios(List<Audio> audios) {
        this.audios = audios;
    }

    public ResponseApp getApp() {
        return app;
    }

    public void setApp(ResponseApp app) {
        this.app = app;
    }

    public Mp getMp() {
        return mp;
    }

    public void setMp(Mp mp) {
        this.mp = mp;
    }

    public List<Tracker> getTrackers() {
        return trackers;
    }

    public void setTrackers(List<Tracker> trackers) {
        this.trackers = trackers;
    }

    public List<VideoExtTracker> getVideo_ext_trackers() {
        return video_ext_trackers;
    }

    public void setVideo_ext_trackers(List<VideoExtTracker> video_ext_trackers) {
        this.video_ext_trackers = video_ext_trackers;
    }

    public AdClickAreaReport getClick_area_report() {
        return click_area_report;
    }

    public void setClick_area_report(AdClickAreaReport click_area_report) {
        this.click_area_report = click_area_report;
    }
}
