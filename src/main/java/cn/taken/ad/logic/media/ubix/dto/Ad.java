// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: ubix.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.media.ubix.dto;

/**
 * Protobuf type {@code Ad}
 */
public final class Ad extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Ad)
    AdOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      Ad.class.getName());
  }
  // Use Ad.newBuilder() to construct.
  private Ad(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private Ad() {
    adId_ = "";
    ext_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.taken.ad.logic.media.ubix.dto.Ad.class, cn.taken.ad.logic.media.ubix.dto.Ad.Builder.class);
  }

  public interface MaterialMetaOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Ad.MaterialMeta)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 创意的唯一标志
     * </pre>
     *
     * <code>string creative_id = 1;</code>
     * @return The creativeId.
     */
    java.lang.String getCreativeId();
    /**
     * <pre>
     * 创意的唯一标志
     * </pre>
     *
     * <code>string creative_id = 1;</code>
     * @return The bytes for creativeId.
     */
    com.google.protobuf.ByteString
        getCreativeIdBytes();

    /**
     * <pre>
     * 必填。该广告的创意类型，和Adslot对应。1：单图，2：多图，3：视频
     * </pre>
     *
     * <code>int32 creative_type = 2;</code>
     * @return The creativeType.
     */
    int getCreativeType();

    /**
     * <pre>
     * 广告支持的交互类型，和Adslot对象。3：应用内打开 4：download
     * </pre>
     *
     * <code>int32 interaction_type = 3;</code>
     * @return The interactionType.
     */
    int getInteractionType();

    /**
     * <pre>
     * 可选。广告标题。信息流必填
     * </pre>
     *
     * <code>string title = 4;</code>
     * @return The title.
     */
    java.lang.String getTitle();
    /**
     * <pre>
     * 可选。广告标题。信息流必填
     * </pre>
     *
     * <code>string title = 4;</code>
     * @return The bytes for title.
     */
    com.google.protobuf.ByteString
        getTitleBytes();

    /**
     * <pre>
     * 可选。广告描述。信息流必填
     * </pre>
     *
     * <code>string description = 5;</code>
     * @return The description.
     */
    java.lang.String getDescription();
    /**
     * <pre>
     * 可选。广告描述。信息流必填
     * </pre>
     *
     * <code>string description = 5;</code>
     * @return The bytes for description.
     */
    com.google.protobuf.ByteString
        getDescriptionBytes();

    /**
     * <pre>
     * 可选。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_name = 6;</code>
     * @return The appName.
     */
    java.lang.String getAppName();
    /**
     * <pre>
     * 可选。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_name = 6;</code>
     * @return The bytes for appName.
     */
    com.google.protobuf.ByteString
        getAppNameBytes();

    /**
     * <pre>
     * 下载类广告（包括deeplink）。IOS填写bundleID，Android填写包名
     * </pre>
     *
     * <code>string package_name = 7;</code>
     * @return The packageName.
     */
    java.lang.String getPackageName();
    /**
     * <pre>
     * 下载类广告（包括deeplink）。IOS填写bundleID，Android填写包名
     * </pre>
     *
     * <code>string package_name = 7;</code>
     * @return The bytes for packageName.
     */
    com.google.protobuf.ByteString
        getPackageNameBytes();

    /**
     * <pre>
     * 广告创意的图标URL
     * </pre>
     *
     * <code>string icon = 8;</code>
     * @return The icon.
     */
    java.lang.String getIcon();
    /**
     * <pre>
     * 广告创意的图标URL
     * </pre>
     *
     * <code>string icon = 8;</code>
     * @return The bytes for icon.
     */
    com.google.protobuf.ByteString
        getIconBytes();

    /**
     * <pre>
     * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
     */
    java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image> 
        getImageList();
    /**
     * <pre>
     * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
     */
    cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image getImage(int index);
    /**
     * <pre>
     * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
     */
    int getImageCount();
    /**
     * <pre>
     * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
     */
    java.util.List<? extends cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.ImageOrBuilder> 
        getImageOrBuilderList();
    /**
     * <pre>
     * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
     */
    cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.ImageOrBuilder getImageOrBuilder(
        int index);

    /**
     * <pre>
     * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
     * </pre>
     *
     * <code>.Ad.MaterialMeta.Video video = 10;</code>
     * @return Whether the video field is set.
     */
    boolean hasVideo();
    /**
     * <pre>
     * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
     * </pre>
     *
     * <code>.Ad.MaterialMeta.Video video = 10;</code>
     * @return The video.
     */
    cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video getVideo();
    /**
     * <pre>
     * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
     * </pre>
     *
     * <code>.Ad.MaterialMeta.Video video = 10;</code>
     */
    cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.VideoOrBuilder getVideoOrBuilder();

    /**
     * <pre>
     * 可选。点击创意的响应地址url。
     * </pre>
     *
     * <code>string target_url = 12;</code>
     * @return The targetUrl.
     */
    java.lang.String getTargetUrl();
    /**
     * <pre>
     * 可选。点击创意的响应地址url。
     * </pre>
     *
     * <code>string target_url = 12;</code>
     * @return The bytes for targetUrl.
     */
    com.google.protobuf.ByteString
        getTargetUrlBytes();

    /**
     * <pre>
     * 应用直接下载url，只Android流量。
     * </pre>
     *
     * <code>string download_url = 13;</code>
     * @return The downloadUrl.
     */
    java.lang.String getDownloadUrl();
    /**
     * <pre>
     * 应用直接下载url，只Android流量。
     * </pre>
     *
     * <code>string download_url = 13;</code>
     * @return The bytes for downloadUrl.
     */
    com.google.protobuf.ByteString
        getDownloadUrlBytes();

    /**
     * <pre>
     * 应用吊起的直达链接。如果该url非空，优先使用该直达链接
     * </pre>
     *
     * <code>string deeplink_url = 14;</code>
     * @return The deeplinkUrl.
     */
    java.lang.String getDeeplinkUrl();
    /**
     * <pre>
     * 应用吊起的直达链接。如果该url非空，优先使用该直达链接
     * </pre>
     *
     * <code>string deeplink_url = 14;</code>
     * @return The bytes for deeplinkUrl.
     */
    com.google.protobuf.ByteString
        getDeeplinkUrlBytes();

    /**
     * <pre>
     * 用于各种打点的url
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
     */
    java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking> 
        getTrackingEventList();
    /**
     * <pre>
     * 用于各种打点的url
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
     */
    cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking getTrackingEvent(int index);
    /**
     * <pre>
     * 用于各种打点的url
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
     */
    int getTrackingEventCount();
    /**
     * <pre>
     * 用于各种打点的url
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
     */
    java.util.List<? extends cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.TrackingOrBuilder> 
        getTrackingEventOrBuilderList();
    /**
     * <pre>
     * 用于各种打点的url
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
     */
    cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.TrackingOrBuilder getTrackingEventOrBuilder(
        int index);

    /**
     * <pre>
     * 竞价模式时， win notice填写在这里
     * </pre>
     *
     * <code>repeated string win_notice_url = 16;</code>
     * @return A list containing the winNoticeUrl.
     */
    java.util.List<java.lang.String>
        getWinNoticeUrlList();
    /**
     * <pre>
     * 竞价模式时， win notice填写在这里
     * </pre>
     *
     * <code>repeated string win_notice_url = 16;</code>
     * @return The count of winNoticeUrl.
     */
    int getWinNoticeUrlCount();
    /**
     * <pre>
     * 竞价模式时， win notice填写在这里
     * </pre>
     *
     * <code>repeated string win_notice_url = 16;</code>
     * @param index The index of the element to return.
     * @return The winNoticeUrl at the given index.
     */
    java.lang.String getWinNoticeUrl(int index);
    /**
     * <pre>
     * 竞价模式时， win notice填写在这里
     * </pre>
     *
     * <code>repeated string win_notice_url = 16;</code>
     * @param index The index of the value to return.
     * @return The bytes of the winNoticeUrl at the given index.
     */
    com.google.protobuf.ByteString
        getWinNoticeUrlBytes(int index);

    /**
     * <pre>
     * 在PD合作模式下返回
     * </pre>
     *
     * <code>string deal_id = 17;</code>
     * @return The dealId.
     */
    java.lang.String getDealId();
    /**
     * <pre>
     * 在PD合作模式下返回
     * </pre>
     *
     * <code>string deal_id = 17;</code>
     * @return The bytes for dealId.
     */
    com.google.protobuf.ByteString
        getDealIdBytes();

    /**
     * <pre>
     * 小程序对象
     * </pre>
     *
     * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
     * @return Whether the mp field is set.
     */
    boolean hasMp();
    /**
     * <pre>
     * 小程序对象
     * </pre>
     *
     * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
     * @return The mp.
     */
    cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo getMp();
    /**
     * <pre>
     * 小程序对象
     * </pre>
     *
     * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
     */
    cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfoOrBuilder getMpOrBuilder();

    /**
     * <pre>
     * 广告来源
     * </pre>
     *
     * <code>string source = 20;</code>
     * @return The source.
     */
    java.lang.String getSource();
    /**
     * <pre>
     * 广告来源
     * </pre>
     *
     * <code>string source = 20;</code>
     * @return The bytes for source.
     */
    com.google.protobuf.ByteString
        getSourceBytes();

    /**
     * <pre>
     * ios端调起连接 universal_link优先级高于deeplink
     * </pre>
     *
     * <code>string universal_link = 21;</code>
     * @return The universalLink.
     */
    java.lang.String getUniversalLink();
    /**
     * <pre>
     * ios端调起连接 universal_link优先级高于deeplink
     * </pre>
     *
     * <code>string universal_link = 21;</code>
     * @return The bytes for universalLink.
     */
    com.google.protobuf.ByteString
        getUniversalLinkBytes();

    /**
     * <pre>
     * DSP在应用商店推广应用的时候，应用商店分配给DSP的渠道号
     * </pre>
     *
     * <code>string app_channel = 22;</code>
     * @return The appChannel.
     */
    java.lang.String getAppChannel();
    /**
     * <pre>
     * DSP在应用商店推广应用的时候，应用商店分配给DSP的渠道号
     * </pre>
     *
     * <code>string app_channel = 22;</code>
     * @return The bytes for appChannel.
     */
    com.google.protobuf.ByteString
        getAppChannelBytes();

    /**
     * <pre>
     * 可选。应用介绍。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_desc = 23;</code>
     * @return The appDesc.
     */
    java.lang.String getAppDesc();
    /**
     * <pre>
     * 可选。应用介绍。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_desc = 23;</code>
     * @return The bytes for appDesc.
     */
    com.google.protobuf.ByteString
        getAppDescBytes();

    /**
     * <pre>
     * 可选。应用大小，单位MB。针对应用下载类广告。
     * </pre>
     *
     * <code>float app_package_size = 24;</code>
     * @return The appPackageSize.
     */
    float getAppPackageSize();

    /**
     * <pre>
     * 可选。APP开发者主体名称。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_publisher = 25;</code>
     * @return The appPublisher.
     */
    java.lang.String getAppPublisher();
    /**
     * <pre>
     * 可选。APP开发者主体名称。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_publisher = 25;</code>
     * @return The bytes for appPublisher.
     */
    com.google.protobuf.ByteString
        getAppPublisherBytes();

    /**
     * <pre>
     * 可选。下载APP版本号。针对应用下载类广告。
     * </pre>
     *
     * <code>string down_app_version = 26;</code>
     * @return The downAppVersion.
     */
    java.lang.String getDownAppVersion();
    /**
     * <pre>
     * 可选。下载APP版本号。针对应用下载类广告。
     * </pre>
     *
     * <code>string down_app_version = 26;</code>
     * @return The bytes for downAppVersion.
     */
    com.google.protobuf.ByteString
        getDownAppVersionBytes();

    /**
     * <pre>
     * 可选。隐私协议URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string privacy_link = 27;</code>
     * @return The privacyLink.
     */
    java.lang.String getPrivacyLink();
    /**
     * <pre>
     * 可选。隐私协议URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string privacy_link = 27;</code>
     * @return The bytes for privacyLink.
     */
    com.google.protobuf.ByteString
        getPrivacyLinkBytes();

    /**
     * <pre>
     * 可选。用户权限URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string permission_link = 28;</code>
     * @return The permissionLink.
     */
    java.lang.String getPermissionLink();
    /**
     * <pre>
     * 可选。用户权限URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string permission_link = 28;</code>
     * @return The bytes for permissionLink.
     */
    com.google.protobuf.ByteString
        getPermissionLinkBytes();

    /**
     * <pre>
     * 可选。应用介绍URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string introduce_link = 29;</code>
     * @return The introduceLink.
     */
    java.lang.String getIntroduceLink();
    /**
     * <pre>
     * 可选。应用介绍URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string introduce_link = 29;</code>
     * @return The bytes for introduceLink.
     */
    com.google.protobuf.ByteString
        getIntroduceLinkBytes();

    /**
     * <pre>
     * 可选。下载应用备案号 LCP。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_lcp_number = 30;</code>
     * @return The appLcpNumber.
     */
    java.lang.String getAppLcpNumber();
    /**
     * <pre>
     * 可选。下载应用备案号 LCP。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_lcp_number = 30;</code>
     * @return The bytes for appLcpNumber.
     */
    com.google.protobuf.ByteString
        getAppLcpNumberBytes();

    /**
     * <pre>
     * 可选。下载应用实用年龄。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_suitable_age = 31;</code>
     * @return The appSuitableAge.
     */
    java.lang.String getAppSuitableAge();
    /**
     * <pre>
     * 可选。下载应用实用年龄。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_suitable_age = 31;</code>
     * @return The bytes for appSuitableAge.
     */
    com.google.protobuf.ByteString
        getAppSuitableAgeBytes();

    /**
     * <pre>
     * 点击坐标替换类型 0：浮点型；1：整型；
     * </pre>
     *
     * <code>int32 replacement_type = 32;</code>
     * @return The replacementType.
     */
    int getReplacementType();
  }
  /**
   * Protobuf type {@code Ad.MaterialMeta}
   */
  public static final class MaterialMeta extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Ad.MaterialMeta)
      MaterialMetaOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        MaterialMeta.class.getName());
    }
    // Use MaterialMeta.newBuilder() to construct.
    private MaterialMeta(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MaterialMeta() {
      creativeId_ = "";
      title_ = "";
      description_ = "";
      appName_ = "";
      packageName_ = "";
      icon_ = "";
      image_ = java.util.Collections.emptyList();
      targetUrl_ = "";
      downloadUrl_ = "";
      deeplinkUrl_ = "";
      trackingEvent_ = java.util.Collections.emptyList();
      winNoticeUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      dealId_ = "";
      source_ = "";
      universalLink_ = "";
      appChannel_ = "";
      appDesc_ = "";
      appPublisher_ = "";
      downAppVersion_ = "";
      privacyLink_ = "";
      permissionLink_ = "";
      introduceLink_ = "";
      appLcpNumber_ = "";
      appSuitableAge_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.class, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Builder.class);
    }

    public interface ImageOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Ad.MaterialMeta.Image)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 图片地址
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The url.
       */
      java.lang.String getUrl();
      /**
       * <pre>
       * 图片地址
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The bytes for url.
       */
      com.google.protobuf.ByteString
          getUrlBytes();

      /**
       * <pre>
       * 宽度，单位：像素
       * </pre>
       *
       * <code>uint32 width = 2;</code>
       * @return The width.
       */
      int getWidth();

      /**
       * <pre>
       * 高度，单位：像素
       * </pre>
       *
       * <code>uint32 height = 3;</code>
       * @return The height.
       */
      int getHeight();
    }
    /**
     * <pre>
     * 图片素材信息。
     * </pre>
     *
     * Protobuf type {@code Ad.MaterialMeta.Image}
     */
    public static final class Image extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:Ad.MaterialMeta.Image)
        ImageOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 28,
          /* patch= */ 3,
          /* suffix= */ "",
          Image.class.getName());
      }
      // Use Image.newBuilder() to construct.
      private Image(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private Image() {
        url_ = "";
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Image_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Image_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.class, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder.class);
      }

      public static final int URL_FIELD_NUMBER = 1;
      @SuppressWarnings("serial")
      private volatile java.lang.Object url_ = "";
      /**
       * <pre>
       * 图片地址
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The url.
       */
      @java.lang.Override
      public java.lang.String getUrl() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          url_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 图片地址
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The bytes for url.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getUrlBytes() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          url_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int WIDTH_FIELD_NUMBER = 2;
      private int width_ = 0;
      /**
       * <pre>
       * 宽度，单位：像素
       * </pre>
       *
       * <code>uint32 width = 2;</code>
       * @return The width.
       */
      @java.lang.Override
      public int getWidth() {
        return width_;
      }

      public static final int HEIGHT_FIELD_NUMBER = 3;
      private int height_ = 0;
      /**
       * <pre>
       * 高度，单位：像素
       * </pre>
       *
       * <code>uint32 height = 3;</code>
       * @return The height.
       */
      @java.lang.Override
      public int getHeight() {
        return height_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 1, url_);
        }
        if (width_ != 0) {
          output.writeUInt32(2, width_);
        }
        if (height_ != 0) {
          output.writeUInt32(3, height_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(1, url_);
        }
        if (width_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(2, width_);
        }
        if (height_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(3, height_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image)) {
          return super.equals(obj);
        }
        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image other = (cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image) obj;

        if (!getUrl()
            .equals(other.getUrl())) return false;
        if (getWidth()
            != other.getWidth()) return false;
        if (getHeight()
            != other.getHeight()) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + URL_FIELD_NUMBER;
        hash = (53 * hash) + getUrl().hashCode();
        hash = (37 * hash) + WIDTH_FIELD_NUMBER;
        hash = (53 * hash) + getWidth();
        hash = (37 * hash) + HEIGHT_FIELD_NUMBER;
        hash = (53 * hash) + getHeight();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * 图片素材信息。
       * </pre>
       *
       * Protobuf type {@code Ad.MaterialMeta.Image}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Ad.MaterialMeta.Image)
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.ImageOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Image_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Image_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.class, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder.class);
        }

        // Construct using cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          url_ = "";
          width_ = 0;
          height_ = 0;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Image_descriptor;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image getDefaultInstanceForType() {
          return cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.getDefaultInstance();
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image build() {
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image buildPartial() {
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image result = new cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.url_ = url_;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.width_ = width_;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.height_ = height_;
          }
        }

        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image) {
            return mergeFrom((cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image other) {
          if (other == cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.getDefaultInstance()) return this;
          if (!other.getUrl().isEmpty()) {
            url_ = other.url_;
            bitField0_ |= 0x00000001;
            onChanged();
          }
          if (other.getWidth() != 0) {
            setWidth(other.getWidth());
          }
          if (other.getHeight() != 0) {
            setHeight(other.getHeight());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  url_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 10
                case 16: {
                  width_ = input.readUInt32();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                case 24: {
                  height_ = input.readUInt32();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private java.lang.Object url_ = "";
        /**
         * <pre>
         * 图片地址
         * </pre>
         *
         * <code>string url = 1;</code>
         * @return The url.
         */
        public java.lang.String getUrl() {
          java.lang.Object ref = url_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            url_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 图片地址
         * </pre>
         *
         * <code>string url = 1;</code>
         * @return The bytes for url.
         */
        public com.google.protobuf.ByteString
            getUrlBytes() {
          java.lang.Object ref = url_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            url_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 图片地址
         * </pre>
         *
         * <code>string url = 1;</code>
         * @param value The url to set.
         * @return This builder for chaining.
         */
        public Builder setUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          url_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 图片地址
         * </pre>
         *
         * <code>string url = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearUrl() {
          url_ = getDefaultInstance().getUrl();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 图片地址
         * </pre>
         *
         * <code>string url = 1;</code>
         * @param value The bytes for url to set.
         * @return This builder for chaining.
         */
        public Builder setUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          url_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }

        private int width_ ;
        /**
         * <pre>
         * 宽度，单位：像素
         * </pre>
         *
         * <code>uint32 width = 2;</code>
         * @return The width.
         */
        @java.lang.Override
        public int getWidth() {
          return width_;
        }
        /**
         * <pre>
         * 宽度，单位：像素
         * </pre>
         *
         * <code>uint32 width = 2;</code>
         * @param value The width to set.
         * @return This builder for chaining.
         */
        public Builder setWidth(int value) {

          width_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 宽度，单位：像素
         * </pre>
         *
         * <code>uint32 width = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearWidth() {
          bitField0_ = (bitField0_ & ~0x00000002);
          width_ = 0;
          onChanged();
          return this;
        }

        private int height_ ;
        /**
         * <pre>
         * 高度，单位：像素
         * </pre>
         *
         * <code>uint32 height = 3;</code>
         * @return The height.
         */
        @java.lang.Override
        public int getHeight() {
          return height_;
        }
        /**
         * <pre>
         * 高度，单位：像素
         * </pre>
         *
         * <code>uint32 height = 3;</code>
         * @param value The height to set.
         * @return This builder for chaining.
         */
        public Builder setHeight(int value) {

          height_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 高度，单位：像素
         * </pre>
         *
         * <code>uint32 height = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearHeight() {
          bitField0_ = (bitField0_ & ~0x00000004);
          height_ = 0;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:Ad.MaterialMeta.Image)
      }

      // @@protoc_insertion_point(class_scope:Ad.MaterialMeta.Image)
      private static final cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image();
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Image>
          PARSER = new com.google.protobuf.AbstractParser<Image>() {
        @java.lang.Override
        public Image parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Image> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Image> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface VideoOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Ad.MaterialMeta.Video)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 视频地址。必填
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The url.
       */
      java.lang.String getUrl();
      /**
       * <pre>
       * 视频地址。必填
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The bytes for url.
       */
      com.google.protobuf.ByteString
          getUrlBytes();

      /**
       * <pre>
       * 封面图地址。必填
       * </pre>
       *
       * <code>string cover_image = 2;</code>
       * @return The coverImage.
       */
      java.lang.String getCoverImage();
      /**
       * <pre>
       * 封面图地址。必填
       * </pre>
       *
       * <code>string cover_image = 2;</code>
       * @return The bytes for coverImage.
       */
      com.google.protobuf.ByteString
          getCoverImageBytes();

      /**
       * <pre>
       * 视频时长（秒），可选
       * </pre>
       *
       * <code>float duration = 3;</code>
       * @return The duration.
       */
      float getDuration();

      /**
       * <pre>
       * 视频大小，单位（kb）可选
       * </pre>
       *
       * <code>float size = 4;</code>
       * @return The size.
       */
      float getSize();

      /**
       * <pre>
       * 视频宽，建议必填
       * </pre>
       *
       * <code>int32 width = 5;</code>
       * @return The width.
       */
      int getWidth();

      /**
       * <pre>
       * 视频高，建议必填
       * </pre>
       *
       * <code>int32 height = 6;</code>
       * @return The height.
       */
      int getHeight();

      /**
       * <pre>
       * 视频码率,单位（Kbps）。可选
       * </pre>
       *
       * <code>int32 bitrate = 7;</code>
       * @return The bitrate.
       */
      int getBitrate();

      /**
       * <pre>
       * 视频格式（扩展名）。建议必填
       * </pre>
       *
       * <code>string format = 8;</code>
       * @return The format.
       */
      java.lang.String getFormat();
      /**
       * <pre>
       * 视频格式（扩展名）。建议必填
       * </pre>
       *
       * <code>string format = 8;</code>
       * @return The bytes for format.
       */
      com.google.protobuf.ByteString
          getFormatBytes();

      /**
       * <pre>
       * 编码格式，可选（参考 https://en.wikipedia.org/wiki/Video_file_format）
       * </pre>
       *
       * <code>string coding_format = 9;</code>
       * @return The codingFormat.
       */
      java.lang.String getCodingFormat();
      /**
       * <pre>
       * 编码格式，可选（参考 https://en.wikipedia.org/wiki/Video_file_format）
       * </pre>
       *
       * <code>string coding_format = 9;</code>
       * @return The bytes for codingFormat.
       */
      com.google.protobuf.ByteString
          getCodingFormatBytes();
    }
    /**
     * <pre>
     * 视频素材信息
     * </pre>
     *
     * Protobuf type {@code Ad.MaterialMeta.Video}
     */
    public static final class Video extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:Ad.MaterialMeta.Video)
        VideoOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 28,
          /* patch= */ 3,
          /* suffix= */ "",
          Video.class.getName());
      }
      // Use Video.newBuilder() to construct.
      private Video(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private Video() {
        url_ = "";
        coverImage_ = "";
        format_ = "";
        codingFormat_ = "";
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Video_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Video_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.class, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.Builder.class);
      }

      public static final int URL_FIELD_NUMBER = 1;
      @SuppressWarnings("serial")
      private volatile java.lang.Object url_ = "";
      /**
       * <pre>
       * 视频地址。必填
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The url.
       */
      @java.lang.Override
      public java.lang.String getUrl() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          url_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 视频地址。必填
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The bytes for url.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getUrlBytes() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          url_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int COVER_IMAGE_FIELD_NUMBER = 2;
      @SuppressWarnings("serial")
      private volatile java.lang.Object coverImage_ = "";
      /**
       * <pre>
       * 封面图地址。必填
       * </pre>
       *
       * <code>string cover_image = 2;</code>
       * @return The coverImage.
       */
      @java.lang.Override
      public java.lang.String getCoverImage() {
        java.lang.Object ref = coverImage_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          coverImage_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 封面图地址。必填
       * </pre>
       *
       * <code>string cover_image = 2;</code>
       * @return The bytes for coverImage.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getCoverImageBytes() {
        java.lang.Object ref = coverImage_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          coverImage_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DURATION_FIELD_NUMBER = 3;
      private float duration_ = 0F;
      /**
       * <pre>
       * 视频时长（秒），可选
       * </pre>
       *
       * <code>float duration = 3;</code>
       * @return The duration.
       */
      @java.lang.Override
      public float getDuration() {
        return duration_;
      }

      public static final int SIZE_FIELD_NUMBER = 4;
      private float size_ = 0F;
      /**
       * <pre>
       * 视频大小，单位（kb）可选
       * </pre>
       *
       * <code>float size = 4;</code>
       * @return The size.
       */
      @java.lang.Override
      public float getSize() {
        return size_;
      }

      public static final int WIDTH_FIELD_NUMBER = 5;
      private int width_ = 0;
      /**
       * <pre>
       * 视频宽，建议必填
       * </pre>
       *
       * <code>int32 width = 5;</code>
       * @return The width.
       */
      @java.lang.Override
      public int getWidth() {
        return width_;
      }

      public static final int HEIGHT_FIELD_NUMBER = 6;
      private int height_ = 0;
      /**
       * <pre>
       * 视频高，建议必填
       * </pre>
       *
       * <code>int32 height = 6;</code>
       * @return The height.
       */
      @java.lang.Override
      public int getHeight() {
        return height_;
      }

      public static final int BITRATE_FIELD_NUMBER = 7;
      private int bitrate_ = 0;
      /**
       * <pre>
       * 视频码率,单位（Kbps）。可选
       * </pre>
       *
       * <code>int32 bitrate = 7;</code>
       * @return The bitrate.
       */
      @java.lang.Override
      public int getBitrate() {
        return bitrate_;
      }

      public static final int FORMAT_FIELD_NUMBER = 8;
      @SuppressWarnings("serial")
      private volatile java.lang.Object format_ = "";
      /**
       * <pre>
       * 视频格式（扩展名）。建议必填
       * </pre>
       *
       * <code>string format = 8;</code>
       * @return The format.
       */
      @java.lang.Override
      public java.lang.String getFormat() {
        java.lang.Object ref = format_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          format_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 视频格式（扩展名）。建议必填
       * </pre>
       *
       * <code>string format = 8;</code>
       * @return The bytes for format.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getFormatBytes() {
        java.lang.Object ref = format_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          format_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int CODING_FORMAT_FIELD_NUMBER = 9;
      @SuppressWarnings("serial")
      private volatile java.lang.Object codingFormat_ = "";
      /**
       * <pre>
       * 编码格式，可选（参考 https://en.wikipedia.org/wiki/Video_file_format）
       * </pre>
       *
       * <code>string coding_format = 9;</code>
       * @return The codingFormat.
       */
      @java.lang.Override
      public java.lang.String getCodingFormat() {
        java.lang.Object ref = codingFormat_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          codingFormat_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 编码格式，可选（参考 https://en.wikipedia.org/wiki/Video_file_format）
       * </pre>
       *
       * <code>string coding_format = 9;</code>
       * @return The bytes for codingFormat.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getCodingFormatBytes() {
        java.lang.Object ref = codingFormat_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          codingFormat_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 1, url_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(coverImage_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 2, coverImage_);
        }
        if (java.lang.Float.floatToRawIntBits(duration_) != 0) {
          output.writeFloat(3, duration_);
        }
        if (java.lang.Float.floatToRawIntBits(size_) != 0) {
          output.writeFloat(4, size_);
        }
        if (width_ != 0) {
          output.writeInt32(5, width_);
        }
        if (height_ != 0) {
          output.writeInt32(6, height_);
        }
        if (bitrate_ != 0) {
          output.writeInt32(7, bitrate_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(format_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 8, format_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(codingFormat_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 9, codingFormat_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(1, url_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(coverImage_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(2, coverImage_);
        }
        if (java.lang.Float.floatToRawIntBits(duration_) != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeFloatSize(3, duration_);
        }
        if (java.lang.Float.floatToRawIntBits(size_) != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeFloatSize(4, size_);
        }
        if (width_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(5, width_);
        }
        if (height_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(6, height_);
        }
        if (bitrate_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(7, bitrate_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(format_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(8, format_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(codingFormat_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(9, codingFormat_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video)) {
          return super.equals(obj);
        }
        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video other = (cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video) obj;

        if (!getUrl()
            .equals(other.getUrl())) return false;
        if (!getCoverImage()
            .equals(other.getCoverImage())) return false;
        if (java.lang.Float.floatToIntBits(getDuration())
            != java.lang.Float.floatToIntBits(
                other.getDuration())) return false;
        if (java.lang.Float.floatToIntBits(getSize())
            != java.lang.Float.floatToIntBits(
                other.getSize())) return false;
        if (getWidth()
            != other.getWidth()) return false;
        if (getHeight()
            != other.getHeight()) return false;
        if (getBitrate()
            != other.getBitrate()) return false;
        if (!getFormat()
            .equals(other.getFormat())) return false;
        if (!getCodingFormat()
            .equals(other.getCodingFormat())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + URL_FIELD_NUMBER;
        hash = (53 * hash) + getUrl().hashCode();
        hash = (37 * hash) + COVER_IMAGE_FIELD_NUMBER;
        hash = (53 * hash) + getCoverImage().hashCode();
        hash = (37 * hash) + DURATION_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getDuration());
        hash = (37 * hash) + SIZE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getSize());
        hash = (37 * hash) + WIDTH_FIELD_NUMBER;
        hash = (53 * hash) + getWidth();
        hash = (37 * hash) + HEIGHT_FIELD_NUMBER;
        hash = (53 * hash) + getHeight();
        hash = (37 * hash) + BITRATE_FIELD_NUMBER;
        hash = (53 * hash) + getBitrate();
        hash = (37 * hash) + FORMAT_FIELD_NUMBER;
        hash = (53 * hash) + getFormat().hashCode();
        hash = (37 * hash) + CODING_FORMAT_FIELD_NUMBER;
        hash = (53 * hash) + getCodingFormat().hashCode();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * 视频素材信息
       * </pre>
       *
       * Protobuf type {@code Ad.MaterialMeta.Video}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Ad.MaterialMeta.Video)
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.VideoOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Video_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Video_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.class, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.Builder.class);
        }

        // Construct using cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          url_ = "";
          coverImage_ = "";
          duration_ = 0F;
          size_ = 0F;
          width_ = 0;
          height_ = 0;
          bitrate_ = 0;
          format_ = "";
          codingFormat_ = "";
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Video_descriptor;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video getDefaultInstanceForType() {
          return cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.getDefaultInstance();
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video build() {
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video buildPartial() {
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video result = new cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.url_ = url_;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.coverImage_ = coverImage_;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.duration_ = duration_;
          }
          if (((from_bitField0_ & 0x00000008) != 0)) {
            result.size_ = size_;
          }
          if (((from_bitField0_ & 0x00000010) != 0)) {
            result.width_ = width_;
          }
          if (((from_bitField0_ & 0x00000020) != 0)) {
            result.height_ = height_;
          }
          if (((from_bitField0_ & 0x00000040) != 0)) {
            result.bitrate_ = bitrate_;
          }
          if (((from_bitField0_ & 0x00000080) != 0)) {
            result.format_ = format_;
          }
          if (((from_bitField0_ & 0x00000100) != 0)) {
            result.codingFormat_ = codingFormat_;
          }
        }

        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video) {
            return mergeFrom((cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video other) {
          if (other == cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.getDefaultInstance()) return this;
          if (!other.getUrl().isEmpty()) {
            url_ = other.url_;
            bitField0_ |= 0x00000001;
            onChanged();
          }
          if (!other.getCoverImage().isEmpty()) {
            coverImage_ = other.coverImage_;
            bitField0_ |= 0x00000002;
            onChanged();
          }
          if (other.getDuration() != 0F) {
            setDuration(other.getDuration());
          }
          if (other.getSize() != 0F) {
            setSize(other.getSize());
          }
          if (other.getWidth() != 0) {
            setWidth(other.getWidth());
          }
          if (other.getHeight() != 0) {
            setHeight(other.getHeight());
          }
          if (other.getBitrate() != 0) {
            setBitrate(other.getBitrate());
          }
          if (!other.getFormat().isEmpty()) {
            format_ = other.format_;
            bitField0_ |= 0x00000080;
            onChanged();
          }
          if (!other.getCodingFormat().isEmpty()) {
            codingFormat_ = other.codingFormat_;
            bitField0_ |= 0x00000100;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  url_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 10
                case 18: {
                  coverImage_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 18
                case 29: {
                  duration_ = input.readFloat();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 29
                case 37: {
                  size_ = input.readFloat();
                  bitField0_ |= 0x00000008;
                  break;
                } // case 37
                case 40: {
                  width_ = input.readInt32();
                  bitField0_ |= 0x00000010;
                  break;
                } // case 40
                case 48: {
                  height_ = input.readInt32();
                  bitField0_ |= 0x00000020;
                  break;
                } // case 48
                case 56: {
                  bitrate_ = input.readInt32();
                  bitField0_ |= 0x00000040;
                  break;
                } // case 56
                case 66: {
                  format_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000080;
                  break;
                } // case 66
                case 74: {
                  codingFormat_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000100;
                  break;
                } // case 74
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private java.lang.Object url_ = "";
        /**
         * <pre>
         * 视频地址。必填
         * </pre>
         *
         * <code>string url = 1;</code>
         * @return The url.
         */
        public java.lang.String getUrl() {
          java.lang.Object ref = url_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            url_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 视频地址。必填
         * </pre>
         *
         * <code>string url = 1;</code>
         * @return The bytes for url.
         */
        public com.google.protobuf.ByteString
            getUrlBytes() {
          java.lang.Object ref = url_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            url_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 视频地址。必填
         * </pre>
         *
         * <code>string url = 1;</code>
         * @param value The url to set.
         * @return This builder for chaining.
         */
        public Builder setUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          url_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频地址。必填
         * </pre>
         *
         * <code>string url = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearUrl() {
          url_ = getDefaultInstance().getUrl();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频地址。必填
         * </pre>
         *
         * <code>string url = 1;</code>
         * @param value The bytes for url to set.
         * @return This builder for chaining.
         */
        public Builder setUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          url_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }

        private java.lang.Object coverImage_ = "";
        /**
         * <pre>
         * 封面图地址。必填
         * </pre>
         *
         * <code>string cover_image = 2;</code>
         * @return The coverImage.
         */
        public java.lang.String getCoverImage() {
          java.lang.Object ref = coverImage_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            coverImage_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 封面图地址。必填
         * </pre>
         *
         * <code>string cover_image = 2;</code>
         * @return The bytes for coverImage.
         */
        public com.google.protobuf.ByteString
            getCoverImageBytes() {
          java.lang.Object ref = coverImage_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            coverImage_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 封面图地址。必填
         * </pre>
         *
         * <code>string cover_image = 2;</code>
         * @param value The coverImage to set.
         * @return This builder for chaining.
         */
        public Builder setCoverImage(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          coverImage_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 封面图地址。必填
         * </pre>
         *
         * <code>string cover_image = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearCoverImage() {
          coverImage_ = getDefaultInstance().getCoverImage();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 封面图地址。必填
         * </pre>
         *
         * <code>string cover_image = 2;</code>
         * @param value The bytes for coverImage to set.
         * @return This builder for chaining.
         */
        public Builder setCoverImageBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          coverImage_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }

        private float duration_ ;
        /**
         * <pre>
         * 视频时长（秒），可选
         * </pre>
         *
         * <code>float duration = 3;</code>
         * @return The duration.
         */
        @java.lang.Override
        public float getDuration() {
          return duration_;
        }
        /**
         * <pre>
         * 视频时长（秒），可选
         * </pre>
         *
         * <code>float duration = 3;</code>
         * @param value The duration to set.
         * @return This builder for chaining.
         */
        public Builder setDuration(float value) {

          duration_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频时长（秒），可选
         * </pre>
         *
         * <code>float duration = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearDuration() {
          bitField0_ = (bitField0_ & ~0x00000004);
          duration_ = 0F;
          onChanged();
          return this;
        }

        private float size_ ;
        /**
         * <pre>
         * 视频大小，单位（kb）可选
         * </pre>
         *
         * <code>float size = 4;</code>
         * @return The size.
         */
        @java.lang.Override
        public float getSize() {
          return size_;
        }
        /**
         * <pre>
         * 视频大小，单位（kb）可选
         * </pre>
         *
         * <code>float size = 4;</code>
         * @param value The size to set.
         * @return This builder for chaining.
         */
        public Builder setSize(float value) {

          size_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频大小，单位（kb）可选
         * </pre>
         *
         * <code>float size = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearSize() {
          bitField0_ = (bitField0_ & ~0x00000008);
          size_ = 0F;
          onChanged();
          return this;
        }

        private int width_ ;
        /**
         * <pre>
         * 视频宽，建议必填
         * </pre>
         *
         * <code>int32 width = 5;</code>
         * @return The width.
         */
        @java.lang.Override
        public int getWidth() {
          return width_;
        }
        /**
         * <pre>
         * 视频宽，建议必填
         * </pre>
         *
         * <code>int32 width = 5;</code>
         * @param value The width to set.
         * @return This builder for chaining.
         */
        public Builder setWidth(int value) {

          width_ = value;
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频宽，建议必填
         * </pre>
         *
         * <code>int32 width = 5;</code>
         * @return This builder for chaining.
         */
        public Builder clearWidth() {
          bitField0_ = (bitField0_ & ~0x00000010);
          width_ = 0;
          onChanged();
          return this;
        }

        private int height_ ;
        /**
         * <pre>
         * 视频高，建议必填
         * </pre>
         *
         * <code>int32 height = 6;</code>
         * @return The height.
         */
        @java.lang.Override
        public int getHeight() {
          return height_;
        }
        /**
         * <pre>
         * 视频高，建议必填
         * </pre>
         *
         * <code>int32 height = 6;</code>
         * @param value The height to set.
         * @return This builder for chaining.
         */
        public Builder setHeight(int value) {

          height_ = value;
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频高，建议必填
         * </pre>
         *
         * <code>int32 height = 6;</code>
         * @return This builder for chaining.
         */
        public Builder clearHeight() {
          bitField0_ = (bitField0_ & ~0x00000020);
          height_ = 0;
          onChanged();
          return this;
        }

        private int bitrate_ ;
        /**
         * <pre>
         * 视频码率,单位（Kbps）。可选
         * </pre>
         *
         * <code>int32 bitrate = 7;</code>
         * @return The bitrate.
         */
        @java.lang.Override
        public int getBitrate() {
          return bitrate_;
        }
        /**
         * <pre>
         * 视频码率,单位（Kbps）。可选
         * </pre>
         *
         * <code>int32 bitrate = 7;</code>
         * @param value The bitrate to set.
         * @return This builder for chaining.
         */
        public Builder setBitrate(int value) {

          bitrate_ = value;
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频码率,单位（Kbps）。可选
         * </pre>
         *
         * <code>int32 bitrate = 7;</code>
         * @return This builder for chaining.
         */
        public Builder clearBitrate() {
          bitField0_ = (bitField0_ & ~0x00000040);
          bitrate_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object format_ = "";
        /**
         * <pre>
         * 视频格式（扩展名）。建议必填
         * </pre>
         *
         * <code>string format = 8;</code>
         * @return The format.
         */
        public java.lang.String getFormat() {
          java.lang.Object ref = format_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            format_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 视频格式（扩展名）。建议必填
         * </pre>
         *
         * <code>string format = 8;</code>
         * @return The bytes for format.
         */
        public com.google.protobuf.ByteString
            getFormatBytes() {
          java.lang.Object ref = format_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            format_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 视频格式（扩展名）。建议必填
         * </pre>
         *
         * <code>string format = 8;</code>
         * @param value The format to set.
         * @return This builder for chaining.
         */
        public Builder setFormat(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          format_ = value;
          bitField0_ |= 0x00000080;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频格式（扩展名）。建议必填
         * </pre>
         *
         * <code>string format = 8;</code>
         * @return This builder for chaining.
         */
        public Builder clearFormat() {
          format_ = getDefaultInstance().getFormat();
          bitField0_ = (bitField0_ & ~0x00000080);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频格式（扩展名）。建议必填
         * </pre>
         *
         * <code>string format = 8;</code>
         * @param value The bytes for format to set.
         * @return This builder for chaining.
         */
        public Builder setFormatBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          format_ = value;
          bitField0_ |= 0x00000080;
          onChanged();
          return this;
        }

        private java.lang.Object codingFormat_ = "";
        /**
         * <pre>
         * 编码格式，可选（参考 https://en.wikipedia.org/wiki/Video_file_format）
         * </pre>
         *
         * <code>string coding_format = 9;</code>
         * @return The codingFormat.
         */
        public java.lang.String getCodingFormat() {
          java.lang.Object ref = codingFormat_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            codingFormat_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 编码格式，可选（参考 https://en.wikipedia.org/wiki/Video_file_format）
         * </pre>
         *
         * <code>string coding_format = 9;</code>
         * @return The bytes for codingFormat.
         */
        public com.google.protobuf.ByteString
            getCodingFormatBytes() {
          java.lang.Object ref = codingFormat_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            codingFormat_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 编码格式，可选（参考 https://en.wikipedia.org/wiki/Video_file_format）
         * </pre>
         *
         * <code>string coding_format = 9;</code>
         * @param value The codingFormat to set.
         * @return This builder for chaining.
         */
        public Builder setCodingFormat(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          codingFormat_ = value;
          bitField0_ |= 0x00000100;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 编码格式，可选（参考 https://en.wikipedia.org/wiki/Video_file_format）
         * </pre>
         *
         * <code>string coding_format = 9;</code>
         * @return This builder for chaining.
         */
        public Builder clearCodingFormat() {
          codingFormat_ = getDefaultInstance().getCodingFormat();
          bitField0_ = (bitField0_ & ~0x00000100);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 编码格式，可选（参考 https://en.wikipedia.org/wiki/Video_file_format）
         * </pre>
         *
         * <code>string coding_format = 9;</code>
         * @param value The bytes for codingFormat to set.
         * @return This builder for chaining.
         */
        public Builder setCodingFormatBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          codingFormat_ = value;
          bitField0_ |= 0x00000100;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:Ad.MaterialMeta.Video)
      }

      // @@protoc_insertion_point(class_scope:Ad.MaterialMeta.Video)
      private static final cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video();
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Video>
          PARSER = new com.google.protobuf.AbstractParser<Video>() {
        @java.lang.Override
        public Video parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Video> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Video> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface TrackingOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Ad.MaterialMeta.Tracking)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>int32 event = 1;</code>
       * @return The event.
       */
      int getEvent();

      /**
       * <pre>
       * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
       * </pre>
       *
       * <code>repeated string urls = 2;</code>
       * @return A list containing the urls.
       */
      java.util.List<java.lang.String>
          getUrlsList();
      /**
       * <pre>
       * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
       * </pre>
       *
       * <code>repeated string urls = 2;</code>
       * @return The count of urls.
       */
      int getUrlsCount();
      /**
       * <pre>
       * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
       * </pre>
       *
       * <code>repeated string urls = 2;</code>
       * @param index The index of the element to return.
       * @return The urls at the given index.
       */
      java.lang.String getUrls(int index);
      /**
       * <pre>
       * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
       * </pre>
       *
       * <code>repeated string urls = 2;</code>
       * @param index The index of the value to return.
       * @return The bytes of the urls at the given index.
       */
      com.google.protobuf.ByteString
          getUrlsBytes(int index);
    }
    /**
     * <pre>
     * *
     * 支持的事件类型
     * 101：曝光事件，创意曝光后，开发者侧回传此事件
     * 201：点击事件，创意被用户点击后，开发者侧回传此事件
     * 301：下载开始事件，系统开始执行下载后，开发者侧回传此事件
     * 302：下载完成事件，app下载完成后，开发者侧回传此事件
     * 303：用户开始安装事件，用户点击目标App开始安装，开发者侧回传此事件
     * 304：安装完成事件，app下载并安装成功后，开发者侧回传此事件
     * 401：deeplink打点，检测到目标app已安装
     * 402：deeplink打点，检测到目标app未安装
     * 403：deeplink打点，目标app已安装，并且调起成功
     * 404：deeplink打点，目标app已安装，但是调起失败
     * 5000: 视频开始播放
     * 5025: 视频播放进度1/4
     * 5050: 视频播放进度1/2
     * 5075: 视频播放进度3/4
     * 5100: 视频播放完成
     * </pre>
     *
     * Protobuf type {@code Ad.MaterialMeta.Tracking}
     */
    public static final class Tracking extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:Ad.MaterialMeta.Tracking)
        TrackingOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 28,
          /* patch= */ 3,
          /* suffix= */ "",
          Tracking.class.getName());
      }
      // Use Tracking.newBuilder() to construct.
      private Tracking(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private Tracking() {
        urls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Tracking_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Tracking_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.class, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder.class);
      }

      public static final int EVENT_FIELD_NUMBER = 1;
      private int event_ = 0;
      /**
       * <code>int32 event = 1;</code>
       * @return The event.
       */
      @java.lang.Override
      public int getEvent() {
        return event_;
      }

      public static final int URLS_FIELD_NUMBER = 2;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList urls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <pre>
       * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
       * </pre>
       *
       * <code>repeated string urls = 2;</code>
       * @return A list containing the urls.
       */
      public com.google.protobuf.ProtocolStringList
          getUrlsList() {
        return urls_;
      }
      /**
       * <pre>
       * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
       * </pre>
       *
       * <code>repeated string urls = 2;</code>
       * @return The count of urls.
       */
      public int getUrlsCount() {
        return urls_.size();
      }
      /**
       * <pre>
       * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
       * </pre>
       *
       * <code>repeated string urls = 2;</code>
       * @param index The index of the element to return.
       * @return The urls at the given index.
       */
      public java.lang.String getUrls(int index) {
        return urls_.get(index);
      }
      /**
       * <pre>
       * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
       * </pre>
       *
       * <code>repeated string urls = 2;</code>
       * @param index The index of the value to return.
       * @return The bytes of the urls at the given index.
       */
      public com.google.protobuf.ByteString
          getUrlsBytes(int index) {
        return urls_.getByteString(index);
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (event_ != 0) {
          output.writeInt32(1, event_);
        }
        for (int i = 0; i < urls_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 2, urls_.getRaw(i));
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (event_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(1, event_);
        }
        {
          int dataSize = 0;
          for (int i = 0; i < urls_.size(); i++) {
            dataSize += computeStringSizeNoTag(urls_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getUrlsList().size();
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking)) {
          return super.equals(obj);
        }
        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking other = (cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking) obj;

        if (getEvent()
            != other.getEvent()) return false;
        if (!getUrlsList()
            .equals(other.getUrlsList())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + EVENT_FIELD_NUMBER;
        hash = (53 * hash) + getEvent();
        if (getUrlsCount() > 0) {
          hash = (37 * hash) + URLS_FIELD_NUMBER;
          hash = (53 * hash) + getUrlsList().hashCode();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * *
       * 支持的事件类型
       * 101：曝光事件，创意曝光后，开发者侧回传此事件
       * 201：点击事件，创意被用户点击后，开发者侧回传此事件
       * 301：下载开始事件，系统开始执行下载后，开发者侧回传此事件
       * 302：下载完成事件，app下载完成后，开发者侧回传此事件
       * 303：用户开始安装事件，用户点击目标App开始安装，开发者侧回传此事件
       * 304：安装完成事件，app下载并安装成功后，开发者侧回传此事件
       * 401：deeplink打点，检测到目标app已安装
       * 402：deeplink打点，检测到目标app未安装
       * 403：deeplink打点，目标app已安装，并且调起成功
       * 404：deeplink打点，目标app已安装，但是调起失败
       * 5000: 视频开始播放
       * 5025: 视频播放进度1/4
       * 5050: 视频播放进度1/2
       * 5075: 视频播放进度3/4
       * 5100: 视频播放完成
       * </pre>
       *
       * Protobuf type {@code Ad.MaterialMeta.Tracking}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Ad.MaterialMeta.Tracking)
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.TrackingOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Tracking_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Tracking_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.class, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder.class);
        }

        // Construct using cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          event_ = 0;
          urls_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_Tracking_descriptor;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking getDefaultInstanceForType() {
          return cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.getDefaultInstance();
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking build() {
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking buildPartial() {
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking result = new cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.event_ = event_;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            urls_.makeImmutable();
            result.urls_ = urls_;
          }
        }

        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking) {
            return mergeFrom((cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking other) {
          if (other == cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.getDefaultInstance()) return this;
          if (other.getEvent() != 0) {
            setEvent(other.getEvent());
          }
          if (!other.urls_.isEmpty()) {
            if (urls_.isEmpty()) {
              urls_ = other.urls_;
              bitField0_ |= 0x00000002;
            } else {
              ensureUrlsIsMutable();
              urls_.addAll(other.urls_);
            }
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  event_ = input.readInt32();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 18: {
                  java.lang.String s = input.readStringRequireUtf8();
                  ensureUrlsIsMutable();
                  urls_.add(s);
                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private int event_ ;
        /**
         * <code>int32 event = 1;</code>
         * @return The event.
         */
        @java.lang.Override
        public int getEvent() {
          return event_;
        }
        /**
         * <code>int32 event = 1;</code>
         * @param value The event to set.
         * @return This builder for chaining.
         */
        public Builder setEvent(int value) {

          event_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>int32 event = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearEvent() {
          bitField0_ = (bitField0_ & ~0x00000001);
          event_ = 0;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList urls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureUrlsIsMutable() {
          if (!urls_.isModifiable()) {
            urls_ = new com.google.protobuf.LazyStringArrayList(urls_);
          }
          bitField0_ |= 0x00000002;
        }
        /**
         * <pre>
         * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
         * </pre>
         *
         * <code>repeated string urls = 2;</code>
         * @return A list containing the urls.
         */
        public com.google.protobuf.ProtocolStringList
            getUrlsList() {
          urls_.makeImmutable();
          return urls_;
        }
        /**
         * <pre>
         * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
         * </pre>
         *
         * <code>repeated string urls = 2;</code>
         * @return The count of urls.
         */
        public int getUrlsCount() {
          return urls_.size();
        }
        /**
         * <pre>
         * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
         * </pre>
         *
         * <code>repeated string urls = 2;</code>
         * @param index The index of the element to return.
         * @return The urls at the given index.
         */
        public java.lang.String getUrls(int index) {
          return urls_.get(index);
        }
        /**
         * <pre>
         * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
         * </pre>
         *
         * <code>repeated string urls = 2;</code>
         * @param index The index of the value to return.
         * @return The bytes of the urls at the given index.
         */
        public com.google.protobuf.ByteString
            getUrlsBytes(int index) {
          return urls_.getByteString(index);
        }
        /**
         * <pre>
         * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
         * </pre>
         *
         * <code>repeated string urls = 2;</code>
         * @param index The index to set the value at.
         * @param value The urls to set.
         * @return This builder for chaining.
         */
        public Builder setUrls(
            int index, java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureUrlsIsMutable();
          urls_.set(index, value);
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
         * </pre>
         *
         * <code>repeated string urls = 2;</code>
         * @param value The urls to add.
         * @return This builder for chaining.
         */
        public Builder addUrls(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureUrlsIsMutable();
          urls_.add(value);
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
         * </pre>
         *
         * <code>repeated string urls = 2;</code>
         * @param values The urls to add.
         * @return This builder for chaining.
         */
        public Builder addAllUrls(
            java.lang.Iterable<java.lang.String> values) {
          ensureUrlsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, urls_);
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
         * </pre>
         *
         * <code>repeated string urls = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearUrls() {
          urls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 需要上报的url列表，如果is_support_macro=1，需要替换url里边包含的宏
         * </pre>
         *
         * <code>repeated string urls = 2;</code>
         * @param value The bytes of the urls to add.
         * @return This builder for chaining.
         */
        public Builder addUrlsBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureUrlsIsMutable();
          urls_.add(value);
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:Ad.MaterialMeta.Tracking)
      }

      // @@protoc_insertion_point(class_scope:Ad.MaterialMeta.Tracking)
      private static final cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking();
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Tracking>
          PARSER = new com.google.protobuf.AbstractParser<Tracking>() {
        @java.lang.Override
        public Tracking parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Tracking> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Tracking> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface WxMpInfoOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Ad.MaterialMeta.WxMpInfo)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 小程序原始ID 如:gh_d43f693ca31f
       * </pre>
       *
       * <code>string username = 1;</code>
       * @return The username.
       */
      java.lang.String getUsername();
      /**
       * <pre>
       * 小程序原始ID 如:gh_d43f693ca31f
       * </pre>
       *
       * <code>string username = 1;</code>
       * @return The bytes for username.
       */
      com.google.protobuf.ByteString
          getUsernameBytes();

      /**
       * <pre>
       * 填应用AppId
       * </pre>
       *
       * <code>string app_id = 2;</code>
       * @return The appId.
       */
      java.lang.String getAppId();
      /**
       * <pre>
       * 填应用AppId
       * </pre>
       *
       * <code>string app_id = 2;</code>
       * @return The bytes for appId.
       */
      com.google.protobuf.ByteString
          getAppIdBytes();

      /**
       * <pre>
       * 拉起小程序页面的可带参路径，不填默认拉起小程序首页
       * </pre>
       *
       * <code>string path = 3;</code>
       * @return The path.
       */
      java.lang.String getPath();
      /**
       * <pre>
       * 拉起小程序页面的可带参路径，不填默认拉起小程序首页
       * </pre>
       *
       * <code>string path = 3;</code>
       * @return The bytes for path.
       */
      com.google.protobuf.ByteString
          getPathBytes();
    }
    /**
     * Protobuf type {@code Ad.MaterialMeta.WxMpInfo}
     */
    public static final class WxMpInfo extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:Ad.MaterialMeta.WxMpInfo)
        WxMpInfoOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 28,
          /* patch= */ 3,
          /* suffix= */ "",
          WxMpInfo.class.getName());
      }
      // Use WxMpInfo.newBuilder() to construct.
      private WxMpInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private WxMpInfo() {
        username_ = "";
        appId_ = "";
        path_ = "";
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_WxMpInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_WxMpInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.class, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.Builder.class);
      }

      public static final int USERNAME_FIELD_NUMBER = 1;
      @SuppressWarnings("serial")
      private volatile java.lang.Object username_ = "";
      /**
       * <pre>
       * 小程序原始ID 如:gh_d43f693ca31f
       * </pre>
       *
       * <code>string username = 1;</code>
       * @return The username.
       */
      @java.lang.Override
      public java.lang.String getUsername() {
        java.lang.Object ref = username_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          username_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 小程序原始ID 如:gh_d43f693ca31f
       * </pre>
       *
       * <code>string username = 1;</code>
       * @return The bytes for username.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getUsernameBytes() {
        java.lang.Object ref = username_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          username_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int APP_ID_FIELD_NUMBER = 2;
      @SuppressWarnings("serial")
      private volatile java.lang.Object appId_ = "";
      /**
       * <pre>
       * 填应用AppId
       * </pre>
       *
       * <code>string app_id = 2;</code>
       * @return The appId.
       */
      @java.lang.Override
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appId_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 填应用AppId
       * </pre>
       *
       * <code>string app_id = 2;</code>
       * @return The bytes for appId.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int PATH_FIELD_NUMBER = 3;
      @SuppressWarnings("serial")
      private volatile java.lang.Object path_ = "";
      /**
       * <pre>
       * 拉起小程序页面的可带参路径，不填默认拉起小程序首页
       * </pre>
       *
       * <code>string path = 3;</code>
       * @return The path.
       */
      @java.lang.Override
      public java.lang.String getPath() {
        java.lang.Object ref = path_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          path_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 拉起小程序页面的可带参路径，不填默认拉起小程序首页
       * </pre>
       *
       * <code>string path = 3;</code>
       * @return The bytes for path.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getPathBytes() {
        java.lang.Object ref = path_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          path_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(username_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 1, username_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appId_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 2, appId_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(path_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 3, path_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(username_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(1, username_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appId_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(2, appId_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(path_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(3, path_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo)) {
          return super.equals(obj);
        }
        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo other = (cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo) obj;

        if (!getUsername()
            .equals(other.getUsername())) return false;
        if (!getAppId()
            .equals(other.getAppId())) return false;
        if (!getPath()
            .equals(other.getPath())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + USERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getUsername().hashCode();
        hash = (37 * hash) + APP_ID_FIELD_NUMBER;
        hash = (53 * hash) + getAppId().hashCode();
        hash = (37 * hash) + PATH_FIELD_NUMBER;
        hash = (53 * hash) + getPath().hashCode();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code Ad.MaterialMeta.WxMpInfo}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Ad.MaterialMeta.WxMpInfo)
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfoOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_WxMpInfo_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_WxMpInfo_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.class, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.Builder.class);
        }

        // Construct using cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          username_ = "";
          appId_ = "";
          path_ = "";
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_WxMpInfo_descriptor;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo getDefaultInstanceForType() {
          return cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.getDefaultInstance();
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo build() {
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo buildPartial() {
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo result = new cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.username_ = username_;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.appId_ = appId_;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.path_ = path_;
          }
        }

        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo) {
            return mergeFrom((cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo other) {
          if (other == cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.getDefaultInstance()) return this;
          if (!other.getUsername().isEmpty()) {
            username_ = other.username_;
            bitField0_ |= 0x00000001;
            onChanged();
          }
          if (!other.getAppId().isEmpty()) {
            appId_ = other.appId_;
            bitField0_ |= 0x00000002;
            onChanged();
          }
          if (!other.getPath().isEmpty()) {
            path_ = other.path_;
            bitField0_ |= 0x00000004;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  username_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 10
                case 18: {
                  appId_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 18
                case 26: {
                  path_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 26
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private java.lang.Object username_ = "";
        /**
         * <pre>
         * 小程序原始ID 如:gh_d43f693ca31f
         * </pre>
         *
         * <code>string username = 1;</code>
         * @return The username.
         */
        public java.lang.String getUsername() {
          java.lang.Object ref = username_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            username_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 小程序原始ID 如:gh_d43f693ca31f
         * </pre>
         *
         * <code>string username = 1;</code>
         * @return The bytes for username.
         */
        public com.google.protobuf.ByteString
            getUsernameBytes() {
          java.lang.Object ref = username_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            username_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 小程序原始ID 如:gh_d43f693ca31f
         * </pre>
         *
         * <code>string username = 1;</code>
         * @param value The username to set.
         * @return This builder for chaining.
         */
        public Builder setUsername(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          username_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 小程序原始ID 如:gh_d43f693ca31f
         * </pre>
         *
         * <code>string username = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearUsername() {
          username_ = getDefaultInstance().getUsername();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 小程序原始ID 如:gh_d43f693ca31f
         * </pre>
         *
         * <code>string username = 1;</code>
         * @param value The bytes for username to set.
         * @return This builder for chaining.
         */
        public Builder setUsernameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          username_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }

        private java.lang.Object appId_ = "";
        /**
         * <pre>
         * 填应用AppId
         * </pre>
         *
         * <code>string app_id = 2;</code>
         * @return The appId.
         */
        public java.lang.String getAppId() {
          java.lang.Object ref = appId_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            appId_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 填应用AppId
         * </pre>
         *
         * <code>string app_id = 2;</code>
         * @return The bytes for appId.
         */
        public com.google.protobuf.ByteString
            getAppIdBytes() {
          java.lang.Object ref = appId_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            appId_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 填应用AppId
         * </pre>
         *
         * <code>string app_id = 2;</code>
         * @param value The appId to set.
         * @return This builder for chaining.
         */
        public Builder setAppId(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          appId_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 填应用AppId
         * </pre>
         *
         * <code>string app_id = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearAppId() {
          appId_ = getDefaultInstance().getAppId();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 填应用AppId
         * </pre>
         *
         * <code>string app_id = 2;</code>
         * @param value The bytes for appId to set.
         * @return This builder for chaining.
         */
        public Builder setAppIdBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          appId_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }

        private java.lang.Object path_ = "";
        /**
         * <pre>
         * 拉起小程序页面的可带参路径，不填默认拉起小程序首页
         * </pre>
         *
         * <code>string path = 3;</code>
         * @return The path.
         */
        public java.lang.String getPath() {
          java.lang.Object ref = path_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            path_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 拉起小程序页面的可带参路径，不填默认拉起小程序首页
         * </pre>
         *
         * <code>string path = 3;</code>
         * @return The bytes for path.
         */
        public com.google.protobuf.ByteString
            getPathBytes() {
          java.lang.Object ref = path_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            path_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 拉起小程序页面的可带参路径，不填默认拉起小程序首页
         * </pre>
         *
         * <code>string path = 3;</code>
         * @param value The path to set.
         * @return This builder for chaining.
         */
        public Builder setPath(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          path_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 拉起小程序页面的可带参路径，不填默认拉起小程序首页
         * </pre>
         *
         * <code>string path = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearPath() {
          path_ = getDefaultInstance().getPath();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 拉起小程序页面的可带参路径，不填默认拉起小程序首页
         * </pre>
         *
         * <code>string path = 3;</code>
         * @param value The bytes for path to set.
         * @return This builder for chaining.
         */
        public Builder setPathBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          path_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:Ad.MaterialMeta.WxMpInfo)
      }

      // @@protoc_insertion_point(class_scope:Ad.MaterialMeta.WxMpInfo)
      private static final cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo();
      }

      public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<WxMpInfo>
          PARSER = new com.google.protobuf.AbstractParser<WxMpInfo>() {
        @java.lang.Override
        public WxMpInfo parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<WxMpInfo> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<WxMpInfo> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    private int bitField0_;
    public static final int CREATIVE_ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object creativeId_ = "";
    /**
     * <pre>
     * 创意的唯一标志
     * </pre>
     *
     * <code>string creative_id = 1;</code>
     * @return The creativeId.
     */
    @java.lang.Override
    public java.lang.String getCreativeId() {
      java.lang.Object ref = creativeId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        creativeId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 创意的唯一标志
     * </pre>
     *
     * <code>string creative_id = 1;</code>
     * @return The bytes for creativeId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCreativeIdBytes() {
      java.lang.Object ref = creativeId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        creativeId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CREATIVE_TYPE_FIELD_NUMBER = 2;
    private int creativeType_ = 0;
    /**
     * <pre>
     * 必填。该广告的创意类型，和Adslot对应。1：单图，2：多图，3：视频
     * </pre>
     *
     * <code>int32 creative_type = 2;</code>
     * @return The creativeType.
     */
    @java.lang.Override
    public int getCreativeType() {
      return creativeType_;
    }

    public static final int INTERACTION_TYPE_FIELD_NUMBER = 3;
    private int interactionType_ = 0;
    /**
     * <pre>
     * 广告支持的交互类型，和Adslot对象。3：应用内打开 4：download
     * </pre>
     *
     * <code>int32 interaction_type = 3;</code>
     * @return The interactionType.
     */
    @java.lang.Override
    public int getInteractionType() {
      return interactionType_;
    }

    public static final int TITLE_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object title_ = "";
    /**
     * <pre>
     * 可选。广告标题。信息流必填
     * </pre>
     *
     * <code>string title = 4;</code>
     * @return The title.
     */
    @java.lang.Override
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        title_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。广告标题。信息流必填
     * </pre>
     *
     * <code>string title = 4;</code>
     * @return The bytes for title.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DESCRIPTION_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object description_ = "";
    /**
     * <pre>
     * 可选。广告描述。信息流必填
     * </pre>
     *
     * <code>string description = 5;</code>
     * @return The description.
     */
    @java.lang.Override
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。广告描述。信息流必填
     * </pre>
     *
     * <code>string description = 5;</code>
     * @return The bytes for description.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_NAME_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object appName_ = "";
    /**
     * <pre>
     * 可选。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_name = 6;</code>
     * @return The appName.
     */
    @java.lang.Override
    public java.lang.String getAppName() {
      java.lang.Object ref = appName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_name = 6;</code>
     * @return The bytes for appName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppNameBytes() {
      java.lang.Object ref = appName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PACKAGE_NAME_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private volatile java.lang.Object packageName_ = "";
    /**
     * <pre>
     * 下载类广告（包括deeplink）。IOS填写bundleID，Android填写包名
     * </pre>
     *
     * <code>string package_name = 7;</code>
     * @return The packageName.
     */
    @java.lang.Override
    public java.lang.String getPackageName() {
      java.lang.Object ref = packageName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        packageName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 下载类广告（包括deeplink）。IOS填写bundleID，Android填写包名
     * </pre>
     *
     * <code>string package_name = 7;</code>
     * @return The bytes for packageName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPackageNameBytes() {
      java.lang.Object ref = packageName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        packageName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ICON_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object icon_ = "";
    /**
     * <pre>
     * 广告创意的图标URL
     * </pre>
     *
     * <code>string icon = 8;</code>
     * @return The icon.
     */
    @java.lang.Override
    public java.lang.String getIcon() {
      java.lang.Object ref = icon_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        icon_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 广告创意的图标URL
     * </pre>
     *
     * <code>string icon = 8;</code>
     * @return The bytes for icon.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIconBytes() {
      java.lang.Object ref = icon_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        icon_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IMAGE_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image> image_;
    /**
     * <pre>
     * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
     */
    @java.lang.Override
    public java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image> getImageList() {
      return image_;
    }
    /**
     * <pre>
     * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
     */
    @java.lang.Override
    public java.util.List<? extends cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.ImageOrBuilder> 
        getImageOrBuilderList() {
      return image_;
    }
    /**
     * <pre>
     * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
     */
    @java.lang.Override
    public int getImageCount() {
      return image_.size();
    }
    /**
     * <pre>
     * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image getImage(int index) {
      return image_.get(index);
    }
    /**
     * <pre>
     * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.ImageOrBuilder getImageOrBuilder(
        int index) {
      return image_.get(index);
    }

    public static final int VIDEO_FIELD_NUMBER = 10;
    private cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video video_;
    /**
     * <pre>
     * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
     * </pre>
     *
     * <code>.Ad.MaterialMeta.Video video = 10;</code>
     * @return Whether the video field is set.
     */
    @java.lang.Override
    public boolean hasVideo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
     * </pre>
     *
     * <code>.Ad.MaterialMeta.Video video = 10;</code>
     * @return The video.
     */
    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video getVideo() {
      return video_ == null ? cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.getDefaultInstance() : video_;
    }
    /**
     * <pre>
     * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
     * </pre>
     *
     * <code>.Ad.MaterialMeta.Video video = 10;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.VideoOrBuilder getVideoOrBuilder() {
      return video_ == null ? cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.getDefaultInstance() : video_;
    }

    public static final int TARGET_URL_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private volatile java.lang.Object targetUrl_ = "";
    /**
     * <pre>
     * 可选。点击创意的响应地址url。
     * </pre>
     *
     * <code>string target_url = 12;</code>
     * @return The targetUrl.
     */
    @java.lang.Override
    public java.lang.String getTargetUrl() {
      java.lang.Object ref = targetUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        targetUrl_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。点击创意的响应地址url。
     * </pre>
     *
     * <code>string target_url = 12;</code>
     * @return The bytes for targetUrl.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTargetUrlBytes() {
      java.lang.Object ref = targetUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DOWNLOAD_URL_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private volatile java.lang.Object downloadUrl_ = "";
    /**
     * <pre>
     * 应用直接下载url，只Android流量。
     * </pre>
     *
     * <code>string download_url = 13;</code>
     * @return The downloadUrl.
     */
    @java.lang.Override
    public java.lang.String getDownloadUrl() {
      java.lang.Object ref = downloadUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        downloadUrl_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 应用直接下载url，只Android流量。
     * </pre>
     *
     * <code>string download_url = 13;</code>
     * @return The bytes for downloadUrl.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDownloadUrlBytes() {
      java.lang.Object ref = downloadUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        downloadUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DEEPLINK_URL_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private volatile java.lang.Object deeplinkUrl_ = "";
    /**
     * <pre>
     * 应用吊起的直达链接。如果该url非空，优先使用该直达链接
     * </pre>
     *
     * <code>string deeplink_url = 14;</code>
     * @return The deeplinkUrl.
     */
    @java.lang.Override
    public java.lang.String getDeeplinkUrl() {
      java.lang.Object ref = deeplinkUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deeplinkUrl_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 应用吊起的直达链接。如果该url非空，优先使用该直达链接
     * </pre>
     *
     * <code>string deeplink_url = 14;</code>
     * @return The bytes for deeplinkUrl.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDeeplinkUrlBytes() {
      java.lang.Object ref = deeplinkUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deeplinkUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TRACKING_EVENT_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking> trackingEvent_;
    /**
     * <pre>
     * 用于各种打点的url
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
     */
    @java.lang.Override
    public java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking> getTrackingEventList() {
      return trackingEvent_;
    }
    /**
     * <pre>
     * 用于各种打点的url
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
     */
    @java.lang.Override
    public java.util.List<? extends cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.TrackingOrBuilder> 
        getTrackingEventOrBuilderList() {
      return trackingEvent_;
    }
    /**
     * <pre>
     * 用于各种打点的url
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
     */
    @java.lang.Override
    public int getTrackingEventCount() {
      return trackingEvent_.size();
    }
    /**
     * <pre>
     * 用于各种打点的url
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking getTrackingEvent(int index) {
      return trackingEvent_.get(index);
    }
    /**
     * <pre>
     * 用于各种打点的url
     * </pre>
     *
     * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.TrackingOrBuilder getTrackingEventOrBuilder(
        int index) {
      return trackingEvent_.get(index);
    }

    public static final int WIN_NOTICE_URL_FIELD_NUMBER = 16;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList winNoticeUrl_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 竞价模式时， win notice填写在这里
     * </pre>
     *
     * <code>repeated string win_notice_url = 16;</code>
     * @return A list containing the winNoticeUrl.
     */
    public com.google.protobuf.ProtocolStringList
        getWinNoticeUrlList() {
      return winNoticeUrl_;
    }
    /**
     * <pre>
     * 竞价模式时， win notice填写在这里
     * </pre>
     *
     * <code>repeated string win_notice_url = 16;</code>
     * @return The count of winNoticeUrl.
     */
    public int getWinNoticeUrlCount() {
      return winNoticeUrl_.size();
    }
    /**
     * <pre>
     * 竞价模式时， win notice填写在这里
     * </pre>
     *
     * <code>repeated string win_notice_url = 16;</code>
     * @param index The index of the element to return.
     * @return The winNoticeUrl at the given index.
     */
    public java.lang.String getWinNoticeUrl(int index) {
      return winNoticeUrl_.get(index);
    }
    /**
     * <pre>
     * 竞价模式时， win notice填写在这里
     * </pre>
     *
     * <code>repeated string win_notice_url = 16;</code>
     * @param index The index of the value to return.
     * @return The bytes of the winNoticeUrl at the given index.
     */
    public com.google.protobuf.ByteString
        getWinNoticeUrlBytes(int index) {
      return winNoticeUrl_.getByteString(index);
    }

    public static final int DEAL_ID_FIELD_NUMBER = 17;
    @SuppressWarnings("serial")
    private volatile java.lang.Object dealId_ = "";
    /**
     * <pre>
     * 在PD合作模式下返回
     * </pre>
     *
     * <code>string deal_id = 17;</code>
     * @return The dealId.
     */
    @java.lang.Override
    public java.lang.String getDealId() {
      java.lang.Object ref = dealId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        dealId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 在PD合作模式下返回
     * </pre>
     *
     * <code>string deal_id = 17;</code>
     * @return The bytes for dealId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDealIdBytes() {
      java.lang.Object ref = dealId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        dealId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MP_FIELD_NUMBER = 19;
    private cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo mp_;
    /**
     * <pre>
     * 小程序对象
     * </pre>
     *
     * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
     * @return Whether the mp field is set.
     */
    @java.lang.Override
    public boolean hasMp() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 小程序对象
     * </pre>
     *
     * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
     * @return The mp.
     */
    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo getMp() {
      return mp_ == null ? cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.getDefaultInstance() : mp_;
    }
    /**
     * <pre>
     * 小程序对象
     * </pre>
     *
     * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfoOrBuilder getMpOrBuilder() {
      return mp_ == null ? cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.getDefaultInstance() : mp_;
    }

    public static final int SOURCE_FIELD_NUMBER = 20;
    @SuppressWarnings("serial")
    private volatile java.lang.Object source_ = "";
    /**
     * <pre>
     * 广告来源
     * </pre>
     *
     * <code>string source = 20;</code>
     * @return The source.
     */
    @java.lang.Override
    public java.lang.String getSource() {
      java.lang.Object ref = source_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        source_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 广告来源
     * </pre>
     *
     * <code>string source = 20;</code>
     * @return The bytes for source.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSourceBytes() {
      java.lang.Object ref = source_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        source_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int UNIVERSAL_LINK_FIELD_NUMBER = 21;
    @SuppressWarnings("serial")
    private volatile java.lang.Object universalLink_ = "";
    /**
     * <pre>
     * ios端调起连接 universal_link优先级高于deeplink
     * </pre>
     *
     * <code>string universal_link = 21;</code>
     * @return The universalLink.
     */
    @java.lang.Override
    public java.lang.String getUniversalLink() {
      java.lang.Object ref = universalLink_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        universalLink_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * ios端调起连接 universal_link优先级高于deeplink
     * </pre>
     *
     * <code>string universal_link = 21;</code>
     * @return The bytes for universalLink.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUniversalLinkBytes() {
      java.lang.Object ref = universalLink_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        universalLink_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_CHANNEL_FIELD_NUMBER = 22;
    @SuppressWarnings("serial")
    private volatile java.lang.Object appChannel_ = "";
    /**
     * <pre>
     * DSP在应用商店推广应用的时候，应用商店分配给DSP的渠道号
     * </pre>
     *
     * <code>string app_channel = 22;</code>
     * @return The appChannel.
     */
    @java.lang.Override
    public java.lang.String getAppChannel() {
      java.lang.Object ref = appChannel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appChannel_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * DSP在应用商店推广应用的时候，应用商店分配给DSP的渠道号
     * </pre>
     *
     * <code>string app_channel = 22;</code>
     * @return The bytes for appChannel.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppChannelBytes() {
      java.lang.Object ref = appChannel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appChannel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_DESC_FIELD_NUMBER = 23;
    @SuppressWarnings("serial")
    private volatile java.lang.Object appDesc_ = "";
    /**
     * <pre>
     * 可选。应用介绍。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_desc = 23;</code>
     * @return The appDesc.
     */
    @java.lang.Override
    public java.lang.String getAppDesc() {
      java.lang.Object ref = appDesc_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appDesc_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。应用介绍。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_desc = 23;</code>
     * @return The bytes for appDesc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppDescBytes() {
      java.lang.Object ref = appDesc_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appDesc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_PACKAGE_SIZE_FIELD_NUMBER = 24;
    private float appPackageSize_ = 0F;
    /**
     * <pre>
     * 可选。应用大小，单位MB。针对应用下载类广告。
     * </pre>
     *
     * <code>float app_package_size = 24;</code>
     * @return The appPackageSize.
     */
    @java.lang.Override
    public float getAppPackageSize() {
      return appPackageSize_;
    }

    public static final int APP_PUBLISHER_FIELD_NUMBER = 25;
    @SuppressWarnings("serial")
    private volatile java.lang.Object appPublisher_ = "";
    /**
     * <pre>
     * 可选。APP开发者主体名称。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_publisher = 25;</code>
     * @return The appPublisher.
     */
    @java.lang.Override
    public java.lang.String getAppPublisher() {
      java.lang.Object ref = appPublisher_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appPublisher_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。APP开发者主体名称。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_publisher = 25;</code>
     * @return The bytes for appPublisher.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppPublisherBytes() {
      java.lang.Object ref = appPublisher_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appPublisher_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DOWN_APP_VERSION_FIELD_NUMBER = 26;
    @SuppressWarnings("serial")
    private volatile java.lang.Object downAppVersion_ = "";
    /**
     * <pre>
     * 可选。下载APP版本号。针对应用下载类广告。
     * </pre>
     *
     * <code>string down_app_version = 26;</code>
     * @return The downAppVersion.
     */
    @java.lang.Override
    public java.lang.String getDownAppVersion() {
      java.lang.Object ref = downAppVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        downAppVersion_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。下载APP版本号。针对应用下载类广告。
     * </pre>
     *
     * <code>string down_app_version = 26;</code>
     * @return The bytes for downAppVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDownAppVersionBytes() {
      java.lang.Object ref = downAppVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        downAppVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PRIVACY_LINK_FIELD_NUMBER = 27;
    @SuppressWarnings("serial")
    private volatile java.lang.Object privacyLink_ = "";
    /**
     * <pre>
     * 可选。隐私协议URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string privacy_link = 27;</code>
     * @return The privacyLink.
     */
    @java.lang.Override
    public java.lang.String getPrivacyLink() {
      java.lang.Object ref = privacyLink_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        privacyLink_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。隐私协议URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string privacy_link = 27;</code>
     * @return The bytes for privacyLink.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPrivacyLinkBytes() {
      java.lang.Object ref = privacyLink_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        privacyLink_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PERMISSION_LINK_FIELD_NUMBER = 28;
    @SuppressWarnings("serial")
    private volatile java.lang.Object permissionLink_ = "";
    /**
     * <pre>
     * 可选。用户权限URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string permission_link = 28;</code>
     * @return The permissionLink.
     */
    @java.lang.Override
    public java.lang.String getPermissionLink() {
      java.lang.Object ref = permissionLink_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        permissionLink_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。用户权限URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string permission_link = 28;</code>
     * @return The bytes for permissionLink.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPermissionLinkBytes() {
      java.lang.Object ref = permissionLink_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        permissionLink_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int INTRODUCE_LINK_FIELD_NUMBER = 29;
    @SuppressWarnings("serial")
    private volatile java.lang.Object introduceLink_ = "";
    /**
     * <pre>
     * 可选。应用介绍URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string introduce_link = 29;</code>
     * @return The introduceLink.
     */
    @java.lang.Override
    public java.lang.String getIntroduceLink() {
      java.lang.Object ref = introduceLink_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        introduceLink_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。应用介绍URL。针对应用下载类广告。
     * </pre>
     *
     * <code>string introduce_link = 29;</code>
     * @return The bytes for introduceLink.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIntroduceLinkBytes() {
      java.lang.Object ref = introduceLink_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        introduceLink_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_LCP_NUMBER_FIELD_NUMBER = 30;
    @SuppressWarnings("serial")
    private volatile java.lang.Object appLcpNumber_ = "";
    /**
     * <pre>
     * 可选。下载应用备案号 LCP。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_lcp_number = 30;</code>
     * @return The appLcpNumber.
     */
    @java.lang.Override
    public java.lang.String getAppLcpNumber() {
      java.lang.Object ref = appLcpNumber_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appLcpNumber_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。下载应用备案号 LCP。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_lcp_number = 30;</code>
     * @return The bytes for appLcpNumber.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppLcpNumberBytes() {
      java.lang.Object ref = appLcpNumber_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appLcpNumber_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_SUITABLE_AGE_FIELD_NUMBER = 31;
    @SuppressWarnings("serial")
    private volatile java.lang.Object appSuitableAge_ = "";
    /**
     * <pre>
     * 可选。下载应用实用年龄。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_suitable_age = 31;</code>
     * @return The appSuitableAge.
     */
    @java.lang.Override
    public java.lang.String getAppSuitableAge() {
      java.lang.Object ref = appSuitableAge_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appSuitableAge_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可选。下载应用实用年龄。针对应用下载类广告。
     * </pre>
     *
     * <code>string app_suitable_age = 31;</code>
     * @return The bytes for appSuitableAge.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppSuitableAgeBytes() {
      java.lang.Object ref = appSuitableAge_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appSuitableAge_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int REPLACEMENT_TYPE_FIELD_NUMBER = 32;
    private int replacementType_ = 0;
    /**
     * <pre>
     * 点击坐标替换类型 0：浮点型；1：整型；
     * </pre>
     *
     * <code>int32 replacement_type = 32;</code>
     * @return The replacementType.
     */
    @java.lang.Override
    public int getReplacementType() {
      return replacementType_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creativeId_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, creativeId_);
      }
      if (creativeType_ != 0) {
        output.writeInt32(2, creativeType_);
      }
      if (interactionType_ != 0) {
        output.writeInt32(3, interactionType_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, title_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(description_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, description_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appName_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, appName_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(packageName_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 7, packageName_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(icon_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, icon_);
      }
      for (int i = 0; i < image_.size(); i++) {
        output.writeMessage(9, image_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(10, getVideo());
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(targetUrl_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 12, targetUrl_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(downloadUrl_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 13, downloadUrl_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(deeplinkUrl_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 14, deeplinkUrl_);
      }
      for (int i = 0; i < trackingEvent_.size(); i++) {
        output.writeMessage(15, trackingEvent_.get(i));
      }
      for (int i = 0; i < winNoticeUrl_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 16, winNoticeUrl_.getRaw(i));
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(dealId_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 17, dealId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(19, getMp());
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(source_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 20, source_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(universalLink_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 21, universalLink_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appChannel_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 22, appChannel_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appDesc_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 23, appDesc_);
      }
      if (java.lang.Float.floatToRawIntBits(appPackageSize_) != 0) {
        output.writeFloat(24, appPackageSize_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appPublisher_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 25, appPublisher_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(downAppVersion_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 26, downAppVersion_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(privacyLink_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 27, privacyLink_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(permissionLink_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 28, permissionLink_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(introduceLink_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 29, introduceLink_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appLcpNumber_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 30, appLcpNumber_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appSuitableAge_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 31, appSuitableAge_);
      }
      if (replacementType_ != 0) {
        output.writeInt32(32, replacementType_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creativeId_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, creativeId_);
      }
      if (creativeType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, creativeType_);
      }
      if (interactionType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, interactionType_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, title_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(description_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, description_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appName_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(6, appName_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(packageName_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(7, packageName_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(icon_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(8, icon_);
      }
      for (int i = 0; i < image_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, image_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(10, getVideo());
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(targetUrl_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(12, targetUrl_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(downloadUrl_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(13, downloadUrl_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(deeplinkUrl_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(14, deeplinkUrl_);
      }
      for (int i = 0; i < trackingEvent_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(15, trackingEvent_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < winNoticeUrl_.size(); i++) {
          dataSize += computeStringSizeNoTag(winNoticeUrl_.getRaw(i));
        }
        size += dataSize;
        size += 2 * getWinNoticeUrlList().size();
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(dealId_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(17, dealId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(19, getMp());
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(source_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(20, source_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(universalLink_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(21, universalLink_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appChannel_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(22, appChannel_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appDesc_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(23, appDesc_);
      }
      if (java.lang.Float.floatToRawIntBits(appPackageSize_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(24, appPackageSize_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appPublisher_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(25, appPublisher_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(downAppVersion_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(26, downAppVersion_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(privacyLink_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(27, privacyLink_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(permissionLink_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(28, permissionLink_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(introduceLink_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(29, introduceLink_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appLcpNumber_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(30, appLcpNumber_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appSuitableAge_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(31, appSuitableAge_);
      }
      if (replacementType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(32, replacementType_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta)) {
        return super.equals(obj);
      }
      cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta other = (cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta) obj;

      if (!getCreativeId()
          .equals(other.getCreativeId())) return false;
      if (getCreativeType()
          != other.getCreativeType()) return false;
      if (getInteractionType()
          != other.getInteractionType()) return false;
      if (!getTitle()
          .equals(other.getTitle())) return false;
      if (!getDescription()
          .equals(other.getDescription())) return false;
      if (!getAppName()
          .equals(other.getAppName())) return false;
      if (!getPackageName()
          .equals(other.getPackageName())) return false;
      if (!getIcon()
          .equals(other.getIcon())) return false;
      if (!getImageList()
          .equals(other.getImageList())) return false;
      if (hasVideo() != other.hasVideo()) return false;
      if (hasVideo()) {
        if (!getVideo()
            .equals(other.getVideo())) return false;
      }
      if (!getTargetUrl()
          .equals(other.getTargetUrl())) return false;
      if (!getDownloadUrl()
          .equals(other.getDownloadUrl())) return false;
      if (!getDeeplinkUrl()
          .equals(other.getDeeplinkUrl())) return false;
      if (!getTrackingEventList()
          .equals(other.getTrackingEventList())) return false;
      if (!getWinNoticeUrlList()
          .equals(other.getWinNoticeUrlList())) return false;
      if (!getDealId()
          .equals(other.getDealId())) return false;
      if (hasMp() != other.hasMp()) return false;
      if (hasMp()) {
        if (!getMp()
            .equals(other.getMp())) return false;
      }
      if (!getSource()
          .equals(other.getSource())) return false;
      if (!getUniversalLink()
          .equals(other.getUniversalLink())) return false;
      if (!getAppChannel()
          .equals(other.getAppChannel())) return false;
      if (!getAppDesc()
          .equals(other.getAppDesc())) return false;
      if (java.lang.Float.floatToIntBits(getAppPackageSize())
          != java.lang.Float.floatToIntBits(
              other.getAppPackageSize())) return false;
      if (!getAppPublisher()
          .equals(other.getAppPublisher())) return false;
      if (!getDownAppVersion()
          .equals(other.getDownAppVersion())) return false;
      if (!getPrivacyLink()
          .equals(other.getPrivacyLink())) return false;
      if (!getPermissionLink()
          .equals(other.getPermissionLink())) return false;
      if (!getIntroduceLink()
          .equals(other.getIntroduceLink())) return false;
      if (!getAppLcpNumber()
          .equals(other.getAppLcpNumber())) return false;
      if (!getAppSuitableAge()
          .equals(other.getAppSuitableAge())) return false;
      if (getReplacementType()
          != other.getReplacementType()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CREATIVE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getCreativeId().hashCode();
      hash = (37 * hash) + CREATIVE_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getCreativeType();
      hash = (37 * hash) + INTERACTION_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getInteractionType();
      hash = (37 * hash) + TITLE_FIELD_NUMBER;
      hash = (53 * hash) + getTitle().hashCode();
      hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
      hash = (53 * hash) + getDescription().hashCode();
      hash = (37 * hash) + APP_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getAppName().hashCode();
      hash = (37 * hash) + PACKAGE_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getPackageName().hashCode();
      hash = (37 * hash) + ICON_FIELD_NUMBER;
      hash = (53 * hash) + getIcon().hashCode();
      if (getImageCount() > 0) {
        hash = (37 * hash) + IMAGE_FIELD_NUMBER;
        hash = (53 * hash) + getImageList().hashCode();
      }
      if (hasVideo()) {
        hash = (37 * hash) + VIDEO_FIELD_NUMBER;
        hash = (53 * hash) + getVideo().hashCode();
      }
      hash = (37 * hash) + TARGET_URL_FIELD_NUMBER;
      hash = (53 * hash) + getTargetUrl().hashCode();
      hash = (37 * hash) + DOWNLOAD_URL_FIELD_NUMBER;
      hash = (53 * hash) + getDownloadUrl().hashCode();
      hash = (37 * hash) + DEEPLINK_URL_FIELD_NUMBER;
      hash = (53 * hash) + getDeeplinkUrl().hashCode();
      if (getTrackingEventCount() > 0) {
        hash = (37 * hash) + TRACKING_EVENT_FIELD_NUMBER;
        hash = (53 * hash) + getTrackingEventList().hashCode();
      }
      if (getWinNoticeUrlCount() > 0) {
        hash = (37 * hash) + WIN_NOTICE_URL_FIELD_NUMBER;
        hash = (53 * hash) + getWinNoticeUrlList().hashCode();
      }
      hash = (37 * hash) + DEAL_ID_FIELD_NUMBER;
      hash = (53 * hash) + getDealId().hashCode();
      if (hasMp()) {
        hash = (37 * hash) + MP_FIELD_NUMBER;
        hash = (53 * hash) + getMp().hashCode();
      }
      hash = (37 * hash) + SOURCE_FIELD_NUMBER;
      hash = (53 * hash) + getSource().hashCode();
      hash = (37 * hash) + UNIVERSAL_LINK_FIELD_NUMBER;
      hash = (53 * hash) + getUniversalLink().hashCode();
      hash = (37 * hash) + APP_CHANNEL_FIELD_NUMBER;
      hash = (53 * hash) + getAppChannel().hashCode();
      hash = (37 * hash) + APP_DESC_FIELD_NUMBER;
      hash = (53 * hash) + getAppDesc().hashCode();
      hash = (37 * hash) + APP_PACKAGE_SIZE_FIELD_NUMBER;
      hash = (53 * hash) + java.lang.Float.floatToIntBits(
          getAppPackageSize());
      hash = (37 * hash) + APP_PUBLISHER_FIELD_NUMBER;
      hash = (53 * hash) + getAppPublisher().hashCode();
      hash = (37 * hash) + DOWN_APP_VERSION_FIELD_NUMBER;
      hash = (53 * hash) + getDownAppVersion().hashCode();
      hash = (37 * hash) + PRIVACY_LINK_FIELD_NUMBER;
      hash = (53 * hash) + getPrivacyLink().hashCode();
      hash = (37 * hash) + PERMISSION_LINK_FIELD_NUMBER;
      hash = (53 * hash) + getPermissionLink().hashCode();
      hash = (37 * hash) + INTRODUCE_LINK_FIELD_NUMBER;
      hash = (53 * hash) + getIntroduceLink().hashCode();
      hash = (37 * hash) + APP_LCP_NUMBER_FIELD_NUMBER;
      hash = (53 * hash) + getAppLcpNumber().hashCode();
      hash = (37 * hash) + APP_SUITABLE_AGE_FIELD_NUMBER;
      hash = (53 * hash) + getAppSuitableAge().hashCode();
      hash = (37 * hash) + REPLACEMENT_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getReplacementType();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Ad.MaterialMeta}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Ad.MaterialMeta)
        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMetaOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.class, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Builder.class);
      }

      // Construct using cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getImageFieldBuilder();
          getVideoFieldBuilder();
          getTrackingEventFieldBuilder();
          getMpFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        creativeId_ = "";
        creativeType_ = 0;
        interactionType_ = 0;
        title_ = "";
        description_ = "";
        appName_ = "";
        packageName_ = "";
        icon_ = "";
        if (imageBuilder_ == null) {
          image_ = java.util.Collections.emptyList();
        } else {
          image_ = null;
          imageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        video_ = null;
        if (videoBuilder_ != null) {
          videoBuilder_.dispose();
          videoBuilder_ = null;
        }
        targetUrl_ = "";
        downloadUrl_ = "";
        deeplinkUrl_ = "";
        if (trackingEventBuilder_ == null) {
          trackingEvent_ = java.util.Collections.emptyList();
        } else {
          trackingEvent_ = null;
          trackingEventBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00002000);
        winNoticeUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        dealId_ = "";
        mp_ = null;
        if (mpBuilder_ != null) {
          mpBuilder_.dispose();
          mpBuilder_ = null;
        }
        source_ = "";
        universalLink_ = "";
        appChannel_ = "";
        appDesc_ = "";
        appPackageSize_ = 0F;
        appPublisher_ = "";
        downAppVersion_ = "";
        privacyLink_ = "";
        permissionLink_ = "";
        introduceLink_ = "";
        appLcpNumber_ = "";
        appSuitableAge_ = "";
        replacementType_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_MaterialMeta_descriptor;
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta getDefaultInstanceForType() {
        return cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.getDefaultInstance();
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta build() {
        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta buildPartial() {
        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta result = new cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta result) {
        if (imageBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0)) {
            image_ = java.util.Collections.unmodifiableList(image_);
            bitField0_ = (bitField0_ & ~0x00000100);
          }
          result.image_ = image_;
        } else {
          result.image_ = imageBuilder_.build();
        }
        if (trackingEventBuilder_ == null) {
          if (((bitField0_ & 0x00002000) != 0)) {
            trackingEvent_ = java.util.Collections.unmodifiableList(trackingEvent_);
            bitField0_ = (bitField0_ & ~0x00002000);
          }
          result.trackingEvent_ = trackingEvent_;
        } else {
          result.trackingEvent_ = trackingEventBuilder_.build();
        }
      }

      private void buildPartial0(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.creativeId_ = creativeId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.creativeType_ = creativeType_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.interactionType_ = interactionType_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.title_ = title_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.description_ = description_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.appName_ = appName_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.packageName_ = packageName_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.icon_ = icon_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.video_ = videoBuilder_ == null
              ? video_
              : videoBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.targetUrl_ = targetUrl_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.downloadUrl_ = downloadUrl_;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.deeplinkUrl_ = deeplinkUrl_;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          winNoticeUrl_.makeImmutable();
          result.winNoticeUrl_ = winNoticeUrl_;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.dealId_ = dealId_;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.mp_ = mpBuilder_ == null
              ? mp_
              : mpBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.source_ = source_;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.universalLink_ = universalLink_;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.appChannel_ = appChannel_;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.appDesc_ = appDesc_;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.appPackageSize_ = appPackageSize_;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.appPublisher_ = appPublisher_;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.downAppVersion_ = downAppVersion_;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.privacyLink_ = privacyLink_;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.permissionLink_ = permissionLink_;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.introduceLink_ = introduceLink_;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.appLcpNumber_ = appLcpNumber_;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.appSuitableAge_ = appSuitableAge_;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.replacementType_ = replacementType_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta) {
          return mergeFrom((cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta other) {
        if (other == cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.getDefaultInstance()) return this;
        if (!other.getCreativeId().isEmpty()) {
          creativeId_ = other.creativeId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.getCreativeType() != 0) {
          setCreativeType(other.getCreativeType());
        }
        if (other.getInteractionType() != 0) {
          setInteractionType(other.getInteractionType());
        }
        if (!other.getTitle().isEmpty()) {
          title_ = other.title_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getDescription().isEmpty()) {
          description_ = other.description_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (!other.getAppName().isEmpty()) {
          appName_ = other.appName_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (!other.getPackageName().isEmpty()) {
          packageName_ = other.packageName_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (!other.getIcon().isEmpty()) {
          icon_ = other.icon_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        if (imageBuilder_ == null) {
          if (!other.image_.isEmpty()) {
            if (image_.isEmpty()) {
              image_ = other.image_;
              bitField0_ = (bitField0_ & ~0x00000100);
            } else {
              ensureImageIsMutable();
              image_.addAll(other.image_);
            }
            onChanged();
          }
        } else {
          if (!other.image_.isEmpty()) {
            if (imageBuilder_.isEmpty()) {
              imageBuilder_.dispose();
              imageBuilder_ = null;
              image_ = other.image_;
              bitField0_ = (bitField0_ & ~0x00000100);
              imageBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getImageFieldBuilder() : null;
            } else {
              imageBuilder_.addAllMessages(other.image_);
            }
          }
        }
        if (other.hasVideo()) {
          mergeVideo(other.getVideo());
        }
        if (!other.getTargetUrl().isEmpty()) {
          targetUrl_ = other.targetUrl_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (!other.getDownloadUrl().isEmpty()) {
          downloadUrl_ = other.downloadUrl_;
          bitField0_ |= 0x00000800;
          onChanged();
        }
        if (!other.getDeeplinkUrl().isEmpty()) {
          deeplinkUrl_ = other.deeplinkUrl_;
          bitField0_ |= 0x00001000;
          onChanged();
        }
        if (trackingEventBuilder_ == null) {
          if (!other.trackingEvent_.isEmpty()) {
            if (trackingEvent_.isEmpty()) {
              trackingEvent_ = other.trackingEvent_;
              bitField0_ = (bitField0_ & ~0x00002000);
            } else {
              ensureTrackingEventIsMutable();
              trackingEvent_.addAll(other.trackingEvent_);
            }
            onChanged();
          }
        } else {
          if (!other.trackingEvent_.isEmpty()) {
            if (trackingEventBuilder_.isEmpty()) {
              trackingEventBuilder_.dispose();
              trackingEventBuilder_ = null;
              trackingEvent_ = other.trackingEvent_;
              bitField0_ = (bitField0_ & ~0x00002000);
              trackingEventBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getTrackingEventFieldBuilder() : null;
            } else {
              trackingEventBuilder_.addAllMessages(other.trackingEvent_);
            }
          }
        }
        if (!other.winNoticeUrl_.isEmpty()) {
          if (winNoticeUrl_.isEmpty()) {
            winNoticeUrl_ = other.winNoticeUrl_;
            bitField0_ |= 0x00004000;
          } else {
            ensureWinNoticeUrlIsMutable();
            winNoticeUrl_.addAll(other.winNoticeUrl_);
          }
          onChanged();
        }
        if (!other.getDealId().isEmpty()) {
          dealId_ = other.dealId_;
          bitField0_ |= 0x00008000;
          onChanged();
        }
        if (other.hasMp()) {
          mergeMp(other.getMp());
        }
        if (!other.getSource().isEmpty()) {
          source_ = other.source_;
          bitField0_ |= 0x00020000;
          onChanged();
        }
        if (!other.getUniversalLink().isEmpty()) {
          universalLink_ = other.universalLink_;
          bitField0_ |= 0x00040000;
          onChanged();
        }
        if (!other.getAppChannel().isEmpty()) {
          appChannel_ = other.appChannel_;
          bitField0_ |= 0x00080000;
          onChanged();
        }
        if (!other.getAppDesc().isEmpty()) {
          appDesc_ = other.appDesc_;
          bitField0_ |= 0x00100000;
          onChanged();
        }
        if (other.getAppPackageSize() != 0F) {
          setAppPackageSize(other.getAppPackageSize());
        }
        if (!other.getAppPublisher().isEmpty()) {
          appPublisher_ = other.appPublisher_;
          bitField0_ |= 0x00400000;
          onChanged();
        }
        if (!other.getDownAppVersion().isEmpty()) {
          downAppVersion_ = other.downAppVersion_;
          bitField0_ |= 0x00800000;
          onChanged();
        }
        if (!other.getPrivacyLink().isEmpty()) {
          privacyLink_ = other.privacyLink_;
          bitField0_ |= 0x01000000;
          onChanged();
        }
        if (!other.getPermissionLink().isEmpty()) {
          permissionLink_ = other.permissionLink_;
          bitField0_ |= 0x02000000;
          onChanged();
        }
        if (!other.getIntroduceLink().isEmpty()) {
          introduceLink_ = other.introduceLink_;
          bitField0_ |= 0x04000000;
          onChanged();
        }
        if (!other.getAppLcpNumber().isEmpty()) {
          appLcpNumber_ = other.appLcpNumber_;
          bitField0_ |= 0x08000000;
          onChanged();
        }
        if (!other.getAppSuitableAge().isEmpty()) {
          appSuitableAge_ = other.appSuitableAge_;
          bitField0_ |= 0x10000000;
          onChanged();
        }
        if (other.getReplacementType() != 0) {
          setReplacementType(other.getReplacementType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                creativeId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                creativeType_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                interactionType_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                title_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                description_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                appName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 58: {
                packageName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                icon_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image m =
                    input.readMessage(
                        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.parser(),
                        extensionRegistry);
                if (imageBuilder_ == null) {
                  ensureImageIsMutable();
                  image_.add(m);
                } else {
                  imageBuilder_.addMessage(m);
                }
                break;
              } // case 74
              case 82: {
                input.readMessage(
                    getVideoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 98: {
                targetUrl_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000400;
                break;
              } // case 98
              case 106: {
                downloadUrl_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000800;
                break;
              } // case 106
              case 114: {
                deeplinkUrl_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00001000;
                break;
              } // case 114
              case 122: {
                cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking m =
                    input.readMessage(
                        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.parser(),
                        extensionRegistry);
                if (trackingEventBuilder_ == null) {
                  ensureTrackingEventIsMutable();
                  trackingEvent_.add(m);
                } else {
                  trackingEventBuilder_.addMessage(m);
                }
                break;
              } // case 122
              case 130: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureWinNoticeUrlIsMutable();
                winNoticeUrl_.add(s);
                break;
              } // case 130
              case 138: {
                dealId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00008000;
                break;
              } // case 138
              case 154: {
                input.readMessage(
                    getMpFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00010000;
                break;
              } // case 154
              case 162: {
                source_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00020000;
                break;
              } // case 162
              case 170: {
                universalLink_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00040000;
                break;
              } // case 170
              case 178: {
                appChannel_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00080000;
                break;
              } // case 178
              case 186: {
                appDesc_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00100000;
                break;
              } // case 186
              case 197: {
                appPackageSize_ = input.readFloat();
                bitField0_ |= 0x00200000;
                break;
              } // case 197
              case 202: {
                appPublisher_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00400000;
                break;
              } // case 202
              case 210: {
                downAppVersion_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00800000;
                break;
              } // case 210
              case 218: {
                privacyLink_ = input.readStringRequireUtf8();
                bitField0_ |= 0x01000000;
                break;
              } // case 218
              case 226: {
                permissionLink_ = input.readStringRequireUtf8();
                bitField0_ |= 0x02000000;
                break;
              } // case 226
              case 234: {
                introduceLink_ = input.readStringRequireUtf8();
                bitField0_ |= 0x04000000;
                break;
              } // case 234
              case 242: {
                appLcpNumber_ = input.readStringRequireUtf8();
                bitField0_ |= 0x08000000;
                break;
              } // case 242
              case 250: {
                appSuitableAge_ = input.readStringRequireUtf8();
                bitField0_ |= 0x10000000;
                break;
              } // case 250
              case 256: {
                replacementType_ = input.readInt32();
                bitField0_ |= 0x20000000;
                break;
              } // case 256
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object creativeId_ = "";
      /**
       * <pre>
       * 创意的唯一标志
       * </pre>
       *
       * <code>string creative_id = 1;</code>
       * @return The creativeId.
       */
      public java.lang.String getCreativeId() {
        java.lang.Object ref = creativeId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          creativeId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 创意的唯一标志
       * </pre>
       *
       * <code>string creative_id = 1;</code>
       * @return The bytes for creativeId.
       */
      public com.google.protobuf.ByteString
          getCreativeIdBytes() {
        java.lang.Object ref = creativeId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          creativeId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 创意的唯一标志
       * </pre>
       *
       * <code>string creative_id = 1;</code>
       * @param value The creativeId to set.
       * @return This builder for chaining.
       */
      public Builder setCreativeId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        creativeId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 创意的唯一标志
       * </pre>
       *
       * <code>string creative_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreativeId() {
        creativeId_ = getDefaultInstance().getCreativeId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 创意的唯一标志
       * </pre>
       *
       * <code>string creative_id = 1;</code>
       * @param value The bytes for creativeId to set.
       * @return This builder for chaining.
       */
      public Builder setCreativeIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        creativeId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private int creativeType_ ;
      /**
       * <pre>
       * 必填。该广告的创意类型，和Adslot对应。1：单图，2：多图，3：视频
       * </pre>
       *
       * <code>int32 creative_type = 2;</code>
       * @return The creativeType.
       */
      @java.lang.Override
      public int getCreativeType() {
        return creativeType_;
      }
      /**
       * <pre>
       * 必填。该广告的创意类型，和Adslot对应。1：单图，2：多图，3：视频
       * </pre>
       *
       * <code>int32 creative_type = 2;</code>
       * @param value The creativeType to set.
       * @return This builder for chaining.
       */
      public Builder setCreativeType(int value) {

        creativeType_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 必填。该广告的创意类型，和Adslot对应。1：单图，2：多图，3：视频
       * </pre>
       *
       * <code>int32 creative_type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreativeType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        creativeType_ = 0;
        onChanged();
        return this;
      }

      private int interactionType_ ;
      /**
       * <pre>
       * 广告支持的交互类型，和Adslot对象。3：应用内打开 4：download
       * </pre>
       *
       * <code>int32 interaction_type = 3;</code>
       * @return The interactionType.
       */
      @java.lang.Override
      public int getInteractionType() {
        return interactionType_;
      }
      /**
       * <pre>
       * 广告支持的交互类型，和Adslot对象。3：应用内打开 4：download
       * </pre>
       *
       * <code>int32 interaction_type = 3;</code>
       * @param value The interactionType to set.
       * @return This builder for chaining.
       */
      public Builder setInteractionType(int value) {

        interactionType_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告支持的交互类型，和Adslot对象。3：应用内打开 4：download
       * </pre>
       *
       * <code>int32 interaction_type = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearInteractionType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        interactionType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object title_ = "";
      /**
       * <pre>
       * 可选。广告标题。信息流必填
       * </pre>
       *
       * <code>string title = 4;</code>
       * @return The title.
       */
      public java.lang.String getTitle() {
        java.lang.Object ref = title_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          title_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。广告标题。信息流必填
       * </pre>
       *
       * <code>string title = 4;</code>
       * @return The bytes for title.
       */
      public com.google.protobuf.ByteString
          getTitleBytes() {
        java.lang.Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。广告标题。信息流必填
       * </pre>
       *
       * <code>string title = 4;</code>
       * @param value The title to set.
       * @return This builder for chaining.
       */
      public Builder setTitle(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        title_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。广告标题。信息流必填
       * </pre>
       *
       * <code>string title = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTitle() {
        title_ = getDefaultInstance().getTitle();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。广告标题。信息流必填
       * </pre>
       *
       * <code>string title = 4;</code>
       * @param value The bytes for title to set.
       * @return This builder for chaining.
       */
      public Builder setTitleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        title_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object description_ = "";
      /**
       * <pre>
       * 可选。广告描述。信息流必填
       * </pre>
       *
       * <code>string description = 5;</code>
       * @return The description.
       */
      public java.lang.String getDescription() {
        java.lang.Object ref = description_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          description_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。广告描述。信息流必填
       * </pre>
       *
       * <code>string description = 5;</code>
       * @return The bytes for description.
       */
      public com.google.protobuf.ByteString
          getDescriptionBytes() {
        java.lang.Object ref = description_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          description_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。广告描述。信息流必填
       * </pre>
       *
       * <code>string description = 5;</code>
       * @param value The description to set.
       * @return This builder for chaining.
       */
      public Builder setDescription(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        description_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。广告描述。信息流必填
       * </pre>
       *
       * <code>string description = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDescription() {
        description_ = getDefaultInstance().getDescription();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。广告描述。信息流必填
       * </pre>
       *
       * <code>string description = 5;</code>
       * @param value The bytes for description to set.
       * @return This builder for chaining.
       */
      public Builder setDescriptionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        description_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object appName_ = "";
      /**
       * <pre>
       * 可选。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_name = 6;</code>
       * @return The appName.
       */
      public java.lang.String getAppName() {
        java.lang.Object ref = appName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_name = 6;</code>
       * @return The bytes for appName.
       */
      public com.google.protobuf.ByteString
          getAppNameBytes() {
        java.lang.Object ref = appName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_name = 6;</code>
       * @param value The appName to set.
       * @return This builder for chaining.
       */
      public Builder setAppName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appName_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_name = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppName() {
        appName_ = getDefaultInstance().getAppName();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_name = 6;</code>
       * @param value The bytes for appName to set.
       * @return This builder for chaining.
       */
      public Builder setAppNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        appName_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private java.lang.Object packageName_ = "";
      /**
       * <pre>
       * 下载类广告（包括deeplink）。IOS填写bundleID，Android填写包名
       * </pre>
       *
       * <code>string package_name = 7;</code>
       * @return The packageName.
       */
      public java.lang.String getPackageName() {
        java.lang.Object ref = packageName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          packageName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 下载类广告（包括deeplink）。IOS填写bundleID，Android填写包名
       * </pre>
       *
       * <code>string package_name = 7;</code>
       * @return The bytes for packageName.
       */
      public com.google.protobuf.ByteString
          getPackageNameBytes() {
        java.lang.Object ref = packageName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          packageName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 下载类广告（包括deeplink）。IOS填写bundleID，Android填写包名
       * </pre>
       *
       * <code>string package_name = 7;</code>
       * @param value The packageName to set.
       * @return This builder for chaining.
       */
      public Builder setPackageName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        packageName_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 下载类广告（包括deeplink）。IOS填写bundleID，Android填写包名
       * </pre>
       *
       * <code>string package_name = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearPackageName() {
        packageName_ = getDefaultInstance().getPackageName();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 下载类广告（包括deeplink）。IOS填写bundleID，Android填写包名
       * </pre>
       *
       * <code>string package_name = 7;</code>
       * @param value The bytes for packageName to set.
       * @return This builder for chaining.
       */
      public Builder setPackageNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        packageName_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private java.lang.Object icon_ = "";
      /**
       * <pre>
       * 广告创意的图标URL
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return The icon.
       */
      public java.lang.String getIcon() {
        java.lang.Object ref = icon_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          icon_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 广告创意的图标URL
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return The bytes for icon.
       */
      public com.google.protobuf.ByteString
          getIconBytes() {
        java.lang.Object ref = icon_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          icon_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 广告创意的图标URL
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @param value The icon to set.
       * @return This builder for chaining.
       */
      public Builder setIcon(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        icon_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告创意的图标URL
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearIcon() {
        icon_ = getDefaultInstance().getIcon();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告创意的图标URL
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @param value The bytes for icon to set.
       * @return This builder for chaining.
       */
      public Builder setIconBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        icon_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image> image_ =
        java.util.Collections.emptyList();
      private void ensureImageIsMutable() {
        if (!((bitField0_ & 0x00000100) != 0)) {
          image_ = new java.util.ArrayList<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image>(image_);
          bitField0_ |= 0x00000100;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.ImageOrBuilder> imageBuilder_;

      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image> getImageList() {
        if (imageBuilder_ == null) {
          return java.util.Collections.unmodifiableList(image_);
        } else {
          return imageBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public int getImageCount() {
        if (imageBuilder_ == null) {
          return image_.size();
        } else {
          return imageBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image getImage(int index) {
        if (imageBuilder_ == null) {
          return image_.get(index);
        } else {
          return imageBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public Builder setImage(
          int index, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image value) {
        if (imageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureImageIsMutable();
          image_.set(index, value);
          onChanged();
        } else {
          imageBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public Builder setImage(
          int index, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder builderForValue) {
        if (imageBuilder_ == null) {
          ensureImageIsMutable();
          image_.set(index, builderForValue.build());
          onChanged();
        } else {
          imageBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public Builder addImage(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image value) {
        if (imageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureImageIsMutable();
          image_.add(value);
          onChanged();
        } else {
          imageBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public Builder addImage(
          int index, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image value) {
        if (imageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureImageIsMutable();
          image_.add(index, value);
          onChanged();
        } else {
          imageBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public Builder addImage(
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder builderForValue) {
        if (imageBuilder_ == null) {
          ensureImageIsMutable();
          image_.add(builderForValue.build());
          onChanged();
        } else {
          imageBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public Builder addImage(
          int index, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder builderForValue) {
        if (imageBuilder_ == null) {
          ensureImageIsMutable();
          image_.add(index, builderForValue.build());
          onChanged();
        } else {
          imageBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public Builder addAllImage(
          java.lang.Iterable<? extends cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image> values) {
        if (imageBuilder_ == null) {
          ensureImageIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, image_);
          onChanged();
        } else {
          imageBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public Builder clearImage() {
        if (imageBuilder_ == null) {
          image_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000100);
          onChanged();
        } else {
          imageBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public Builder removeImage(int index) {
        if (imageBuilder_ == null) {
          ensureImageIsMutable();
          image_.remove(index);
          onChanged();
        } else {
          imageBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder getImageBuilder(
          int index) {
        return getImageFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.ImageOrBuilder getImageOrBuilder(
          int index) {
        if (imageBuilder_ == null) {
          return image_.get(index);  } else {
          return imageBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public java.util.List<? extends cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.ImageOrBuilder> 
           getImageOrBuilderList() {
        if (imageBuilder_ != null) {
          return imageBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(image_);
        }
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder addImageBuilder() {
        return getImageFieldBuilder().addBuilder(
            cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.getDefaultInstance());
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder addImageBuilder(
          int index) {
        return getImageFieldBuilder().addBuilder(
            index, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.getDefaultInstance());
      }
      /**
       * <pre>
       * 可选。创意类型为单图，只有一张图片；为多图时有多张图片。
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Image image = 9;</code>
       */
      public java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder> 
           getImageBuilderList() {
        return getImageFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.ImageOrBuilder> 
          getImageFieldBuilder() {
        if (imageBuilder_ == null) {
          imageBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Image.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.ImageOrBuilder>(
                  image_,
                  ((bitField0_ & 0x00000100) != 0),
                  getParentForChildren(),
                  isClean());
          image_ = null;
        }
        return imageBuilder_;
      }

      private cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video video_;
      private com.google.protobuf.SingleFieldBuilder<
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.VideoOrBuilder> videoBuilder_;
      /**
       * <pre>
       * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
       * </pre>
       *
       * <code>.Ad.MaterialMeta.Video video = 10;</code>
       * @return Whether the video field is set.
       */
      public boolean hasVideo() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
       * </pre>
       *
       * <code>.Ad.MaterialMeta.Video video = 10;</code>
       * @return The video.
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video getVideo() {
        if (videoBuilder_ == null) {
          return video_ == null ? cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.getDefaultInstance() : video_;
        } else {
          return videoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
       * </pre>
       *
       * <code>.Ad.MaterialMeta.Video video = 10;</code>
       */
      public Builder setVideo(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video value) {
        if (videoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          video_ = value;
        } else {
          videoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
       * </pre>
       *
       * <code>.Ad.MaterialMeta.Video video = 10;</code>
       */
      public Builder setVideo(
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.Builder builderForValue) {
        if (videoBuilder_ == null) {
          video_ = builderForValue.build();
        } else {
          videoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
       * </pre>
       *
       * <code>.Ad.MaterialMeta.Video video = 10;</code>
       */
      public Builder mergeVideo(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video value) {
        if (videoBuilder_ == null) {
          if (((bitField0_ & 0x00000200) != 0) &&
            video_ != null &&
            video_ != cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.getDefaultInstance()) {
            getVideoBuilder().mergeFrom(value);
          } else {
            video_ = value;
          }
        } else {
          videoBuilder_.mergeFrom(value);
        }
        if (video_ != null) {
          bitField0_ |= 0x00000200;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
       * </pre>
       *
       * <code>.Ad.MaterialMeta.Video video = 10;</code>
       */
      public Builder clearVideo() {
        bitField0_ = (bitField0_ & ~0x00000200);
        video_ = null;
        if (videoBuilder_ != null) {
          videoBuilder_.dispose();
          videoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
       * </pre>
       *
       * <code>.Ad.MaterialMeta.Video video = 10;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.Builder getVideoBuilder() {
        bitField0_ |= 0x00000200;
        onChanged();
        return getVideoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
       * </pre>
       *
       * <code>.Ad.MaterialMeta.Video video = 10;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.VideoOrBuilder getVideoOrBuilder() {
        if (videoBuilder_ != null) {
          return videoBuilder_.getMessageOrBuilder();
        } else {
          return video_ == null ?
              cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.getDefaultInstance() : video_;
        }
      }
      /**
       * <pre>
       * creative_type = 3 （视频）时必填，同时可以选择是否需要上报视频相关的事件
       * </pre>
       *
       * <code>.Ad.MaterialMeta.Video video = 10;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.VideoOrBuilder> 
          getVideoFieldBuilder() {
        if (videoBuilder_ == null) {
          videoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Video.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.VideoOrBuilder>(
                  getVideo(),
                  getParentForChildren(),
                  isClean());
          video_ = null;
        }
        return videoBuilder_;
      }

      private java.lang.Object targetUrl_ = "";
      /**
       * <pre>
       * 可选。点击创意的响应地址url。
       * </pre>
       *
       * <code>string target_url = 12;</code>
       * @return The targetUrl.
       */
      public java.lang.String getTargetUrl() {
        java.lang.Object ref = targetUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          targetUrl_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。点击创意的响应地址url。
       * </pre>
       *
       * <code>string target_url = 12;</code>
       * @return The bytes for targetUrl.
       */
      public com.google.protobuf.ByteString
          getTargetUrlBytes() {
        java.lang.Object ref = targetUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          targetUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。点击创意的响应地址url。
       * </pre>
       *
       * <code>string target_url = 12;</code>
       * @param value The targetUrl to set.
       * @return This builder for chaining.
       */
      public Builder setTargetUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        targetUrl_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。点击创意的响应地址url。
       * </pre>
       *
       * <code>string target_url = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetUrl() {
        targetUrl_ = getDefaultInstance().getTargetUrl();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。点击创意的响应地址url。
       * </pre>
       *
       * <code>string target_url = 12;</code>
       * @param value The bytes for targetUrl to set.
       * @return This builder for chaining.
       */
      public Builder setTargetUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        targetUrl_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private java.lang.Object downloadUrl_ = "";
      /**
       * <pre>
       * 应用直接下载url，只Android流量。
       * </pre>
       *
       * <code>string download_url = 13;</code>
       * @return The downloadUrl.
       */
      public java.lang.String getDownloadUrl() {
        java.lang.Object ref = downloadUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          downloadUrl_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 应用直接下载url，只Android流量。
       * </pre>
       *
       * <code>string download_url = 13;</code>
       * @return The bytes for downloadUrl.
       */
      public com.google.protobuf.ByteString
          getDownloadUrlBytes() {
        java.lang.Object ref = downloadUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          downloadUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 应用直接下载url，只Android流量。
       * </pre>
       *
       * <code>string download_url = 13;</code>
       * @param value The downloadUrl to set.
       * @return This builder for chaining.
       */
      public Builder setDownloadUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        downloadUrl_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用直接下载url，只Android流量。
       * </pre>
       *
       * <code>string download_url = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownloadUrl() {
        downloadUrl_ = getDefaultInstance().getDownloadUrl();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用直接下载url，只Android流量。
       * </pre>
       *
       * <code>string download_url = 13;</code>
       * @param value The bytes for downloadUrl to set.
       * @return This builder for chaining.
       */
      public Builder setDownloadUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        downloadUrl_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }

      private java.lang.Object deeplinkUrl_ = "";
      /**
       * <pre>
       * 应用吊起的直达链接。如果该url非空，优先使用该直达链接
       * </pre>
       *
       * <code>string deeplink_url = 14;</code>
       * @return The deeplinkUrl.
       */
      public java.lang.String getDeeplinkUrl() {
        java.lang.Object ref = deeplinkUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          deeplinkUrl_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 应用吊起的直达链接。如果该url非空，优先使用该直达链接
       * </pre>
       *
       * <code>string deeplink_url = 14;</code>
       * @return The bytes for deeplinkUrl.
       */
      public com.google.protobuf.ByteString
          getDeeplinkUrlBytes() {
        java.lang.Object ref = deeplinkUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          deeplinkUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 应用吊起的直达链接。如果该url非空，优先使用该直达链接
       * </pre>
       *
       * <code>string deeplink_url = 14;</code>
       * @param value The deeplinkUrl to set.
       * @return This builder for chaining.
       */
      public Builder setDeeplinkUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        deeplinkUrl_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用吊起的直达链接。如果该url非空，优先使用该直达链接
       * </pre>
       *
       * <code>string deeplink_url = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeeplinkUrl() {
        deeplinkUrl_ = getDefaultInstance().getDeeplinkUrl();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用吊起的直达链接。如果该url非空，优先使用该直达链接
       * </pre>
       *
       * <code>string deeplink_url = 14;</code>
       * @param value The bytes for deeplinkUrl to set.
       * @return This builder for chaining.
       */
      public Builder setDeeplinkUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        deeplinkUrl_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }

      private java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking> trackingEvent_ =
        java.util.Collections.emptyList();
      private void ensureTrackingEventIsMutable() {
        if (!((bitField0_ & 0x00002000) != 0)) {
          trackingEvent_ = new java.util.ArrayList<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking>(trackingEvent_);
          bitField0_ |= 0x00002000;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.TrackingOrBuilder> trackingEventBuilder_;

      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking> getTrackingEventList() {
        if (trackingEventBuilder_ == null) {
          return java.util.Collections.unmodifiableList(trackingEvent_);
        } else {
          return trackingEventBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public int getTrackingEventCount() {
        if (trackingEventBuilder_ == null) {
          return trackingEvent_.size();
        } else {
          return trackingEventBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking getTrackingEvent(int index) {
        if (trackingEventBuilder_ == null) {
          return trackingEvent_.get(index);
        } else {
          return trackingEventBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public Builder setTrackingEvent(
          int index, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking value) {
        if (trackingEventBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTrackingEventIsMutable();
          trackingEvent_.set(index, value);
          onChanged();
        } else {
          trackingEventBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public Builder setTrackingEvent(
          int index, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder builderForValue) {
        if (trackingEventBuilder_ == null) {
          ensureTrackingEventIsMutable();
          trackingEvent_.set(index, builderForValue.build());
          onChanged();
        } else {
          trackingEventBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public Builder addTrackingEvent(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking value) {
        if (trackingEventBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTrackingEventIsMutable();
          trackingEvent_.add(value);
          onChanged();
        } else {
          trackingEventBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public Builder addTrackingEvent(
          int index, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking value) {
        if (trackingEventBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTrackingEventIsMutable();
          trackingEvent_.add(index, value);
          onChanged();
        } else {
          trackingEventBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public Builder addTrackingEvent(
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder builderForValue) {
        if (trackingEventBuilder_ == null) {
          ensureTrackingEventIsMutable();
          trackingEvent_.add(builderForValue.build());
          onChanged();
        } else {
          trackingEventBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public Builder addTrackingEvent(
          int index, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder builderForValue) {
        if (trackingEventBuilder_ == null) {
          ensureTrackingEventIsMutable();
          trackingEvent_.add(index, builderForValue.build());
          onChanged();
        } else {
          trackingEventBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public Builder addAllTrackingEvent(
          java.lang.Iterable<? extends cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking> values) {
        if (trackingEventBuilder_ == null) {
          ensureTrackingEventIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, trackingEvent_);
          onChanged();
        } else {
          trackingEventBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public Builder clearTrackingEvent() {
        if (trackingEventBuilder_ == null) {
          trackingEvent_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00002000);
          onChanged();
        } else {
          trackingEventBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public Builder removeTrackingEvent(int index) {
        if (trackingEventBuilder_ == null) {
          ensureTrackingEventIsMutable();
          trackingEvent_.remove(index);
          onChanged();
        } else {
          trackingEventBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder getTrackingEventBuilder(
          int index) {
        return getTrackingEventFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.TrackingOrBuilder getTrackingEventOrBuilder(
          int index) {
        if (trackingEventBuilder_ == null) {
          return trackingEvent_.get(index);  } else {
          return trackingEventBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public java.util.List<? extends cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.TrackingOrBuilder> 
           getTrackingEventOrBuilderList() {
        if (trackingEventBuilder_ != null) {
          return trackingEventBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(trackingEvent_);
        }
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder addTrackingEventBuilder() {
        return getTrackingEventFieldBuilder().addBuilder(
            cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.getDefaultInstance());
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder addTrackingEventBuilder(
          int index) {
        return getTrackingEventFieldBuilder().addBuilder(
            index, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.getDefaultInstance());
      }
      /**
       * <pre>
       * 用于各种打点的url
       * </pre>
       *
       * <code>repeated .Ad.MaterialMeta.Tracking tracking_event = 15;</code>
       */
      public java.util.List<cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder> 
           getTrackingEventBuilderList() {
        return getTrackingEventFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.TrackingOrBuilder> 
          getTrackingEventFieldBuilder() {
        if (trackingEventBuilder_ == null) {
          trackingEventBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Tracking.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.TrackingOrBuilder>(
                  trackingEvent_,
                  ((bitField0_ & 0x00002000) != 0),
                  getParentForChildren(),
                  isClean());
          trackingEvent_ = null;
        }
        return trackingEventBuilder_;
      }

      private com.google.protobuf.LazyStringArrayList winNoticeUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureWinNoticeUrlIsMutable() {
        if (!winNoticeUrl_.isModifiable()) {
          winNoticeUrl_ = new com.google.protobuf.LazyStringArrayList(winNoticeUrl_);
        }
        bitField0_ |= 0x00004000;
      }
      /**
       * <pre>
       * 竞价模式时， win notice填写在这里
       * </pre>
       *
       * <code>repeated string win_notice_url = 16;</code>
       * @return A list containing the winNoticeUrl.
       */
      public com.google.protobuf.ProtocolStringList
          getWinNoticeUrlList() {
        winNoticeUrl_.makeImmutable();
        return winNoticeUrl_;
      }
      /**
       * <pre>
       * 竞价模式时， win notice填写在这里
       * </pre>
       *
       * <code>repeated string win_notice_url = 16;</code>
       * @return The count of winNoticeUrl.
       */
      public int getWinNoticeUrlCount() {
        return winNoticeUrl_.size();
      }
      /**
       * <pre>
       * 竞价模式时， win notice填写在这里
       * </pre>
       *
       * <code>repeated string win_notice_url = 16;</code>
       * @param index The index of the element to return.
       * @return The winNoticeUrl at the given index.
       */
      public java.lang.String getWinNoticeUrl(int index) {
        return winNoticeUrl_.get(index);
      }
      /**
       * <pre>
       * 竞价模式时， win notice填写在这里
       * </pre>
       *
       * <code>repeated string win_notice_url = 16;</code>
       * @param index The index of the value to return.
       * @return The bytes of the winNoticeUrl at the given index.
       */
      public com.google.protobuf.ByteString
          getWinNoticeUrlBytes(int index) {
        return winNoticeUrl_.getByteString(index);
      }
      /**
       * <pre>
       * 竞价模式时， win notice填写在这里
       * </pre>
       *
       * <code>repeated string win_notice_url = 16;</code>
       * @param index The index to set the value at.
       * @param value The winNoticeUrl to set.
       * @return This builder for chaining.
       */
      public Builder setWinNoticeUrl(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureWinNoticeUrlIsMutable();
        winNoticeUrl_.set(index, value);
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价模式时， win notice填写在这里
       * </pre>
       *
       * <code>repeated string win_notice_url = 16;</code>
       * @param value The winNoticeUrl to add.
       * @return This builder for chaining.
       */
      public Builder addWinNoticeUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureWinNoticeUrlIsMutable();
        winNoticeUrl_.add(value);
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价模式时， win notice填写在这里
       * </pre>
       *
       * <code>repeated string win_notice_url = 16;</code>
       * @param values The winNoticeUrl to add.
       * @return This builder for chaining.
       */
      public Builder addAllWinNoticeUrl(
          java.lang.Iterable<java.lang.String> values) {
        ensureWinNoticeUrlIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, winNoticeUrl_);
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价模式时， win notice填写在这里
       * </pre>
       *
       * <code>repeated string win_notice_url = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearWinNoticeUrl() {
        winNoticeUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00004000);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价模式时， win notice填写在这里
       * </pre>
       *
       * <code>repeated string win_notice_url = 16;</code>
       * @param value The bytes of the winNoticeUrl to add.
       * @return This builder for chaining.
       */
      public Builder addWinNoticeUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ensureWinNoticeUrlIsMutable();
        winNoticeUrl_.add(value);
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }

      private java.lang.Object dealId_ = "";
      /**
       * <pre>
       * 在PD合作模式下返回
       * </pre>
       *
       * <code>string deal_id = 17;</code>
       * @return The dealId.
       */
      public java.lang.String getDealId() {
        java.lang.Object ref = dealId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          dealId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 在PD合作模式下返回
       * </pre>
       *
       * <code>string deal_id = 17;</code>
       * @return The bytes for dealId.
       */
      public com.google.protobuf.ByteString
          getDealIdBytes() {
        java.lang.Object ref = dealId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          dealId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 在PD合作模式下返回
       * </pre>
       *
       * <code>string deal_id = 17;</code>
       * @param value The dealId to set.
       * @return This builder for chaining.
       */
      public Builder setDealId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        dealId_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 在PD合作模式下返回
       * </pre>
       *
       * <code>string deal_id = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearDealId() {
        dealId_ = getDefaultInstance().getDealId();
        bitField0_ = (bitField0_ & ~0x00008000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 在PD合作模式下返回
       * </pre>
       *
       * <code>string deal_id = 17;</code>
       * @param value The bytes for dealId to set.
       * @return This builder for chaining.
       */
      public Builder setDealIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        dealId_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }

      private cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo mp_;
      private com.google.protobuf.SingleFieldBuilder<
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfoOrBuilder> mpBuilder_;
      /**
       * <pre>
       * 小程序对象
       * </pre>
       *
       * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
       * @return Whether the mp field is set.
       */
      public boolean hasMp() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * 小程序对象
       * </pre>
       *
       * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
       * @return The mp.
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo getMp() {
        if (mpBuilder_ == null) {
          return mp_ == null ? cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.getDefaultInstance() : mp_;
        } else {
          return mpBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 小程序对象
       * </pre>
       *
       * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
       */
      public Builder setMp(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo value) {
        if (mpBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          mp_ = value;
        } else {
          mpBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 小程序对象
       * </pre>
       *
       * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
       */
      public Builder setMp(
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.Builder builderForValue) {
        if (mpBuilder_ == null) {
          mp_ = builderForValue.build();
        } else {
          mpBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 小程序对象
       * </pre>
       *
       * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
       */
      public Builder mergeMp(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo value) {
        if (mpBuilder_ == null) {
          if (((bitField0_ & 0x00010000) != 0) &&
            mp_ != null &&
            mp_ != cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.getDefaultInstance()) {
            getMpBuilder().mergeFrom(value);
          } else {
            mp_ = value;
          }
        } else {
          mpBuilder_.mergeFrom(value);
        }
        if (mp_ != null) {
          bitField0_ |= 0x00010000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 小程序对象
       * </pre>
       *
       * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
       */
      public Builder clearMp() {
        bitField0_ = (bitField0_ & ~0x00010000);
        mp_ = null;
        if (mpBuilder_ != null) {
          mpBuilder_.dispose();
          mpBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 小程序对象
       * </pre>
       *
       * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.Builder getMpBuilder() {
        bitField0_ |= 0x00010000;
        onChanged();
        return getMpFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 小程序对象
       * </pre>
       *
       * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
       */
      public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfoOrBuilder getMpOrBuilder() {
        if (mpBuilder_ != null) {
          return mpBuilder_.getMessageOrBuilder();
        } else {
          return mp_ == null ?
              cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.getDefaultInstance() : mp_;
        }
      }
      /**
       * <pre>
       * 小程序对象
       * </pre>
       *
       * <code>.Ad.MaterialMeta.WxMpInfo mp = 19;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfoOrBuilder> 
          getMpFieldBuilder() {
        if (mpBuilder_ == null) {
          mpBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfo.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.WxMpInfoOrBuilder>(
                  getMp(),
                  getParentForChildren(),
                  isClean());
          mp_ = null;
        }
        return mpBuilder_;
      }

      private java.lang.Object source_ = "";
      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>string source = 20;</code>
       * @return The source.
       */
      public java.lang.String getSource() {
        java.lang.Object ref = source_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          source_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>string source = 20;</code>
       * @return The bytes for source.
       */
      public com.google.protobuf.ByteString
          getSourceBytes() {
        java.lang.Object ref = source_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          source_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>string source = 20;</code>
       * @param value The source to set.
       * @return This builder for chaining.
       */
      public Builder setSource(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        source_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>string source = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearSource() {
        source_ = getDefaultInstance().getSource();
        bitField0_ = (bitField0_ & ~0x00020000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告来源
       * </pre>
       *
       * <code>string source = 20;</code>
       * @param value The bytes for source to set.
       * @return This builder for chaining.
       */
      public Builder setSourceBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        source_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }

      private java.lang.Object universalLink_ = "";
      /**
       * <pre>
       * ios端调起连接 universal_link优先级高于deeplink
       * </pre>
       *
       * <code>string universal_link = 21;</code>
       * @return The universalLink.
       */
      public java.lang.String getUniversalLink() {
        java.lang.Object ref = universalLink_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          universalLink_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * ios端调起连接 universal_link优先级高于deeplink
       * </pre>
       *
       * <code>string universal_link = 21;</code>
       * @return The bytes for universalLink.
       */
      public com.google.protobuf.ByteString
          getUniversalLinkBytes() {
        java.lang.Object ref = universalLink_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          universalLink_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * ios端调起连接 universal_link优先级高于deeplink
       * </pre>
       *
       * <code>string universal_link = 21;</code>
       * @param value The universalLink to set.
       * @return This builder for chaining.
       */
      public Builder setUniversalLink(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        universalLink_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * ios端调起连接 universal_link优先级高于deeplink
       * </pre>
       *
       * <code>string universal_link = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearUniversalLink() {
        universalLink_ = getDefaultInstance().getUniversalLink();
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * ios端调起连接 universal_link优先级高于deeplink
       * </pre>
       *
       * <code>string universal_link = 21;</code>
       * @param value The bytes for universalLink to set.
       * @return This builder for chaining.
       */
      public Builder setUniversalLinkBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        universalLink_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }

      private java.lang.Object appChannel_ = "";
      /**
       * <pre>
       * DSP在应用商店推广应用的时候，应用商店分配给DSP的渠道号
       * </pre>
       *
       * <code>string app_channel = 22;</code>
       * @return The appChannel.
       */
      public java.lang.String getAppChannel() {
        java.lang.Object ref = appChannel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appChannel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * DSP在应用商店推广应用的时候，应用商店分配给DSP的渠道号
       * </pre>
       *
       * <code>string app_channel = 22;</code>
       * @return The bytes for appChannel.
       */
      public com.google.protobuf.ByteString
          getAppChannelBytes() {
        java.lang.Object ref = appChannel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appChannel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * DSP在应用商店推广应用的时候，应用商店分配给DSP的渠道号
       * </pre>
       *
       * <code>string app_channel = 22;</code>
       * @param value The appChannel to set.
       * @return This builder for chaining.
       */
      public Builder setAppChannel(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appChannel_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DSP在应用商店推广应用的时候，应用商店分配给DSP的渠道号
       * </pre>
       *
       * <code>string app_channel = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppChannel() {
        appChannel_ = getDefaultInstance().getAppChannel();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DSP在应用商店推广应用的时候，应用商店分配给DSP的渠道号
       * </pre>
       *
       * <code>string app_channel = 22;</code>
       * @param value The bytes for appChannel to set.
       * @return This builder for chaining.
       */
      public Builder setAppChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        appChannel_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }

      private java.lang.Object appDesc_ = "";
      /**
       * <pre>
       * 可选。应用介绍。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_desc = 23;</code>
       * @return The appDesc.
       */
      public java.lang.String getAppDesc() {
        java.lang.Object ref = appDesc_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appDesc_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。应用介绍。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_desc = 23;</code>
       * @return The bytes for appDesc.
       */
      public com.google.protobuf.ByteString
          getAppDescBytes() {
        java.lang.Object ref = appDesc_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appDesc_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。应用介绍。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_desc = 23;</code>
       * @param value The appDesc to set.
       * @return This builder for chaining.
       */
      public Builder setAppDesc(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appDesc_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。应用介绍。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_desc = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppDesc() {
        appDesc_ = getDefaultInstance().getAppDesc();
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。应用介绍。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_desc = 23;</code>
       * @param value The bytes for appDesc to set.
       * @return This builder for chaining.
       */
      public Builder setAppDescBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        appDesc_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }

      private float appPackageSize_ ;
      /**
       * <pre>
       * 可选。应用大小，单位MB。针对应用下载类广告。
       * </pre>
       *
       * <code>float app_package_size = 24;</code>
       * @return The appPackageSize.
       */
      @java.lang.Override
      public float getAppPackageSize() {
        return appPackageSize_;
      }
      /**
       * <pre>
       * 可选。应用大小，单位MB。针对应用下载类广告。
       * </pre>
       *
       * <code>float app_package_size = 24;</code>
       * @param value The appPackageSize to set.
       * @return This builder for chaining.
       */
      public Builder setAppPackageSize(float value) {

        appPackageSize_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。应用大小，单位MB。针对应用下载类广告。
       * </pre>
       *
       * <code>float app_package_size = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppPackageSize() {
        bitField0_ = (bitField0_ & ~0x00200000);
        appPackageSize_ = 0F;
        onChanged();
        return this;
      }

      private java.lang.Object appPublisher_ = "";
      /**
       * <pre>
       * 可选。APP开发者主体名称。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_publisher = 25;</code>
       * @return The appPublisher.
       */
      public java.lang.String getAppPublisher() {
        java.lang.Object ref = appPublisher_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appPublisher_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。APP开发者主体名称。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_publisher = 25;</code>
       * @return The bytes for appPublisher.
       */
      public com.google.protobuf.ByteString
          getAppPublisherBytes() {
        java.lang.Object ref = appPublisher_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appPublisher_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。APP开发者主体名称。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_publisher = 25;</code>
       * @param value The appPublisher to set.
       * @return This builder for chaining.
       */
      public Builder setAppPublisher(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appPublisher_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。APP开发者主体名称。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_publisher = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppPublisher() {
        appPublisher_ = getDefaultInstance().getAppPublisher();
        bitField0_ = (bitField0_ & ~0x00400000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。APP开发者主体名称。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_publisher = 25;</code>
       * @param value The bytes for appPublisher to set.
       * @return This builder for chaining.
       */
      public Builder setAppPublisherBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        appPublisher_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }

      private java.lang.Object downAppVersion_ = "";
      /**
       * <pre>
       * 可选。下载APP版本号。针对应用下载类广告。
       * </pre>
       *
       * <code>string down_app_version = 26;</code>
       * @return The downAppVersion.
       */
      public java.lang.String getDownAppVersion() {
        java.lang.Object ref = downAppVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          downAppVersion_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。下载APP版本号。针对应用下载类广告。
       * </pre>
       *
       * <code>string down_app_version = 26;</code>
       * @return The bytes for downAppVersion.
       */
      public com.google.protobuf.ByteString
          getDownAppVersionBytes() {
        java.lang.Object ref = downAppVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          downAppVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。下载APP版本号。针对应用下载类广告。
       * </pre>
       *
       * <code>string down_app_version = 26;</code>
       * @param value The downAppVersion to set.
       * @return This builder for chaining.
       */
      public Builder setDownAppVersion(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        downAppVersion_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。下载APP版本号。针对应用下载类广告。
       * </pre>
       *
       * <code>string down_app_version = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownAppVersion() {
        downAppVersion_ = getDefaultInstance().getDownAppVersion();
        bitField0_ = (bitField0_ & ~0x00800000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。下载APP版本号。针对应用下载类广告。
       * </pre>
       *
       * <code>string down_app_version = 26;</code>
       * @param value The bytes for downAppVersion to set.
       * @return This builder for chaining.
       */
      public Builder setDownAppVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        downAppVersion_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }

      private java.lang.Object privacyLink_ = "";
      /**
       * <pre>
       * 可选。隐私协议URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string privacy_link = 27;</code>
       * @return The privacyLink.
       */
      public java.lang.String getPrivacyLink() {
        java.lang.Object ref = privacyLink_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          privacyLink_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。隐私协议URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string privacy_link = 27;</code>
       * @return The bytes for privacyLink.
       */
      public com.google.protobuf.ByteString
          getPrivacyLinkBytes() {
        java.lang.Object ref = privacyLink_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          privacyLink_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。隐私协议URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string privacy_link = 27;</code>
       * @param value The privacyLink to set.
       * @return This builder for chaining.
       */
      public Builder setPrivacyLink(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        privacyLink_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。隐私协议URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string privacy_link = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearPrivacyLink() {
        privacyLink_ = getDefaultInstance().getPrivacyLink();
        bitField0_ = (bitField0_ & ~0x01000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。隐私协议URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string privacy_link = 27;</code>
       * @param value The bytes for privacyLink to set.
       * @return This builder for chaining.
       */
      public Builder setPrivacyLinkBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        privacyLink_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }

      private java.lang.Object permissionLink_ = "";
      /**
       * <pre>
       * 可选。用户权限URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string permission_link = 28;</code>
       * @return The permissionLink.
       */
      public java.lang.String getPermissionLink() {
        java.lang.Object ref = permissionLink_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          permissionLink_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。用户权限URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string permission_link = 28;</code>
       * @return The bytes for permissionLink.
       */
      public com.google.protobuf.ByteString
          getPermissionLinkBytes() {
        java.lang.Object ref = permissionLink_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          permissionLink_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。用户权限URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string permission_link = 28;</code>
       * @param value The permissionLink to set.
       * @return This builder for chaining.
       */
      public Builder setPermissionLink(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        permissionLink_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。用户权限URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string permission_link = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearPermissionLink() {
        permissionLink_ = getDefaultInstance().getPermissionLink();
        bitField0_ = (bitField0_ & ~0x02000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。用户权限URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string permission_link = 28;</code>
       * @param value The bytes for permissionLink to set.
       * @return This builder for chaining.
       */
      public Builder setPermissionLinkBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        permissionLink_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }

      private java.lang.Object introduceLink_ = "";
      /**
       * <pre>
       * 可选。应用介绍URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string introduce_link = 29;</code>
       * @return The introduceLink.
       */
      public java.lang.String getIntroduceLink() {
        java.lang.Object ref = introduceLink_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          introduceLink_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。应用介绍URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string introduce_link = 29;</code>
       * @return The bytes for introduceLink.
       */
      public com.google.protobuf.ByteString
          getIntroduceLinkBytes() {
        java.lang.Object ref = introduceLink_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          introduceLink_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。应用介绍URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string introduce_link = 29;</code>
       * @param value The introduceLink to set.
       * @return This builder for chaining.
       */
      public Builder setIntroduceLink(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        introduceLink_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。应用介绍URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string introduce_link = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearIntroduceLink() {
        introduceLink_ = getDefaultInstance().getIntroduceLink();
        bitField0_ = (bitField0_ & ~0x04000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。应用介绍URL。针对应用下载类广告。
       * </pre>
       *
       * <code>string introduce_link = 29;</code>
       * @param value The bytes for introduceLink to set.
       * @return This builder for chaining.
       */
      public Builder setIntroduceLinkBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        introduceLink_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }

      private java.lang.Object appLcpNumber_ = "";
      /**
       * <pre>
       * 可选。下载应用备案号 LCP。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_lcp_number = 30;</code>
       * @return The appLcpNumber.
       */
      public java.lang.String getAppLcpNumber() {
        java.lang.Object ref = appLcpNumber_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appLcpNumber_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。下载应用备案号 LCP。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_lcp_number = 30;</code>
       * @return The bytes for appLcpNumber.
       */
      public com.google.protobuf.ByteString
          getAppLcpNumberBytes() {
        java.lang.Object ref = appLcpNumber_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appLcpNumber_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。下载应用备案号 LCP。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_lcp_number = 30;</code>
       * @param value The appLcpNumber to set.
       * @return This builder for chaining.
       */
      public Builder setAppLcpNumber(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appLcpNumber_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。下载应用备案号 LCP。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_lcp_number = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppLcpNumber() {
        appLcpNumber_ = getDefaultInstance().getAppLcpNumber();
        bitField0_ = (bitField0_ & ~0x08000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。下载应用备案号 LCP。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_lcp_number = 30;</code>
       * @param value The bytes for appLcpNumber to set.
       * @return This builder for chaining.
       */
      public Builder setAppLcpNumberBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        appLcpNumber_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }

      private java.lang.Object appSuitableAge_ = "";
      /**
       * <pre>
       * 可选。下载应用实用年龄。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_suitable_age = 31;</code>
       * @return The appSuitableAge.
       */
      public java.lang.String getAppSuitableAge() {
        java.lang.Object ref = appSuitableAge_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appSuitableAge_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可选。下载应用实用年龄。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_suitable_age = 31;</code>
       * @return The bytes for appSuitableAge.
       */
      public com.google.protobuf.ByteString
          getAppSuitableAgeBytes() {
        java.lang.Object ref = appSuitableAge_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appSuitableAge_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可选。下载应用实用年龄。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_suitable_age = 31;</code>
       * @param value The appSuitableAge to set.
       * @return This builder for chaining.
       */
      public Builder setAppSuitableAge(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appSuitableAge_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。下载应用实用年龄。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_suitable_age = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppSuitableAge() {
        appSuitableAge_ = getDefaultInstance().getAppSuitableAge();
        bitField0_ = (bitField0_ & ~0x10000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可选。下载应用实用年龄。针对应用下载类广告。
       * </pre>
       *
       * <code>string app_suitable_age = 31;</code>
       * @param value The bytes for appSuitableAge to set.
       * @return This builder for chaining.
       */
      public Builder setAppSuitableAgeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        appSuitableAge_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }

      private int replacementType_ ;
      /**
       * <pre>
       * 点击坐标替换类型 0：浮点型；1：整型；
       * </pre>
       *
       * <code>int32 replacement_type = 32;</code>
       * @return The replacementType.
       */
      @java.lang.Override
      public int getReplacementType() {
        return replacementType_;
      }
      /**
       * <pre>
       * 点击坐标替换类型 0：浮点型；1：整型；
       * </pre>
       *
       * <code>int32 replacement_type = 32;</code>
       * @param value The replacementType to set.
       * @return This builder for chaining.
       */
      public Builder setReplacementType(int value) {

        replacementType_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 点击坐标替换类型 0：浮点型；1：整型；
       * </pre>
       *
       * <code>int32 replacement_type = 32;</code>
       * @return This builder for chaining.
       */
      public Builder clearReplacementType() {
        bitField0_ = (bitField0_ & ~0x20000000);
        replacementType_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Ad.MaterialMeta)
    }

    // @@protoc_insertion_point(class_scope:Ad.MaterialMeta)
    private static final cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta();
    }

    public static cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MaterialMeta>
        PARSER = new com.google.protobuf.AbstractParser<MaterialMeta>() {
      @java.lang.Override
      public MaterialMeta parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MaterialMeta> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MaterialMeta> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private int bitField0_;
  public static final int AD_ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object adId_ = "";
  /**
   * <pre>
   * 必填。广告组ID
   * </pre>
   *
   * <code>string ad_id = 1;</code>
   * @return The adId.
   */
  @java.lang.Override
  public java.lang.String getAdId() {
    java.lang.Object ref = adId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      adId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 必填。广告组ID
   * </pre>
   *
   * <code>string ad_id = 1;</code>
   * @return The bytes for adId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAdIdBytes() {
    java.lang.Object ref = adId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      adId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CREATIVE_FIELD_NUMBER = 2;
  private cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta creative_;
  /**
   * <pre>
   * 必填。广告物料
   * </pre>
   *
   * <code>.Ad.MaterialMeta creative = 2;</code>
   * @return Whether the creative field is set.
   */
  @java.lang.Override
  public boolean hasCreative() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <pre>
   * 必填。广告物料
   * </pre>
   *
   * <code>.Ad.MaterialMeta creative = 2;</code>
   * @return The creative.
   */
  @java.lang.Override
  public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta getCreative() {
    return creative_ == null ? cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.getDefaultInstance() : creative_;
  }
  /**
   * <pre>
   * 必填。广告物料
   * </pre>
   *
   * <code>.Ad.MaterialMeta creative = 2;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMetaOrBuilder getCreativeOrBuilder() {
    return creative_ == null ? cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.getDefaultInstance() : creative_;
  }

  public static final int BID_PRICE_FIELD_NUMBER = 3;
  private long bidPrice_ = 0L;
  /**
   * <pre>
   * 竞价模式时必填。出价, cpm分
   * </pre>
   *
   * <code>uint64 bid_price = 3;</code>
   * @return The bidPrice.
   */
  @java.lang.Override
  public long getBidPrice() {
    return bidPrice_;
  }

  public static final int BID_TYPE_FIELD_NUMBER = 5;
  private int bidType_ = 0;
  /**
   * <pre>
   * 出价类型 默认:1,1=cpm
   * </pre>
   *
   * <code>int32 bid_type = 5;</code>
   * @return The bidType.
   */
  @java.lang.Override
  public int getBidType() {
    return bidType_;
  }

  public static final int SETTLE_TYPE_FIELD_NUMBER = 6;
  private int settleType_ = 0;
  /**
   * <pre>
   * 计费类型 默认:1,1=cpm
   * </pre>
   *
   * <code>int32 settle_type = 6;</code>
   * @return The settleType.
   */
  @java.lang.Override
  public int getSettleType() {
    return settleType_;
  }

  public static final int EXT_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object ext_ = "";
  /**
   * <pre>
   * 扩展字段
   * </pre>
   *
   * <code>string ext = 10;</code>
   * @return The ext.
   */
  @java.lang.Override
  public java.lang.String getExt() {
    java.lang.Object ref = ext_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ext_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 扩展字段
   * </pre>
   *
   * <code>string ext = 10;</code>
   * @return The bytes for ext.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExtBytes() {
    java.lang.Object ref = ext_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ext_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(adId_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, adId_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(2, getCreative());
    }
    if (bidPrice_ != 0L) {
      output.writeUInt64(3, bidPrice_);
    }
    if (bidType_ != 0) {
      output.writeInt32(5, bidType_);
    }
    if (settleType_ != 0) {
      output.writeInt32(6, settleType_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ext_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 10, ext_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(adId_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, adId_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getCreative());
    }
    if (bidPrice_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, bidPrice_);
    }
    if (bidType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, bidType_);
    }
    if (settleType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, settleType_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ext_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(10, ext_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.taken.ad.logic.media.ubix.dto.Ad)) {
      return super.equals(obj);
    }
    cn.taken.ad.logic.media.ubix.dto.Ad other = (cn.taken.ad.logic.media.ubix.dto.Ad) obj;

    if (!getAdId()
        .equals(other.getAdId())) return false;
    if (hasCreative() != other.hasCreative()) return false;
    if (hasCreative()) {
      if (!getCreative()
          .equals(other.getCreative())) return false;
    }
    if (getBidPrice()
        != other.getBidPrice()) return false;
    if (getBidType()
        != other.getBidType()) return false;
    if (getSettleType()
        != other.getSettleType()) return false;
    if (!getExt()
        .equals(other.getExt())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + AD_ID_FIELD_NUMBER;
    hash = (53 * hash) + getAdId().hashCode();
    if (hasCreative()) {
      hash = (37 * hash) + CREATIVE_FIELD_NUMBER;
      hash = (53 * hash) + getCreative().hashCode();
    }
    hash = (37 * hash) + BID_PRICE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getBidPrice());
    hash = (37 * hash) + BID_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getBidType();
    hash = (37 * hash) + SETTLE_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getSettleType();
    hash = (37 * hash) + EXT_FIELD_NUMBER;
    hash = (53 * hash) + getExt().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.taken.ad.logic.media.ubix.dto.Ad parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.media.ubix.dto.Ad parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.media.ubix.dto.Ad parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.media.ubix.dto.Ad parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.media.ubix.dto.Ad parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.media.ubix.dto.Ad parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.media.ubix.dto.Ad parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.media.ubix.dto.Ad parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static cn.taken.ad.logic.media.ubix.dto.Ad parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static cn.taken.ad.logic.media.ubix.dto.Ad parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.taken.ad.logic.media.ubix.dto.Ad parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.media.ubix.dto.Ad parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.taken.ad.logic.media.ubix.dto.Ad prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Ad}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Ad)
      cn.taken.ad.logic.media.ubix.dto.AdOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.media.ubix.dto.Ad.class, cn.taken.ad.logic.media.ubix.dto.Ad.Builder.class);
    }

    // Construct using cn.taken.ad.logic.media.ubix.dto.Ad.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getCreativeFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      adId_ = "";
      creative_ = null;
      if (creativeBuilder_ != null) {
        creativeBuilder_.dispose();
        creativeBuilder_ = null;
      }
      bidPrice_ = 0L;
      bidType_ = 0;
      settleType_ = 0;
      ext_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.taken.ad.logic.media.ubix.dto.UBiXMediaRequest.internal_static_Ad_descriptor;
    }

    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad getDefaultInstanceForType() {
      return cn.taken.ad.logic.media.ubix.dto.Ad.getDefaultInstance();
    }

    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad build() {
      cn.taken.ad.logic.media.ubix.dto.Ad result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.taken.ad.logic.media.ubix.dto.Ad buildPartial() {
      cn.taken.ad.logic.media.ubix.dto.Ad result = new cn.taken.ad.logic.media.ubix.dto.Ad(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(cn.taken.ad.logic.media.ubix.dto.Ad result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.adId_ = adId_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.creative_ = creativeBuilder_ == null
            ? creative_
            : creativeBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.bidPrice_ = bidPrice_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.bidType_ = bidType_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.settleType_ = settleType_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.ext_ = ext_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.taken.ad.logic.media.ubix.dto.Ad) {
        return mergeFrom((cn.taken.ad.logic.media.ubix.dto.Ad)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.taken.ad.logic.media.ubix.dto.Ad other) {
      if (other == cn.taken.ad.logic.media.ubix.dto.Ad.getDefaultInstance()) return this;
      if (!other.getAdId().isEmpty()) {
        adId_ = other.adId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasCreative()) {
        mergeCreative(other.getCreative());
      }
      if (other.getBidPrice() != 0L) {
        setBidPrice(other.getBidPrice());
      }
      if (other.getBidType() != 0) {
        setBidType(other.getBidType());
      }
      if (other.getSettleType() != 0) {
        setSettleType(other.getSettleType());
      }
      if (!other.getExt().isEmpty()) {
        ext_ = other.ext_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              adId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  getCreativeFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              bidPrice_ = input.readUInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 40: {
              bidType_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 40
            case 48: {
              settleType_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 48
            case 82: {
              ext_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 82
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object adId_ = "";
    /**
     * <pre>
     * 必填。广告组ID
     * </pre>
     *
     * <code>string ad_id = 1;</code>
     * @return The adId.
     */
    public java.lang.String getAdId() {
      java.lang.Object ref = adId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        adId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 必填。广告组ID
     * </pre>
     *
     * <code>string ad_id = 1;</code>
     * @return The bytes for adId.
     */
    public com.google.protobuf.ByteString
        getAdIdBytes() {
      java.lang.Object ref = adId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        adId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 必填。广告组ID
     * </pre>
     *
     * <code>string ad_id = 1;</code>
     * @param value The adId to set.
     * @return This builder for chaining.
     */
    public Builder setAdId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      adId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 必填。广告组ID
     * </pre>
     *
     * <code>string ad_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearAdId() {
      adId_ = getDefaultInstance().getAdId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 必填。广告组ID
     * </pre>
     *
     * <code>string ad_id = 1;</code>
     * @param value The bytes for adId to set.
     * @return This builder for chaining.
     */
    public Builder setAdIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      adId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta creative_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMetaOrBuilder> creativeBuilder_;
    /**
     * <pre>
     * 必填。广告物料
     * </pre>
     *
     * <code>.Ad.MaterialMeta creative = 2;</code>
     * @return Whether the creative field is set.
     */
    public boolean hasCreative() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 必填。广告物料
     * </pre>
     *
     * <code>.Ad.MaterialMeta creative = 2;</code>
     * @return The creative.
     */
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta getCreative() {
      if (creativeBuilder_ == null) {
        return creative_ == null ? cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.getDefaultInstance() : creative_;
      } else {
        return creativeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 必填。广告物料
     * </pre>
     *
     * <code>.Ad.MaterialMeta creative = 2;</code>
     */
    public Builder setCreative(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta value) {
      if (creativeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        creative_ = value;
      } else {
        creativeBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 必填。广告物料
     * </pre>
     *
     * <code>.Ad.MaterialMeta creative = 2;</code>
     */
    public Builder setCreative(
        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Builder builderForValue) {
      if (creativeBuilder_ == null) {
        creative_ = builderForValue.build();
      } else {
        creativeBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 必填。广告物料
     * </pre>
     *
     * <code>.Ad.MaterialMeta creative = 2;</code>
     */
    public Builder mergeCreative(cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta value) {
      if (creativeBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          creative_ != null &&
          creative_ != cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.getDefaultInstance()) {
          getCreativeBuilder().mergeFrom(value);
        } else {
          creative_ = value;
        }
      } else {
        creativeBuilder_.mergeFrom(value);
      }
      if (creative_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <pre>
     * 必填。广告物料
     * </pre>
     *
     * <code>.Ad.MaterialMeta creative = 2;</code>
     */
    public Builder clearCreative() {
      bitField0_ = (bitField0_ & ~0x00000002);
      creative_ = null;
      if (creativeBuilder_ != null) {
        creativeBuilder_.dispose();
        creativeBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 必填。广告物料
     * </pre>
     *
     * <code>.Ad.MaterialMeta creative = 2;</code>
     */
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Builder getCreativeBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return getCreativeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 必填。广告物料
     * </pre>
     *
     * <code>.Ad.MaterialMeta creative = 2;</code>
     */
    public cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMetaOrBuilder getCreativeOrBuilder() {
      if (creativeBuilder_ != null) {
        return creativeBuilder_.getMessageOrBuilder();
      } else {
        return creative_ == null ?
            cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.getDefaultInstance() : creative_;
      }
    }
    /**
     * <pre>
     * 必填。广告物料
     * </pre>
     *
     * <code>.Ad.MaterialMeta creative = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMetaOrBuilder> 
        getCreativeFieldBuilder() {
      if (creativeBuilder_ == null) {
        creativeBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMeta.Builder, cn.taken.ad.logic.media.ubix.dto.Ad.MaterialMetaOrBuilder>(
                getCreative(),
                getParentForChildren(),
                isClean());
        creative_ = null;
      }
      return creativeBuilder_;
    }

    private long bidPrice_ ;
    /**
     * <pre>
     * 竞价模式时必填。出价, cpm分
     * </pre>
     *
     * <code>uint64 bid_price = 3;</code>
     * @return The bidPrice.
     */
    @java.lang.Override
    public long getBidPrice() {
      return bidPrice_;
    }
    /**
     * <pre>
     * 竞价模式时必填。出价, cpm分
     * </pre>
     *
     * <code>uint64 bid_price = 3;</code>
     * @param value The bidPrice to set.
     * @return This builder for chaining.
     */
    public Builder setBidPrice(long value) {

      bidPrice_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 竞价模式时必填。出价, cpm分
     * </pre>
     *
     * <code>uint64 bid_price = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearBidPrice() {
      bitField0_ = (bitField0_ & ~0x00000004);
      bidPrice_ = 0L;
      onChanged();
      return this;
    }

    private int bidType_ ;
    /**
     * <pre>
     * 出价类型 默认:1,1=cpm
     * </pre>
     *
     * <code>int32 bid_type = 5;</code>
     * @return The bidType.
     */
    @java.lang.Override
    public int getBidType() {
      return bidType_;
    }
    /**
     * <pre>
     * 出价类型 默认:1,1=cpm
     * </pre>
     *
     * <code>int32 bid_type = 5;</code>
     * @param value The bidType to set.
     * @return This builder for chaining.
     */
    public Builder setBidType(int value) {

      bidType_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 出价类型 默认:1,1=cpm
     * </pre>
     *
     * <code>int32 bid_type = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearBidType() {
      bitField0_ = (bitField0_ & ~0x00000008);
      bidType_ = 0;
      onChanged();
      return this;
    }

    private int settleType_ ;
    /**
     * <pre>
     * 计费类型 默认:1,1=cpm
     * </pre>
     *
     * <code>int32 settle_type = 6;</code>
     * @return The settleType.
     */
    @java.lang.Override
    public int getSettleType() {
      return settleType_;
    }
    /**
     * <pre>
     * 计费类型 默认:1,1=cpm
     * </pre>
     *
     * <code>int32 settle_type = 6;</code>
     * @param value The settleType to set.
     * @return This builder for chaining.
     */
    public Builder setSettleType(int value) {

      settleType_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 计费类型 默认:1,1=cpm
     * </pre>
     *
     * <code>int32 settle_type = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearSettleType() {
      bitField0_ = (bitField0_ & ~0x00000010);
      settleType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object ext_ = "";
    /**
     * <pre>
     * 扩展字段
     * </pre>
     *
     * <code>string ext = 10;</code>
     * @return The ext.
     */
    public java.lang.String getExt() {
      java.lang.Object ref = ext_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ext_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 扩展字段
     * </pre>
     *
     * <code>string ext = 10;</code>
     * @return The bytes for ext.
     */
    public com.google.protobuf.ByteString
        getExtBytes() {
      java.lang.Object ref = ext_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ext_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 扩展字段
     * </pre>
     *
     * <code>string ext = 10;</code>
     * @param value The ext to set.
     * @return This builder for chaining.
     */
    public Builder setExt(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ext_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 扩展字段
     * </pre>
     *
     * <code>string ext = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearExt() {
      ext_ = getDefaultInstance().getExt();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 扩展字段
     * </pre>
     *
     * <code>string ext = 10;</code>
     * @param value The bytes for ext to set.
     * @return This builder for chaining.
     */
    public Builder setExtBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ext_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:Ad)
  }

  // @@protoc_insertion_point(class_scope:Ad)
  private static final cn.taken.ad.logic.media.ubix.dto.Ad DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.taken.ad.logic.media.ubix.dto.Ad();
  }

  public static cn.taken.ad.logic.media.ubix.dto.Ad getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Ad>
      PARSER = new com.google.protobuf.AbstractParser<Ad>() {
    @java.lang.Override
    public Ad parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Ad> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Ad> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.taken.ad.logic.media.ubix.dto.Ad getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

