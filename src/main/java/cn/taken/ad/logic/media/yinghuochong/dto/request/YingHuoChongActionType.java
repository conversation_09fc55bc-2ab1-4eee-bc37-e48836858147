package cn.taken.ad.logic.media.yinghuochong.dto.request;

public enum YingHuoChongActionType {
    LANDINGPAGE(1),
    QUICK_APP(2),
    DOWNLOAD(3),
    DEEPLINK(4);

    private final int code;

    YingHuoChongActionType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static YingHuoChongActionType findByCode(int code) {
        for (YingHuoChongActionType actionType : YingHuoChongActionType.values()) {
            if (actionType.code == code) {
                return actionType;
            }
        }
        return LANDINGPAGE; // 默认返回落地页
    }
}
