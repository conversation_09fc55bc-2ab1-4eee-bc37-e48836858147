package cn.taken.ad.task.statistics;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.statistics.StatisticsAdvertiserErrorCode;
import cn.taken.ad.core.service.statistics.StatisticsAdvertiserErrorCodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class StatisticsAdvertiserErrorCodeTask {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Resource(name = "BaseRedis")
    private RedisClient redis;

    @Resource
    private StatisticsAdvertiserErrorCodeService statisticsAdvertiserErrorCodeService;

    @SuperScheduled(cron = "20 */1 * * * ?", only = true)
    public void minute() {
        Date statisticsDate = new Date(System.currentTimeMillis() - (60L * 1000L));
        this.statisticsMinute(statisticsDate);
    }

    @SuperScheduled(cron = "40 0/5 * * * ?", only = true)
    public void hour() {
        Date statisticsDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(statisticsDate);
        if (c.get(Calendar.MINUTE) < 9) {
            // 统计上一个小时的数据
            c.add(Calendar.HOUR_OF_DAY, -1);
            this.statisticsHour(c.getTime());
        }
        //当前小时
        this.statisticsHour(statisticsDate);
    }

    @SuperScheduled(cron = "50 0/10 * * * ?", only = true)
    public void day() {
        Date statisticsDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(statisticsDate);
        if (c.get(Calendar.HOUR_OF_DAY) == 0 && c.get(Calendar.MINUTE) < 20) {
            // 统计昨天的数据
            c.add(Calendar.DAY_OF_MONTH, -1);
            this.statisticsDay(c.getTime());
        }
        //当前天的
        this.statisticsDay(statisticsDate);
    }

    private void statisticsDay(Date statisticsDate) {
        String day = DateUtils.toString(statisticsDate, StatisticsType.DAY.getFormat());
        String beginTime = day + "00";
        String endTime = day + "23";

        List<StatisticsAdvertiserErrorCode> eventList = statisticsAdvertiserErrorCodeService.findStatisticsAdvertiserErrorCode(beginTime, endTime, StatisticsType.HOUR);
        if (!eventList.isEmpty()) {
            eventList.forEach(event -> {
                event.setStatisticsType(StatisticsType.DAY.getCode());
                event.setStatisticsTime(day);
            });
            statisticsAdvertiserErrorCodeService.deleteByTime(StatisticsType.DAY, day);
            statisticsAdvertiserErrorCodeService.saveList(eventList);
        }
        log.info("statistics day success {}", day);
    }

    private void statisticsHour(Date statisticsDate) {
        String hour = DateUtils.toString(statisticsDate, StatisticsType.HOUR.getFormat());
        String beginTime = hour + "00";
        String endTime = hour + "59";

        List<StatisticsAdvertiserErrorCode> eventList = statisticsAdvertiserErrorCodeService.findStatisticsAdvertiserErrorCode(beginTime, endTime, StatisticsType.MINUTE);
        if (!eventList.isEmpty()) {
            eventList.forEach(event -> {
                event.setStatisticsType(StatisticsType.HOUR.getCode());
                event.setStatisticsTime(hour);
            });
            statisticsAdvertiserErrorCodeService.deleteByTime(StatisticsType.HOUR, hour);
            statisticsAdvertiserErrorCodeService.saveList(eventList);
        }
        log.info("statistics hour success {}", hour);
    }

    private void statisticsMinute(Date statisticsDate) {
        String minute = DateUtils.toString(statisticsDate, StatisticsType.MINUTE.getFormat());
        Map<String, StatisticsAdvertiserErrorCode> errorCodeMap = new HashMap<>();
        for (; ; ) {
            List<StatisticsAdvertiserErrorCode> eventList = this.saveErrorCodeMinute(minute);
            if (CollectionUtils.isEmpty(eventList)) {
                break;
            }
            for (StatisticsAdvertiserErrorCode errorCode : eventList) {
                String key = errorCode.getAdvertiserId() + "&&" + errorCode.getAdvertiserAppId() + "&&" + errorCode.getAdvertiserTagId() + "&&" + errorCode.getCode();
                StatisticsAdvertiserErrorCode tmp = errorCodeMap.computeIfAbsent(key, k -> new StatisticsAdvertiserErrorCode(StatisticsType.MINUTE.getCode(), minute, errorCode.getAdvertiserId(), errorCode.getAdvertiserAppId(), errorCode.getAdvertiserTagId(), errorCode.getCode()));
                tmp.setTotal(tmp.getTotal() + errorCode.getTotal());
            }
        }
        if (!errorCodeMap.isEmpty()) {
            statisticsAdvertiserErrorCodeService.saveList(new ArrayList<>(errorCodeMap.values()));
        }
        log.info("statistics event minute success {}:{}", minute, errorCodeMap.size());
    }

    private List<StatisticsAdvertiserErrorCode> saveErrorCodeMinute(String minute) {
        return redis.rpop(BaseRedisKeys.QUEUE_STATISTICS_ADV_ERROR_CODE, 200, StatisticsAdvertiserErrorCode.class);
    }
}
