package cn.taken.ad.task;

import cn.taken.ad.component.obs.ObjectSaveClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.component.utils.compress.ZipUtils;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.configuration.PropertiesConfiguration;
import cn.taken.ad.configuration.dmp.DmpMonitor;
import cn.taken.ad.configuration.server.ServerInfoManager;
import cn.taken.ad.utils.file.DeviceFileUtils;
import cn.taken.ad.utils.file.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Component
public class DmpPackageSaveTask {

    private static final Logger log = LoggerFactory.getLogger(DmpPackageSaveTask.class);

    @Resource(name = "dmpObs")
    private ObjectSaveClient objectSaveClient;
    @Resource
    private ServerInfoManager serverInfoManager;
    @Resource
    private PropertiesConfiguration propertiesConfiguration;
    @Resource
    private DmpMonitor dmpMonitor;

    @SuperScheduled(fixedRate = 1000)
    public void saveFile() {
        int h = LocalDateTime.now().getHour();
        String tempDir = DeviceFileUtils.genPath(propertiesConfiguration.getDmpDir(), "package", h + "");
        try {
            for (String key : dmpMonitor.getMemoryKeys()) {
                String filename;
                if (key.contains("@")) {
                    filename = key.replace("@", "Md5@") + ".txt";
                } else {
                    filename = key + "Md5.txt";
                }
                File file = new File(tempDir, filename);
                StringBuilder buffer = new StringBuilder();
                String value;
                while ((value = dmpMonitor.poolMemory(key)) != null) {
                    buffer.append(value).append("\n");
                }
                if (buffer.length() > 0) {
                    FileUtils.writeAppend(file.getAbsolutePath(), buffer.toString());
                }
            }
        } catch (Exception e) {
            log.error("error:", e);
        }
    }

    @SuperScheduled(cron = "0 1 */1 * * ?")
    public void uploadFile() {
        log.info("start upload chunk file");
        // 清理待上传临时目录
        String uploadDir = DeviceFileUtils.genPath(propertiesConfiguration.getDmpDir(), "upload");
        FileUtils.delete(uploadDir);
        // 拿到存储目录
        int h = LocalDateTime.now().plusHours(-1).getHour();
        String packDir = DeviceFileUtils.genPath(propertiesConfiguration.getDmpDir(), "package", h + "");
        try {
            // 拿到所有文件
            List<File> fileList = DeviceFileUtils.getChildrenFiles(packDir, ".txt");
            if (fileList.isEmpty()) {
                return;
            }
            // 文件去重
            for (File file : fileList) {
                DeviceFileUtils.sortAndUniqFile(file, DeviceFileUtils.genPath(uploadDir, UUID.randomUUID().toString()), 100_0000);
            }
            // 文件压缩
            String fid = serverInfoManager.genOnlyBusinessId();
            String targetPath = DeviceFileUtils.genPath(uploadDir, serverInfoManager.genOnlyBusinessId() + ".zip");
            ZipUtils.zip(packDir, targetPath);
            // 拿到上传对象存储路径
            String day = DateUtils.toString(new Date(System.currentTimeMillis() - 60L * 60L * 1000L), "yyyyMMdd");
            String key = "dmp/package/" + day + "/device/" + fid + ".zip";
            String lf = "dmp/package/" + day + "/device/" + fid + ".end";
            File endFile = new File(uploadDir, fid + ".end");
            endFile.createNewFile();
            File file = new File(targetPath);
            //上传主数据
            objectSaveClient.upload2Times(key, file);
            //上传结束锁
            objectSaveClient.upload2Times(lf, endFile);
        } catch (Exception e) {
            log.error("upload to obs error:", e);
        } finally {
            FileUtils.delete(uploadDir);
            FileUtils.delete(packDir);
            log.info("finished upload chunk file");
        }
    }
}
