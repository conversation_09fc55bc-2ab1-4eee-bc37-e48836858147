import request from '@/utils/HttpUtils'

const baseUri = 'o/base/province/'

export function pageProvince(data) {
  return request({
    url: baseUri + 'page',
    method: 'post',
    data: data
  })
}

export function listProvince(data) {
  return request({
    url: baseUri + 'list',
    method: 'post',
    data: data
  })
}

export function addProvince(data) {
  return request({
    url: baseUri + 'add',
    method: 'post',
    data: data
  })
}

export function infoProvince(data) {
  return request({
    url: baseUri + 'info',
    method: 'post',
    data: data
  })
}

export function modifyProvince(data) {
  return request({
    url: baseUri + 'modify',
    method: 'post',
    data: data
  })
}
