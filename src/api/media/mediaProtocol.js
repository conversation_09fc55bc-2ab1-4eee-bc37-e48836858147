import request from '@/utils/HttpUtils'

const baseUri = 'o/media/protocol/'

export function pageMediaProtocol(data) {
  return request({
    url: baseUri + 'page',
    method: 'post',
    data: data
  })
}

export function listMediaProtocol(data) {
  return request({
    url: baseUri + 'list',
    method: 'post',
    data: data
  })
}

export function addMediaProtocol(data) {
  return request({
    url: baseUri + 'add',
    method: 'post',
    data: data
  })
}

export function modifyMediaProtocol(data) {
  return request({
    url: baseUri + 'modify',
    method: 'post',
    data: data
  })
}

export function infoMediaProtocol(data) {
  return request({
    url: baseUri + 'info',
    method: 'post',
    data: data
  })
}
