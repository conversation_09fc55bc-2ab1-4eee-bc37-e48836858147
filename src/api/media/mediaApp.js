import request from '@/utils/HttpUtils'

const baseUri = 'o/media/app/'

export function pageMediaApp(data) {
  return request({
    url: baseUri + 'page',
    method: 'post',
    data: data
  })
}

export function addMediaApp(data) {
  return request({
    url: baseUri + 'add',
    method: 'post',
    data: data
  })
}

export function modifyMediaApp(data) {
  return request({
    url: baseUri + 'modify',
    method: 'post',
    data: data
  })
}

export function auditMediaApp(data) {
  return request({
    url: baseUri + 'audit',
    method: 'post',
    data: data
  })
}

export function infoMediaApp(data) {
  return request({
    url: baseUri + 'info',
    method: 'post',
    data: data
  })
}

export function listMediaApp(data) {
  return request({
    url: baseUri + 'list',
    method: 'post',
    data: data
  })
}
