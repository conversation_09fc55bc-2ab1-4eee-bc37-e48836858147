<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" class="ruleForm" size="mini">
        <el-form-item label="媒体APP" prop="mediaAppId">
          <el-select
            v-model="listQuery.mediaAppId"
            filterable
            clearable
            placeholder="请选择媒体APP"
            style="width: 100%"
            remote
            :remote-method="loadMediaApps"
          >
            <el-option
              v-for="mediaApp in mediaApps"
              :key="mediaApp.id"
              :label="mediaApp.name + ' - ' + mediaApp.code + ' - ' + parseOsType(mediaApp.type)"
              :value="mediaApp.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告位">
          <el-input v-model="listQuery.name" placeholder="名称或CODE" />
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="primary" @click="handleFilter">查询</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="info" @click="remove">清除</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button type="success" icon="el-icon-circle-plus-outline" @click="addTag">新增</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :key="tableKey" v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">
      <el-table-column align="center" label="序号" min-width="60" type="index" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="CODE" prop="code" align="center" />
      <el-table-column label="所属APP" align="center">
        <template slot-scope="scope">
          {{ scope.row.mediaAppName }} - {{ scope.row.mediaAppCode }} - {{ parseOsType(scope.row.mediaAppType) }}
        </template>
      </el-table-column>
      <el-table-column label="类型" prop="type" align="center">
        <template slot-scope="scope">
          {{ parseTagType(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column label="超时时间" align="center">
        <template slot-scope="scope"> {{ scope.row.timeout }}毫秒 </template>
      </el-table-column>
      <el-table-column label="尺寸" align="center">
        <template slot-scope="scope"> {{ scope.row.width }} * {{ scope.row.height }} </template>
      </el-table-column>
      <el-table-column label="结算方式" align="center">
        <template slot-scope="scope">
          {{ parseBidType(scope.row.bidType) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <el-button type="success" size="mini" icon="el-icon-edit" title="编辑" circle @click="handleModify(row)" />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-if="paginationShow"
      class="pagination"
      :total="total"
      :total-page="totalPage"
      :page="currentPageNum"
      :start.sync="listQuery.start"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-drawer
      title="新增广告位"
      :show-close="false"
      size="50%"
      :visible.sync="addOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <MediaTagAdd v-if="addOpen" :is-update.sync="addUpdate" @changePageOpen="changeAddOpen" />
    </el-drawer>

    <el-drawer
      title="修改广告位"
      :show-close="false"
      size="50%"
      :visible.sync="modifyOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <MediaTagModify v-if="modifyOpen" :id.sync="infoId" :is-update.sync="modifyUpdate" @changePageOpen="changeModifyOpen" />
    </el-drawer>
  </div>
</template>
<script>
import { pageMediaTag } from '@/api/media/mediaTag' // 后台接口
import { listMediaApp } from '@/api/media/mediaApp' // 后台接口
import { listOsTypeApi, listTagTypeApi } from '@/api/public/typeinfo'
import { listBidTypeApi } from '@/api/public/typeinfo'
import Pagination from '@/components/Pagination' // 分页
import MediaTagAdd from './MediaTagAdd' // 新增
import MediaTagModify from './MediaTagModify' // 修改
export default {
  name: 'MediaTag',
  components: { MediaTagAdd, MediaTagModify, Pagination },
  data() {
    return {
      tableKey: 0,
      paginationShow: true,
      list: null,
      total: 0,
      totalPage: 0,
      currentPageNum: 0,
      listLoading: false,
      listQuery: {
        start: 0,
        limit: 20,
        mediaAppId: null,
        name: null
      },
      osTypeList: [],
      tagTypeList: [],
      bidTypeList: [],
      mediaApps: [],
      addOpen: false,
      modifyOpen: false,
      addUpdate: false,
      modifyUpdate: false,
      infoId: null
    }
  },
  created() {
    listTagTypeApi().then(response => {
      this.tagTypeList = response.result
    })
    listOsTypeApi().then(response => {
      this.osTypeList = response.result.filter(item => item.id !== 999)
    })
    listBidTypeApi().then(response => {
      this.bidTypeList = response.result
    })
    this.getList()
    this.loadMediaApps(null)
  },
  methods: {
    parseOsType(id) {
      const value = this.osTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseTagType(id) {
      const value = this.tagTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseBidType(id) {
      const value = this.bidTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    loadMediaApps(name) {
      listMediaApp({ name: name }).then(response => {
        this.mediaApps = response.result
      })
    },
    getList() {
      this.list = []
      this.total = 0
      this.totalPage = 0
      this.currentPageNum = 0
      this.paginationShow = false
      this.listLoading = true
      pageMediaTag(this.listQuery).then(response => {
        this.list = response.result.list
        this.total = response.result.totalCount
        this.totalPage = response.result.totalPage
        this.currentPageNum = response.result.currentPageNum
        this.listLoading = false
        this.paginationShow = true
      })
    },
    addTag() {
      this.addOpen = true
    },
    editTag(row) {
      this.infoId = row.id
      this.modifyOpen = true
    },
    handleFilter() {
      this.listQuery.start = 0
      this.getList()
    },
    changeAddOpen(open) {
      this.addOpen = open
      if (this.addUpdate) {
        this.getList()
      }
    },
    changeModifyOpen(flag) {
      this.modifyOpen = flag
      if (this.modifyUpdate) {
        this.getList()
      }
    },
    remove() {
      this.listQuery.start = 0
      this.listQuery.limit = 20
      this.listQuery.name = null
      this.listQuery.mediaAppId = null
      this.getList()
    },
    handleModify(row) {
      this.infoId = row.id
      this.modifyOpen = true
    }
  }
}
</script>
<style scoped>
::deep .el-drawer__body {
  overflow: auto;
}
</style>
