<template>
  <el-form ref="ruleForm" :model="user" status-icon :rules="rules" label-width="150px" size="mini" class="ruleForm">
    <el-form-item label="所属媒体">
      <span>{{ user.mediaName }} - {{ user.mediaCode }}</span>
    </el-form-item>
    <el-form-item label="用户名" prop="username">
      <span>{{ user.username }}</span>
    </el-form-item>
    <el-form-item label="姓名" prop="realname">
      <span>{{ user.realname }}</span>
    </el-form-item>
    <el-form-item label="手机号" prop="mobile">
      <span>{{ user.mobile }}</span>
    </el-form-item>
    <el-form-item label="E-mail" prop="email">
      <span>{{ user.email }}</span>
    </el-form-item>
    <el-form-item>
      <el-button type="danger" @click="createMM">关闭</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
export default {
  name: 'MediaUserInfo',
  props: {
    user: {
      required: true,
      type: Object
    }
  },
  data() {
    return {
      alongRoles: [],
      btnClicked: false,
      ruleForm: {
        id: null,
        username: null,
        realname: null,
        mobile: null,
        email: null,
        mediaName: null,
        mediaCode: null
      },
      rules: {}
    }
  },
  computed: {},
  created() {},
  methods: {
    closeMe() {
      this.$emit('changePageOpen', false)
      this.btnClicked = false
    },
    createMM() {
      this.closeMe()
    }
  }
}
</script>

<style scoped>
.ruleForm {
  margin-left: 2%;
  margin-right: 2%;
}
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
</style>
