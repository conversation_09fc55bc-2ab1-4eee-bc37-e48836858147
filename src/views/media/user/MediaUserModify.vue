<template>
  <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="150px" size="mini" class="ruleForm">
    <el-form-item label="所属媒体">
      <span>{{ ruleForm.mediaName }} - {{ ruleForm.mediaCode }}</span>
    </el-form-item>
    <el-form-item label="用户名:" prop="username">
      <span>{{ ruleForm.username }}</span>
    </el-form-item>
    <el-form-item label="姓名:" prop="realname">
      <el-input v-model="ruleForm.realname" maxlength="10" />
    </el-form-item>
    <el-form-item label="手机号:" prop="mobile">
      <el-input v-model="ruleForm.mobile" />
    </el-form-item>
    <el-form-item label="E-mail:" prop="email">
      <el-input v-model="ruleForm.email" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :disabled="btnClicked" @click="submitForm('ruleForm')">确定</el-button>
      <el-button @click="createMM">取消</el-button>
      <el-button type="danger" @click="createMM">关闭</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { infoUser, modifyUser } from '@/api/media/mediaUser'
import { real_name, phone, userEmail } from '@/utils/Validate'
export default {
  name: 'MediaUserModify',
  props: {
    id: {
      required: true,
      type: Number
    },
    isUpdate: {
      required: true,
      type: Boolean
    }
  },
  data() {
    // 姓名
    var realnameReg = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('姓名不能为空'))
      } else if (!real_name(value)) {
        callback(new Error('请输入中文和英文'))
      } else {
        callback()
      }
    }
    // 手机号
    var mobileReg = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('手机号不能为空'))
      } else if (!phone(value)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }
    // 邮箱
    var emailReg = (rule, value, callback) => {
      if (value !== '' && value !== null) {
        if (!userEmail(value)) {
          callback(new Error('请输入正确的邮箱'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      alongRoles: [],
      btnClicked: false,
      ruleForm: {
        id: null,
        username: null,
        realname: null,
        mobile: null,
        email: null
      },
      rules: {
        realname: [{ required: true, trigger: 'blur', validator: realnameReg }],
        mobile: [{ required: true, trigger: 'blur', validator: mobileReg }],
        email: [{ trigger: 'blur', validator: emailReg }]
      }
    }
  },
  computed: {},
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      const res = await infoUser(this.id)
      this.ruleForm = res.result
    },
    closeMe() {
      this.$emit('changePageOpen', false)
      this.btnClicked = false
    },
    createMM() {
      this.$emit('update:isUpdate', false)
      this.closeMe()
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.btnClicked = true
          modifyUser(this.ruleForm)
            .then(response => {
              if (response.success === true) {
                this.$message({
                  message: '修改用户成功',
                  type: 'success'
                })
                this.$emit('update:isUpdate', true)
                this.closeMe()
              }
            })
            .catch(() => {
              this.btnClicked = false
            })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.ruleForm {
  margin-left: 2%;
  margin-right: 2%;
}
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
</style>
