<template>
  <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" size="mini" class="ruleForm">
    <el-form-item label="统计日期" prop="reportTime">
      <span>{{ ruleForm.reportTime }}</span>
    </el-form-item>
    <el-form-item label="所属媒体" prop="name"> {{ ruleForm.mediaName }} - {{ ruleForm.mediaCode }} </el-form-item>
    <el-form-item label="所属APP" prop="name">
      {{ ruleForm.mediaAppName }} - {{ ruleForm.mediaAppCode }} - {{ parseOsType(ruleForm.mediaAppType) }}
    </el-form-item>
    <el-form-item label="所属广告位" prop="mediaTagName">
      <span>{{ ruleForm. mediaTagName }} - {{ ruleForm. mediaTagCode }} - {{ parseTagType(ruleForm. mediaTagType) }}</span>
    </el-form-item>
    <el-form-item label="结算方式" prop="mediaTagBidType">
      <span>{{ parseBidType(ruleForm. mediaTagBidType) }}</span>
    </el-form-item>
    <el-form-item v-if="ruleForm.mediaTagBidType === 2" label="结算比例" prop="settlementRatio">
      <el-input v-model="ruleForm.settlementRatio">
        <template slot="append">%</template>
      </el-input>
    </el-form-item>
    <el-form-item label="状态" prop="state">
      <span>{{ parseFinancialState(ruleForm.state) }}</span>
    </el-form-item>
    <el-form-item label="发布状态" prop="releaseState">
      <span>{{ parseFinancialReleaseState(ruleForm.releaseState) }}</span>
    </el-form-item>
    <el-form-item label="平台填充量">
      <span>{{ ruleForm.participatingTotal }}</span>
    </el-form-item>
    <el-form-item label="平台曝光量">
      <span>{{ ruleForm.exposureTotal }}</span>
    </el-form-item>
    <el-form-item label="平台点击量">
      <span>{{ ruleForm.clickTotal }}</span>
    </el-form-item>
    <el-form-item label="平台消耗">
      <span v-if="ruleForm.amount">{{ ruleForm.amount }}元</span>
    </el-form-item>
    <el-form-item label="实际填充量" prop="realParticipatingTotal">
      <el-input v-model="ruleForm.realParticipatingTotal" />
    </el-form-item>
    <el-form-item label="实际曝光量" prop="realExposureTotal">
      <el-input v-model="ruleForm.realExposureTotal" />
    </el-form-item>
    <el-form-item label="实际点击量" prop="realClickTotal">
      <el-input v-model="ruleForm.realClickTotal" />
    </el-form-item>
    <el-form-item label="实际消耗" prop="realAmount">
      <el-input v-model="ruleForm.realAmount">
        <template slot="append">元</template>
      </el-input>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :disabled="btnClicked" @click="submitForm('ruleForm')">确定</el-button>
      <el-button @click="createMM">取消</el-button>
      <el-button type="danger" @click="createMM">关闭</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { infoApi, modifyApi } from '@/api/financial/financialMedia' // 后台接口
import { listTagTypeApi, listOsTypeApi, listBidTypeApi, financialStateApi, financialReleaseStateApi } from '@/api/public/typeinfo'
export default {
  name: 'FinancialMediaModify',
  components: { },
  props: {
    isUpdate: {
      required: true,
      type: Boolean
    },
    id: {
      required: true,
      type: Number
    }
  },
  data() {
    return {
      btnClicked: false,
      ruleForm: {
        id: null,
        reportTime: null,
        mediaName: null,
        mediaCode: null,
        mediaId: null,
        mediaAppId: null,
        mediaTagId: null,
        mediaAppName: null,
        mediaAppCode: null,
        mediaAppType: null,
        mediaTagName: null,
        mediaTagCode: null,
        mediaTagType: null,
        mediaTagBidType: null,
        settlementRatio: null,
        participatingTotal: null,
        exposureTotal: null,
        clickTotal: null,
        amount: null,
        realParticipatingTotal: null,
        realExposureTotal: null,
        realClickTotal: null,
        realAmount: null,
        revenueAmount: null,
        state: null,
        releaseState: null
      },
      medias: [],
      mediaApps: [],
      mediaTags: [],
      osTypeList: [],
      tagTypeList: [],
      bidTypeList: [],
      financialStateList: [],
      financialReleaseStateList: [],
      rules: {
        realParticipatingTotal: [{ required: true, trigger: 'blur', message: '不能为空' }],
        realExposureTotal: [{ required: true, trigger: 'blur', message: '不能为空' }],
        realClickTotal: [{ required: true, trigger: 'blur', message: '不能为空' }]
      }
    }
  },
  computed: {},
  created() {
    listTagTypeApi().then(response => {
      this.tagTypeList = response.result
    })
    listOsTypeApi().then(response => {
      this.osTypeList = response.result.filter(item => item.id !== 999)
    })
    listBidTypeApi().then(response => {
      this.bidTypeList = response.result
    })
    financialStateApi().then(response => {
      this.financialStateList = response.result
    })
    financialReleaseStateApi().then(response => {
      this.financialReleaseStateList = response.result
    })
    this.loadData()
  },
  methods: {
    parseOsType(id) {
      const value = this.osTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseTagType(id) {
      const value = this.tagTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseBidType(id) {
      const value = this.bidTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseFinancialState(id) {
      const value = this.financialStateList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseFinancialReleaseState(id) {
      const value = this.financialReleaseStateList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    async loadData() {
      const res = await infoApi({ id: this.id })
      this.ruleForm = res.result
    },
    closeMe() {
      this.$emit('changePageOpen', false)
      this.btnClicked = false
    },
    createMM() {
      this.$emit('update:isUpdate', false)
      this.closeMe()
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.ruleForm.mediaTagBidType === 1 && (this.ruleForm.realAmount === null || this.ruleForm.realAmount === '' || this.ruleForm.realAmount === '0' || this.ruleForm.realAmount === 0)) {
            this.$message({
              message: '竞价结算，实际消耗不能为空',
              type: 'error'
            })
            return false
          }
          this.btnClicked = true
          modifyApi(this.ruleForm)
            .then(response => {
              this.$message({
                message: '修改成功',
                type: 'success'
              })
              this.$emit('update:isUpdate', true)
              this.closeMe()
            })
            .catch(() => {
              this.btnClicked = false
            })
          return true
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.ruleForm {
  margin-left: 2%;
  margin-right: 2%;
}
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
</style>
