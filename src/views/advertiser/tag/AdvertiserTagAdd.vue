<template>
  <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="120px" size="mini" class="ruleForm">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>基础信息</span>
        <!-- <el-button style="float: right; padding: 3px 0" type="text">隐藏</el-button> -->
      </div>
      <div class="text item">
        <el-form-item label="预算" prop="advertiserId">
          <el-select
            v-model="ruleForm.advertiserId"
            filterable
            clearable
            placeholder="请选择预算"
            style="width: 100%"
            remote
            :remote-method="loadAdvertiser"
            @change="changeAdvertiser"
          >
            <el-option v-for="advertiser in advertisers" :key="advertiser.id" :label="advertiser.name" :value="advertiser.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="预算APP" prop="advertiserAppId">
          <el-select
            v-model="ruleForm.advertiserAppId"
            filterable
            clearable
            placeholder="请选择预算APP"
            style="width: 100%"
            remote
            :remote-method="loadAdvApp"
            @change="getRealName"
          >
            <el-option
              v-for="advertiserApp in advertiserApps"
              :key="advertiserApp.id"
              :label="advertiserApp.name + ' - ' + advertiserApp.code + ' - ' + parseOsType(advertiserApp.type)"
              :value="advertiserApp.id"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="DSP类型" prop="dspType">
          <span>{{ ruleForm.dspType === 1 ? '自有' : '三方' }}</span>
        </el-form-item> -->
        <el-form-item label="类型" prop="type">
          <el-select v-model="ruleForm.type" filterable clearable placeholder="请选择类型" style="width: 100%">
            <el-option v-for="(tt, x) in types" :key="'tagt-' + x" :label="tt.name" :value="tt.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="name">
          <el-input v-model="ruleForm.name" />
        </el-form-item>
        <el-form-item label="名称">
          {{ realTagName }}
        </el-form-item>
        <el-form-item v-if="ruleForm.dspType === 2" label="CODE" prop="code">
          <el-input v-model="ruleForm.code" />
        </el-form-item>
        <el-form-item v-if="ruleForm.dspType === 1" label="自有DSP账户" prop="accountId">
          <el-select
            v-model="ruleForm.accountId"
            filterable
            clearable
            placeholder="请选择账户"
            style="width: 100%"
            remote
            :remote-method="loadAccount"
          >
            <el-option v-for="item in accountList" :key="item.id" :label="item.accountName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="超时时间" prop="timeout">
          <el-input v-model="ruleForm.timeout" type="number">
            <template slot="append">毫秒</template>
          </el-input>
        </el-form-item>
        <el-form-item label="过滤重复事件" prop="filterRepeatEvent">
          <el-switch v-model="ruleForm.filterRepeatEvent" />
        </el-form-item>
        <el-form-item label="追加上报事件" prop="appendEventTypes">
          <el-select
            v-model="ruleForm.appendEventTypes"
            style="width: 100%"
            filterable
            clearable
            multiple
            placeholder="请选择需要追加的事件，事件链接后台生成"
          >
            <el-option v-for="item in eventTypeOption" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-show="ruleForm.advertiserId && localProtocol.tagParam && localProtocol.tagParam !== '' && localProtocol.tagParam !== '[]'"
          label="扩展参数"
          prop="pnyParam"
        >
          <ParameterValueForm ref="pnyParam" />
        </el-form-item>
      </div>
    </el-card>
    <br />
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>结算信息</span>
        <!-- <el-button style="float: right; padding: 3px 0" type="text">隐藏</el-button> -->
      </div>
      <div class="text item">
        <el-form-item label="结算方式" prop="settlementType">
          <el-radio-group v-model="ruleForm.settlementType" @change="getRealName">
            <el-radio v-for="item in bidTypeList" :key="item.id" :label="item.id">{{ item.name }} </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.settlementType == 1" label="竞价模式" prop="bidPriceType">
          <el-radio-group v-model="ruleForm.bidPriceType">
            <el-radio v-for="item in bidPriceTypeList" :key="item.id" :label="item.id">{{ item.name }} </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.settlementType == 1 && ruleForm.bidPriceType == 1" label="涨幅比例" prop="bidRisesRatio">
          <el-input v-model="ruleForm.bidRisesRatio" type="number">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="ruleForm.settlementType == 2" label="价格处理" prop="sharingPriceType">
          <el-radio-group v-model="ruleForm.sharingPriceType">
            <el-radio v-for="item in sharingPriceTypeList" :key="item.id" :label="item.id">{{ item.name }} </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="
            (ruleForm.settlementType == 1 && ruleForm.bidPriceType == 2) || (ruleForm.settlementType == 2 && ruleForm.sharingPriceType == 2)
          "
          label="固定低价"
          prop="fixedPrice"
        >
          <el-input v-model="ruleForm.fixedPrice" type="number">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </div>
    </el-card>
    <br />
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>基础规则</span>
        <!-- <el-button style="float: right; padding: 3px 0" type="text">隐藏</el-button> -->
      </div>
      <div class="text item">
        <el-form-item label="开启规则" prop="limitRuleOpen">
          <el-switch v-model="ruleForm.limitRuleOpen" />
        </el-form-item>
        <template v-if="ruleForm.limitRuleOpen">
          <el-form-item label="限量维度" prop="limitType">
            <el-radio-group v-model="ruleForm.limitType">
              <el-radio v-for="item in limitTypeList" :key="item.id" :label="item.id">{{ item.name }} </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="ruleForm.limitType !== 4" label="限量额度" prop="quota">
            <el-input v-model="ruleForm.quota" type="number">
              <template slot="append">
                次
                <template v-if="ruleForm.limitType === 1"> /每天 </template>
                <template v-if="ruleForm.limitType === 2"> /每小时 </template>
                <template v-if="ruleForm.limitType === 3"> /每秒 </template>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="允许时间(小时)" prop="targetTime">
            <el-select v-model="ruleForm.startTime" filterable clearable placeholder="请选择起始小时" style="width: 45%">
              <el-option v-for="hour in hourList" :key="hour" :label="hour" :value="hour" />
            </el-select>
            &nbsp;&nbsp;-&nbsp;&nbsp;
            <el-select v-model="ruleForm.endTime" filterable clearable placeholder="请选择结束小时" style="width: 45%">
              <el-option v-for="hour in hourList" :key="hour" :label="hour" :value="hour" />
            </el-select>
          </el-form-item>
          <el-form-item label="日限曝光量">
            <el-input v-model="ruleForm.filterExposureNum" type="number">
              <template slot="append">次/日</template>
            </el-input>
          </el-form-item>
          <el-form-item label="日限点击量">
            <el-input v-model="ruleForm.filterClickNum" type="number">
              <template slot="append">次/日</template>
            </el-input>
          </el-form-item>
          <el-form-item label="单设备限请求量">
            <el-input v-model="ruleForm.filterDeviceReqNum" type="number">
              <template slot="append">次/日</template>
            </el-input>
          </el-form-item>
          <el-form-item label="单设备限曝光量">
            <el-input v-model="ruleForm.filterDeviceExposureNum" type="number">
              <template slot="append">次/日</template>
            </el-input>
          </el-form-item>
        </template>
      </div>
    </el-card>
    <br />
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>定向规则</span>
        <!-- <el-button style="float: right; padding: 3px 0" type="text">隐藏</el-button> -->
      </div>
      <div class="text item">
        <el-form-item label="开启定向" prop="directOnOff">
          <el-switch v-model="ruleForm.directOnOff" />
        </el-form-item>
        <template v-if="ruleForm.directOnOff">
          <el-form-item label="地区">
            <el-cascader v-model="ruleForm.directCitySelection" clearable :props="directCityProps" style="width: 100%" />
          </el-form-item>
          <el-form-item label="APP行业">
            <el-cascader v-model="ruleForm.industrySelection" clearable :props="industryProps" style="width: 100%" />
          </el-form-item>
          <el-form-item label="APP包名">
            <el-input
              v-model="ruleForm.targetAppPackage"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 5 }"
              placeholder="多个英文逗号分割"
            />
          </el-form-item>
          <el-form-item label="设备型号">
            <el-input
              v-model="ruleForm.targetDeviceModel"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 5 }"
              placeholder="多个英文逗号分割"
            />
          </el-form-item>
          <el-form-item label="设备机型">
            <el-checkbox-group v-model="ruleForm.targetDeviceBrandCheck">
              <el-checkbox v-for="item in deviceBrandTypes" :key="'tdbc-' + item.id" :label="item.id">{{ item.name }} </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="网络类型">
            <el-checkbox-group v-model="ruleForm.targetNetworkCheck">
              <el-checkbox v-for="item in netWorkTypes" :key="'nk' + item.id" :label="item.id">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="运营商">
            <el-checkbox-group v-model="ruleForm.targetOperatorCheck">
              <el-checkbox v-for="item in operatorTypes" :key="'opr' + item.id" :label="item.id">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="系统版本">
            <el-select v-model="ruleForm.targetOsVersionType" style="width: 20%" clearable>
              <el-option v-for="item in compareTypeList" :key="item.id" :value="item.id" :label="item.name" />
            </el-select>
            <el-input v-if="ruleForm.targetOsVersionType > 1" v-model="ruleForm.targetOsVersion" style="width: 60%">
              <template slot="append">版本号</template>
            </el-input>
          </el-form-item>
          <el-form-item label="已安装APP包名">
            <el-input
              v-model="ruleForm.targetInstalledAppPackage"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 5 }"
              placeholder="多个英文逗号分割"
            />
          </el-form-item>
          <el-form-item label="人群包">
            <el-select
              v-model="ruleForm.targetPackId"
              filterable
              clearable
              style="width: 100%"
              remote
              :remote-method="loadDmpPackage"
            >
              <el-option v-for="item in dmpPackList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </template>
      </div>
    </el-card>
    <br />
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>过滤规则</span>
        <!-- <el-button style="float: right; padding: 3px 0" type="text">隐藏</el-button> -->
      </div>
      <div class="text item">
        <el-form-item label="开启过滤" prop="filterOnOff">
          <el-switch v-model="ruleForm.filterOnOff" />
        </el-form-item>
        <template v-if="ruleForm.filterOnOff">
          <el-form-item label="地区">
            <el-cascader v-model="ruleForm.filterCitySelection" clearable :props="directCityProps" style="width: 100%" />
          </el-form-item>
          <el-form-item label="APP行业">
            <el-cascader v-model="ruleForm.filterIndustrySelection" clearable :props="industryProps" style="width: 100%" />
          </el-form-item>
          <el-form-item label="APP包名">
            <el-input
              v-model="ruleForm.filterAppPackage"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 5 }"
              placeholder="多个英文逗号分割"
            />
          </el-form-item>
          <el-form-item label="设备型号">
            <el-input
              v-model="ruleForm.filterDeviceModel"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 5 }"
              placeholder="多个英文逗号分割"
            />
          </el-form-item>
          <el-form-item label="链接域名">
            <el-input
              v-model="ruleForm.filterUrlDomain"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 5 }"
              placeholder="多个英文逗号分割"
            />
          </el-form-item>
          <el-form-item label="设备机型">
            <el-checkbox-group v-model="ruleForm.filterDeviceBrandCheck">
              <el-checkbox v-for="item in deviceBrandTypes" :key="'tdbc-' + item.id" :label="item.id">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="网络类型">
            <el-checkbox-group v-model="ruleForm.filterNetworkCheck">
              <el-checkbox v-for="item in netWorkTypes" :key="'nk' + item.id" :label="item.id">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="运营商">
            <el-checkbox-group v-model="ruleForm.filterOperatorCheck">
              <el-checkbox v-for="item in operatorTypes" :key="'opr' + item.id" :label="item.id">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="过滤空设备">
            <el-radio v-model="ruleForm.filterEmptyDevice" :label="0">不过滤</el-radio>
            <el-radio v-model="ruleForm.filterEmptyDevice" :label="1">过滤</el-radio>
          </el-form-item>
          <el-form-item label="过滤无效流量">
            <el-radio v-model="ruleForm.filterInvalidDevice" :label="0">不过滤</el-radio>
            <el-radio v-model="ruleForm.filterInvalidDevice" :label="1">过滤</el-radio>
          </el-form-item>
          <el-form-item label="过滤国外IP" prop="filterForeignIp">
            <el-radio v-model="ruleForm.filterForeignIp" :label="0">否</el-radio>
            <el-radio v-model="ruleForm.filterForeignIp" :label="1">是</el-radio>
          </el-form-item>
          <el-form-item label="已安装APP包名">
            <el-input
              v-model="ruleForm.filterInstalledAppPackage"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 5 }"
              placeholder="多个英文逗号分割"
            />
          </el-form-item>
          <el-form-item label="人群包">
            <el-select
              v-model="ruleForm.filterPackId"
              filterable
              clearable
              style="width: 100%"
              remote
              :remote-method="loadDmpPackage"
            >
              <el-option v-for="item in dmpPackList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </template>
      </div>
    </el-card>
    <br />

    <el-form-item>
      <el-button type="primary" :disabled="btnClicked" @click="submitForm('ruleForm')">确定</el-button>
      <el-button @click="createMM">取消</el-button>
      <el-button type="danger" @click="createMM">关闭</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { addAdvertiserTag } from '@/api/advertiser/advertiserTag'
import { listAdvertiser } from '@/api/advertiser/advertiserMain'
import { listAdvertiserApp } from '@/api/advertiser/advertiserApp'
import { infoAdvertiserProtocol } from '@/api/advertiser/advertiserProtocol'
import { listProvince } from '@/api/base/province'
import { listCity } from '@/api/base/city'
import { listAppIndustry } from '@/api/base/appIndustry'
import { listTagTypeApi, listCarrierTypeApi, listConnectTypeApi, listDeviceBrandApi, listOsTypeApi } from '@/api/public/typeinfo'
import { listBidTypeApi, listLimitTypeApi, listBidPriceTypeApi, listSharingPriceTypeApi, listCompareTypeApi } from '@/api/public/typeinfo'
import { listApi as listDmpPackApi } from '@/api/dmp/dmpPackage'
import ParameterValueForm from '@/components/ParameterValueForm'
import { listEventApi } from '@/api/public/typeinfo'
import { listAccountApi } from '@/api/dsp/account'
export default {
  name: 'AdvertiserTagAdd',
  components: { ParameterValueForm },
  props: {
    isUpdate: {
      required: true,
      type: Boolean
    }
  },
  data() {
    return {
      btnClicked: false,
      eventTypeOption: [],
      ruleForm: {
        advertiserId: null,
        advertiserAppId: null,
        name: null,
        code: null,
        type: null,
        timeout: 600,
        pnyParam: null,
        appendEventTypes: [1, 2],

        settlementType: 1,
        bidPriceType: 1,
        bidRisesRatio: null,
        sharingPriceType: 1,
        fixedPrice: null,

        limitRuleOpen: false,
        limitType: 4,
        quota: null,
        startTime: null,
        endTime: null,
        filterExposureNum: null,
        filterClickNum: null,
        filterDeviceReqNum: null,
        filterDeviceExposureNum: null,

        directOnOff: false,
        directCitySelection: [],
        industrySelection: [],
        targetAppPackage: null,
        targetDeviceModel: null,
        targetDeviceBrandCheck: [],
        targetNetworkCheck: [],
        targetOperatorCheck: [],
        targetOsVersionType: null,
        targetOsVersion: null,
        targetInstalledAppPackage: null,
        targetPackId: null,

        filterOnOff: false,
        filterCitySelection: [],
        filterIndustrySelection: [],
        filterAppPackage: null,
        filterDeviceModel: null,
        filterUrlDomain: null,
        filterDeviceBrandCheck: [],
        filterNetworkCheck: [],
        filterOperatorCheck: [],
        filterEmptyDevice: 0,
        filterInvalidDevice: 0,
        dspType: 2,
        accountId: null,
        filterForeignIp: 0,
        filterInstalledAppPackage: null,
        filterPackId: null,
        filterRepeatEvent: 0
      },
      rules: {
        name: [{ required: true, trigger: 'blur', message: '不能为空' }],
        code: [{ required: true, trigger: 'blur', message: '不能为空' }],
        advertiserId: [{ required: true, trigger: 'change', message: '不能为空' }],
        advertiserAppId: [{ required: true, trigger: 'change', message: '不能为空' }],
        type: [{ required: true, trigger: 'blur', message: '不能为空' }],
        settlementType: [{ required: true, trigger: 'blur', message: '不能为空' }],
        timeout: [{ required: true, trigger: 'blur', message: '不能为空' }],
        fixedPrice: [{ required: true, trigger: 'blur', message: '不能为空' }],
        bidRisesRatio: [{ required: true, trigger: 'blur', message: '不能为空' }],
        bidPriceType: [{ required: true, trigger: 'blur', message: '不能为空' }],
        sharingPriceType: [{ required: true, trigger: 'blur', message: '不能为空' }],
        limitType: [{ required: true, trigger: 'blur', message: '不能为空' }],
        quota: [{ required: true, trigger: 'blur', message: '不能为空' }],
        filterForeignIp: [{ required: true, trigger: 'blur', message: '不能为空' }],
        limitRuleOpen: [{ required: true, trigger: 'blur', message: '不能为空' }],
        directOnOff: [{ required: true, trigger: 'blur', message: '不能为空' }],
        filterOnOff: [{ required: true, trigger: 'blur', message: '不能为空' }],
        filterEmptyDevice: [{ required: true, trigger: 'blur', message: '不能为空' }],
        filterInvalidDevice: [{ required: true, trigger: 'blur', message: '不能为空' }],
        appendEventTypes: [{ required: true, trigger: 'blur', message: '不能为空' }],
        dspType: [{ required: true, trigger: 'blur', message: '未选择' }],
        accountId: [{ required: true, trigger: 'blur', message: '未选择' }]
      },
      advertisers: [],
      advertiserApps: [],
      localProtocol: {},
      types: [],
      netWorkTypes: [],
      operatorTypes: [],
      deviceBrandTypes: [],
      hourList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      pnyParams: [],
      osTypeList: [],
      bidTypeList: [],
      limitTypeList: [],
      bidPriceTypeList: [],
      sharingPriceTypeList: [],
      compareTypeList: [],
      accountList: [],
      dmpPackList: [],
      directCityProps: {
        multiple: true,
        lazy: true,
        leaf: 'isLeaf',
        label: 'name',
        value: 'code',
        lazyLoad(node, resolve) {
          const { level } = node
          if (level === 0) {
            // 加载省份
            listProvince({}).then(dd => {
              resolve(
                dd.result.map(item => ({
                  ...item,
                  isLeaf: false
                }))
              )
            })
          } else if (level === 1) {
            // 加载城市
            listCity({ provinceId: node.data.id }).then(dd => {
              resolve(
                dd.result.map(item => ({
                  ...item,
                  isLeaf: true
                }))
              )
            })
          } else {
            resolve([])
          }
        }
      },
      industryProps: {
        multiple: true,
        lazy: true,
        leaf: 'isLeaf',
        label: 'name',
        value: 'id',
        lazyLoad(node, resolve) {
          const { level } = node
          if (level === 0) {
            listAppIndustry({ hasParent: false }).then(dd => {
              resolve(
                dd.result.map(item => ({
                  ...item,
                  isLeaf: false
                }))
              )
            })
          } else if (level === 1) {
            listAppIndustry({ hasParent: true, parentId: node.data.id }).then(dd => {
              resolve(
                dd.result.map(item => ({
                  ...item,
                  isLeaf: true
                }))
              )
            })
          } else {
            resolve([])
          }
        }
      },
      realTagName: null
    }
  },
  computed: {},
  watch: {
    'ruleForm.name': {
      handler(v) {
        this.getRealName()
      },
      immediate: true
    }
  },
  created() {
    listLimitTypeApi().then(response => {
      this.limitTypeList = response.result
    })
    listBidPriceTypeApi().then(response => {
      this.bidPriceTypeList = response.result
    })
    listSharingPriceTypeApi().then(response => {
      this.sharingPriceTypeList = response.result
    })
    listCompareTypeApi().then(response => {
      this.compareTypeList = response.result
    })
    listBidTypeApi().then(response => {
      this.bidTypeList = response.result
    })
    listOsTypeApi().then(response => {
      this.osTypeList = response.result.filter(item => item.id !== 999)
    })
    listTagTypeApi({}).then(response => {
      this.types = response.result
    })
    listCarrierTypeApi({}).then(response => {
      this.operatorTypes = response.result
    })
    listConnectTypeApi({}).then(response => {
      this.netWorkTypes = response.result
    })
    listDeviceBrandApi({}).then(response => {
      this.deviceBrandTypes = response.result
    })
    listEventApi().then(response => {
      if (response.result != null && response.result.length !== 0) {
        this.eventTypeOption = response.result
      }
    })
    this.loadDmpPackage(null)
    this.loadAdvertiser(null)
    this.loadAccount(null)
  },
  methods: {
    parseBidType(id) {
      const value = this.bidTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseTagType(id) {
      const value = this.types.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseOsType(id) {
      const value = this.osTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    loadAdvertiser(name) {
      listAdvertiser({ name: name }).then(response => {
        this.advertisers = response.result
      })
    },
    async changeAdvertiser(id) {
      this.ruleForm.advertiserAppId = null
      this.advertiserApps = []
      this.loadAdvApp(null)
      if (id) {
        const m = this.advertisers.find(item => item.id === id)
        const o = await infoAdvertiserProtocol({ id: m.protocolId })
        this.localProtocol = o.result
        if (this.localProtocol.code === 'DSPRTA') {
          this.ruleForm.dspType = 1
        } else {
          this.ruleForm.dspType = 2
        }
        if (o.result.tagParam) {
          this.$refs.pnyParam.setParamText(
            JSON.parse(o.result.tagParam).map(item => ({
              ...item,
              paramsValue: ''
            }))
          )
        } else {
          this.$refs.pnyParam.setParamText([])
        }
      }
    },
    loadAdvApp(name) {
      if (this.ruleForm.advertiserId) {
        listAdvertiserApp({ name: name, advertiserId: this.ruleForm.advertiserId }).then(response => {
          this.advertiserApps = response.result
          this.getRealName()
        })
      }
    },
    closeMe() {
      this.$emit('changePageOpen', false)
      this.btnClicked = false
    },
    createMM() {
      this.$emit('update:isUpdate', false)
      this.closeMe()
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.btnClicked = true
          this.ruleForm.fixedPrice = this.ruleForm.fixedPrice * 100.0
          this.ruleForm.pnyParam = this.$refs.pnyParam.getParamText()
          this.ruleForm.filterRepeatEvent = this.ruleForm.filterRepeatEvent ? 1 : 0
          addAdvertiserTag(this.ruleForm)
            .then(response => {
              this.$message({
                message: '新增成功',
                type: 'success'
              })
              this.$emit('update:isUpdate', true)
              this.closeMe()
            })
            .catch(() => {
              this.btnClicked = false
            })
          return true
        } else {
          return false
        }
      })
    },
    loadAccount(name) {
      listAccountApi({ accountId: name }).then(response => {
        this.accountList = response.result
      })
    },
    getRealName() {
      if (this.ruleForm.advertiserId && this.ruleForm.advertiserAppId && this.ruleForm.type && this.ruleForm.settlementType && this.ruleForm.name) {
        const adv = this.advertisers.find(item => item.id === this.ruleForm.advertiserId)
        const app = this.advertiserApps.find(item => item.id === this.ruleForm.advertiserAppId)
        this.realTagName =
          adv.name +
          '-' +
          app.name +
          '-' +
          this.parseOsType(app.type) +
          '-' +
          this.parseTagType(this.ruleForm.type) +
          '-' +
          this.ruleForm.name +
          '-' +
          this.parseBidType(this.ruleForm.settlementType)
      } else {
        this.realTagName = ''
      }
    },
    loadDmpPackage(name) {
      listDmpPackApi({ name: name }).then(response => {
        this.dmpPackList = response.result
      })
    }
  }
}
</script>

<style scoped>
.ruleForm {
  margin-left: 2%;
  margin-right: 2%;
}
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
</style>
