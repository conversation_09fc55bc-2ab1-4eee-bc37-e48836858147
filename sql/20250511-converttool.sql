CREATE TABLE `conversion_tools` (
        `id` bigint NOT NULL AUTO_INCREMENT,
        `name` varchar(64) NOT NULL COMMENT '名称',
        `url` text NOT NULL COMMENT '原始链接',
        `convert_url` text DEFAULT NULL COMMENT '转换后链接',
        `type` tinyint NOT NULL COMMENT '转链类型：1:芭芭农场',
        `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态 0-待处理，1-处理中，2-成功，3-失败',
        `user_id` bigint DEFAULT NULL COMMENT '创建用户',
        `create_time` datetime NOT NULL COMMENT '创建时间',
        `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
        `description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
        `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '结果',
        PRIMARY KEY (`id`) USING BTREE,
        KEY `convt_t_name` (`name`),
        KEY `convt_t_type` (`type`),
        KEY `convt_t_status` (`status`),
        KEY `convt_t_ctime` (`create_time`),
        KEY `convt_t_lutime` (`last_update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='转链';


INSERT INTO `oper_auth_resource` (`id`, `resource_code`, `resource_name`, `create_time`, `operator_id`, `last_update_time`) VALUES
(94, 'TOOL_CONVERSION_VIEW', 'vc', now(), 1, NOW()),
(95, 'TOOL_CONVERSION_ADD', 'vc', now(), 1, NOW()),
(96, 'TOOL_CONVERSION_MODIFY', 'vc', now(), 1, NOW()),
(97, 'TOOL_CONVERSION_IMPORT', 'vc', now(), 1, NOW()),
(98, 'TOOL_CONVERSION_EXPORT', 'vc', now(), 1, NOW());

INSERT INTO `oper_auth_role_resource_assign` (`resource_id`, `role_id`, `create_time`, `operator_id`, `last_update_time`) VALUES
(94, 1, now(), 1, now()),
(95, 1, now(), 1, now()),
(96, 1, now(), 1, now()),
(97, 1, now(), 1, now()),
(98, 1, now(), 1, now());

ALTER TABLE `ssp`.`dsp_advertiser_ad`
    ADD COLUMN `ad_type` int(11) NULL DEFAULT 1 COMMENT '创意类型-广告位类型' AFTER `status`,
    ADD COLUMN `max_day_amount` decimal(16, 8) NULL COMMENT '日限额' AFTER `ad_type`;

alter table conversion_tools ADD COLUMN ios_convert_url text DEFAULT NULL comment 'ios转换后链接';