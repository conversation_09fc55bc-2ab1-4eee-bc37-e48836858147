doc.sh

rm -rf /opt/ssp/web/doc/*
cd /opt/ssp/web/doc
wget -r -nH -m --cut-dir=1 ftp://********:21/ssp/ssp-doc/
cd /opt/ssp/web/doc/ssp-doc
rm -rf .git
rm -rf .gitignore


web-oper.sh

rm -rf /opt/ssp/web/oper/*
cd /opt/ssp/web/oper
wget -r -nH -m --cut-dir=1 ftp://********:21/ssp/ssp-web-oper/dist/


web-client.sh

rm -rf /opt/ssp/web/client/*
cd /opt/ssp/web/client
wget -r -nH -m --cut-dir=1 ftp://********:21/ssp/ssp-web-client/dist/


web-service.sh

current_time=$(date "+%Y%m%d%H%M%S")

cd /opt/ssp/app/
sh /opt/ssp/app/bin/stop.sh

mkdir -p /opt/ssp/backup/${current_time}
cp -r /opt/ssp/app/* /opt/ssp/backup/${current_time}/
rm -rf /opt/ssp/app/*

curl -O ftp://********:21/ssp/ssp-web-service/target/ssp-web-service.zip 
unzip ssp-web-service.zip 
rm -rf ssp-web-service.zip 
sh /opt/ssp/app/bin/start.sh


business-service.sh

current_time=$(date "+%Y%m%d%H%M%S")

cd /opt/ssp/app/
sh /opt/ssp/app/bin/stop.sh

mkdir -p /opt/ssp/backup/${current_time}
cp -r /opt/ssp/app/* /opt/ssp/backup/${current_time}/
rm -rf /opt/ssp/app/*

curl -O ftp://********:21/ssp/ssp-business-service/target/ssp-business-service.zip 
unzip ssp-business-service.zip 
rm -rf ssp-business-service.zip 
sh /opt/ssp/app/bin/start.sh


rtb-service.sh

current_time=$(date "+%Y%m%d%H%M%S")

cd /opt/ssp/app/
sh /opt/ssp/app/bin/stop.sh

mkdir -p /opt/ssp/backup/${current_time}
cp -r /opt/ssp/app/* /opt/ssp/backup/${current_time}/
rm -rf /opt/ssp/app/*

curl -O ftp://********:21/ssp/ssp-rtb-service/target/ssp-rtb-service.zip 
unzip ssp-rtb-service.zip 
rm -rf ssp-rtb-service.zip 
sh /opt/ssp/app/bin/start.sh
